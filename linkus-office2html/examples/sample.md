# 示例文档

这是一个用于测试Office2HtmlServer转换功能的示例Markdown文档。

## 功能特性

- **Word文档转换**: 支持.docx格式
- **Excel表格转换**: 支持.xlsx和.xls格式
- **PowerPoint转换**: 支持.pptx和.ppt格式
- **Markdown转换**: 支持.md和.markdown格式

## 代码示例

```python
from app.services.conversion import conversion_service

# 转换文档
job = await conversion_service.convert_document(
    file=upload_file,
    format="html"
)
```

## 表格示例

| 格式 | 输入扩展名 | 输出格式 |
|------|------------|----------|
| Word | .docx | HTML |
| Excel | .xlsx, .xls | HTML |
| PowerPoint | .pptx, .ppt | HTML |
| Markdown | .md, .markdown | HTML |

## 列表示例

### 有序列表
1. 安装依赖
2. 配置环境
3. 启动服务
4. 测试功能

### 无序列表
- FastAPI框架
- 异步处理
- 模块化设计
- 易于扩展

## 引用示例

> 这是一个引用块，用于展示引用功能。

## 链接示例

- [FastAPI官网](https://fastapi.tiangolo.com/)
- [Python官网](https://www.python.org/)

## 图片示例

![示例图片](https://via.placeholder.com/300x200?text=示例图片)

---

*文档结束* 