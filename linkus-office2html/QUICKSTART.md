# 快速启动指南

## 🚀 5分钟快速开始

### 方法一：快速安装（推荐）

```bash
# 运行快速安装脚本
python quick_install.py
```

### 方法二：自动安装

```bash
# 运行自动安装脚本
python install.py
```

### 方法三：手动安装

```bash
# 安装核心依赖
pip install fastapi uvicorn python-docx openpyxl python-pptx markdown pydantic loguru aiofiles beautifulsoup4

# 或使用简单requirements
pip install -r requirements-simple.txt
```

### 2. 创建目录和配置

```bash
# 创建必要目录
mkdir uploads outputs temp logs

# 复制配置文件
copy env.example .env
```

### 3. 启动服务

```bash
# 启动服务
python run.py
```

### 4. 访问服务

打开浏览器访问：
- 🌐 主页: http://localhost:8000
- 📚 API文档: http://localhost:8000/docs

### 5. 测试转换

1. 访问 http://localhost:8000/docs
2. 点击 "POST /api/v1/convert"
3. 点击 "Try it out"
4. 上传 `examples/sample.md` 文件
5. 点击 "Execute"

## 🔧 安装选项

### 如果遇到依赖问题，尝试以下方案：

#### 方案1：使用简单requirements
```bash
pip install -r requirements-simple.txt
```

#### 方案2：逐个安装核心包
```bash
pip install fastapi
pip install uvicorn
pip install python-docx
pip install openpyxl
pip install python-pptx
pip install markdown
pip install pydantic
pip install loguru
pip install aiofiles
pip install beautifulsoup4
```

#### 方案3：使用用户安装
```bash
python -m pip install --user fastapi uvicorn python-docx openpyxl python-pptx markdown
```

#### 方案4：使用conda（如果有conda）
```bash
conda install -c conda-forge fastapi uvicorn python-docx openpyxl python-pptx markdown
```

## 🧪 验证安装

运行测试脚本验证安装：

```bash
python test_installation.py
```

## 📋 常用命令

```bash
# 启动服务
python run.py

# 快速安装
python quick_install.py

# 自动安装
python install.py

# 查看日志
tail -f logs/app.log

# 清理旧任务
curl -X POST http://localhost:8000/api/v1/cleanup
```

## 🆘 常见问题

### 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用用户安装
python -m pip install --user fastapi uvicorn

# 或者使用快速安装脚本
python quick_install.py
```

### 端口被占用
修改 `.env` 文件中的端口：
```
PORT=8001
```

### 权限问题
```bash
# Windows
mkdir uploads outputs temp logs

# Linux/Mac
chmod 755 uploads outputs temp logs
```

### Python版本问题
```bash
# 检查Python版本
python --version
# 需要Python 3.8+
```

## 📞 获取帮助

- 查看完整文档: README.md
- 运行快速安装: `python quick_install.py`
- 查看日志: `tail -f logs/app.log` 