# Office2HtmlServer - 文档转换服务

一个基于FastAPI的现代化文档转换服务，支持将Office文档转换为HTML格式。

## 功能特性

- 🚀 基于FastAPI的高性能异步服务
- 📄 支持多种文档格式转换：
  - Word文档 (.docx) → HTML
  - Excel表格 (.xlsx/.xls) → HTML
  - PowerPoint演示文稿 (.pptx/.ppt) → HTML
  - Markdown文件 (.md/.markdown) → HTML
- 🎨 可自定义的HTML模板
- 📊 详细的转换日志和错误处理
- 🔧 模块化架构，易于扩展
- 🛡️ 输入验证和安全处理

## 项目结构

```
Office2HtmlServer/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py              # 配置管理
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes.py          # API路由
│   │   └── models.py          # 请求/响应模型
│   ├── core/
│   │   ├── __init__.py
│   │   ├── converters/        # 转换器模块
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 基础转换器
│   │   │   ├── docx.py        # Word文档转换器
│   │   │   ├── xlsx.py        # Excel转换器
│   │   │   ├── pptx.py        # PowerPoint转换器
│   │   │   └── markdown.py    # Markdown转换器
│   │   └── utils.py           # 工具函数
│   └── services/
│       ├── __init__.py
│       └── conversion.py      # 转换服务
├── tests/                     # 测试文件
├── logs/                      # 日志文件
├── uploads/                   # 上传文件临时目录
├── outputs/                   # 输出文件目录
├── requirements.txt           # 项目依赖
├── env.example               # 环境变量示例
├── install.py                # 自动安装脚本
├── run.py                    # 启动脚本
└── README.md                 # 项目说明
```

## 系统要求

- Python 3.8 或更高版本
- pip 包管理器
- 至少 100MB 可用磁盘空间

## 快速开始

### 方法一：自动安装（推荐）

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd Office2HtmlServer

# 2. 运行自动安装脚本
python install.py
```

### 方法二：手动安装

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd Office2HtmlServer

# 2. 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 创建必要的目录
mkdir uploads outputs temp logs

# 5. 配置环境变量
cp env.example .env
# 编辑 .env 文件（可选）
```

### 3. 启动服务

```bash
# 使用启动脚本
python run.py

# 或直接使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问服务

- 🌐 **主页**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs
- 🔄 **健康检查**: http://localhost:8000/health
- 📊 **统计信息**: http://localhost:8000/api/v1/stats

## API使用

### 转换文档

```bash
curl -X POST "http://localhost:8000/api/v1/convert" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.docx" \
     -F "format=html"
```

### 获取任务状态

```bash
curl "http://localhost:8000/api/v1/jobs/{job_id}"
```

### 获取统计信息

```bash
curl "http://localhost:8000/api/v1/stats"
```

## 配置说明

### 环境变量

复制 `env.example` 为 `.env` 并修改配置：

```bash
# 服务配置
APP_NAME=Office2HtmlServer
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 文件路径配置
UPLOAD_DIR=uploads
OUTPUT_DIR=outputs
TEMP_DIR=temp

# 文件大小限制 (MB)
MAX_FILE_SIZE=50

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 重新安装依赖
   pip install -r requirements.txt
   ```

2. **端口被占用**
   ```bash
   # 修改.env文件中的端口
   PORT=8001
   ```

3. **权限问题**
   ```bash
   # 确保有写入权限
   chmod 755 uploads outputs temp logs
   ```

4. **Python版本问题**
   ```bash
   # 检查Python版本
   python --version
   # 需要Python 3.8+
   ```

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 开发指南

### 添加新的转换器

1. 在 `app/core/converters/` 目录下创建新的转换器类
2. 继承 `BaseConverter` 类
3. 实现 `convert()` 方法
4. 在 `ConversionService` 中注册新的转换器

### 自定义HTML模板

1. 在 `app/core/templates/` 目录下创建新的模板文件
2. 使用Jinja2语法编写模板
3. 在转换器中指定模板路径

### 运行测试

```bash
# 安装测试依赖
pip install pytest

# 运行测试
pytest tests/
```

## 部署

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN mkdir -p uploads outputs temp logs

EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境配置

1. 设置 `DEBUG=false`
2. 配置反向代理（Nginx）
3. 设置日志轮转
4. 配置监控和告警

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Word、Excel、PowerPoint、Markdown转换
- 完整的API文档
- 异步处理支持 