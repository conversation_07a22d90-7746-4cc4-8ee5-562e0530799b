#!/usr/bin/env python3
"""
Office2HtmlServer 启动脚本
"""
import os
import sys
from pathlib import Path

# 获取脚本所在目录作为项目根目录
script_dir = Path(__file__).parent.absolute()
project_root = script_dir

# 添加项目根目录到Python路径
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 确保当前工作目录也在Python路径中
current_dir = os.getcwd()
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 打印调试信息
print(f"🔍 脚本目录: {script_dir}")
print(f"🔍 项目根目录: {project_root}")
print(f"🔍 当前工作目录: {current_dir}")
print(f"🔍 Python路径:")
for i, path in enumerate(sys.path[:5]):  # 只显示前5个路径
    print(f"  {i}: {path}")
print(f"  ... (共{len(sys.path)}个路径)")

# 检查app目录是否存在
app_dir = project_root / "app"
print(f"🔍 app目录是否存在: {app_dir.exists()}")
if app_dir.exists():
    print(f"🔍 app目录内容: {list(app_dir.iterdir())}")

try:
    import uvicorn
    print("✅ uvicorn导入成功")
    
    # 尝试导入app模块
    from app.config import settings
    print("✅ app.config导入成功")
    
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print(f"🔍 尝试直接导入...")
    
    # 如果直接导入失败，尝试使用绝对路径
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", str(app_dir / "config.py"))
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        settings = config_module.settings
        print("✅ 使用绝对路径导入成功")
    except Exception as e2:
        print(f"❌ 绝对路径导入也失败: {e2}")
        sys.exit(1)


def main():
    """主函数"""
    print("🚀 启动Office2HtmlServer...")
    print(f"📁 项目根目录: {project_root}")
    print(f"🌐 监听地址: {settings.host}:{settings.port}")
    print(f"🔧 调试模式: {'开启' if settings.debug else '关闭'}")
    print(f"📝 日志级别: {settings.log_level}")
    print("-" * 50)
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main() 