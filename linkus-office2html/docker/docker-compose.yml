version: '3.8'

services:
  office2html:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: office2html-server
    ports:
      - "8080:8080"
    environment:
      - APP_NAME=Office2HtmlServer
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8080
      - UPLOAD_DIR=uploads
      - OUTPUT_DIR=outputs
      - TEMP_DIR=temp
      - MAX_FILE_SIZE=50
      - LOG_LEVEL=INFO
      - LOG_FILE=logs/app.log
      - SECRET_KEY=your-secret-key-here-change-in-production
      - ALLOWED_EXTENSIONS=docx,xlsx,pptx,md
      - DEFAULT_TEMPLATE=base.html
      - ENABLE_CACHE=true
      - CACHE_TTL=3600
    volumes:
      - ../uploads:/app/uploads
      - ../outputs:/app/outputs
      - ../temp:/app/temp
      - ../logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: office2html-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - office2html
    restart: unless-stopped
    profiles:
      - nginx 