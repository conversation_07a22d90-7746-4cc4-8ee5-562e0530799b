# Docker 部署指南 (CentOS 7版本)

本文档介绍如何使用 Docker 部署 Office2HtmlServer。

**注意：Docker 相关文件已移至 `docker/` 目录，请在该目录下执行相关命令。**
**基础镜像：CentOS 7**

## 📋 目录

- [快速开始](#快速开始)
- [生产环境部署](#生产环境部署)
- [开发环境部署](#开发环境部署)
- [使用 Nginx 反向代理](#使用-nginx-反向代理)
- [Docker 命令参考](#docker-命令参考)
- [故障排除](#故障排除)

## 🚀 快速开始

### 1. 进入 Docker 目录

```bash
cd docker
```

### 2. 构建镜像

```bash
# 构建生产环境镜像 (CentOS 7)
docker build -f Dockerfile -t office2html:latest ..

# 构建开发环境镜像 (CentOS 7)
docker build -f Dockerfile.dev -t office2html:dev ..

# 或使用构建脚本（推荐）
./build-docker.sh prod    # 构建生产环境
./build-docker.sh dev     # 构建开发环境
./build-docker.sh all     # 构建所有环境
```

### 3. 运行容器

```bash
# 使用 docker-compose（推荐）
docker-compose up -d

# 或直接使用 docker 命令
docker run -d \
  --name office2html-server \
  -p 8080:8080 \
  -v ../uploads:/app/uploads \
  -v ../outputs:/app/outputs \
  -v ../temp:/app/temp \
  -v ../logs:/app/logs \
  office2html:latest
```

### 4. 验证部署

```bash
# 检查容器状态
docker ps

# 查看日志
docker logs office2html-server

# 测试健康检查
curl http://localhost:8080/health
```

## 🏭 生产环境部署

### 使用 docker-compose

1. **进入 docker 目录并创建环境配置文件**

```bash
cd docker
cp ../env.example ../.env
# 编辑 ../.env 文件，设置生产环境配置
```

2. **启动服务**

```bash
# 后台运行
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f office2html
```

3. **停止服务**

```bash
docker-compose down
```

### 使用 Nginx 反向代理

```bash
# 启动包含 Nginx 的完整服务栈
docker-compose --profile nginx up -d
```

现在可以通过 `http://localhost` 访问服务（Nginx 代理到 8080 端口）。

## 🛠️ 开发环境部署

### 使用开发环境配置

```bash
# 进入 docker 目录
cd docker

# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 进入容器进行开发
docker exec -it office2html-dev bash

# 运行测试
docker-compose -f docker-compose.dev.yml --profile test up test
```

### 开发环境特性

- ✅ 代码热重载
- ✅ 调试模式启用
- ✅ 开发工具（pytest, black, flake8, mypy）
- ✅ 实时日志输出
- ✅ 卷挂载（代码同步）
- ✅ CentOS 7 环境

## 📊 监控和管理

### 查看容器状态

```bash
# 查看运行中的容器
docker ps

# 查看所有容器（包括停止的）
docker ps -a

# 查看容器资源使用情况
docker stats office2html-server
```

### 日志管理

```bash
# 查看实时日志
docker logs -f office2html-server

# 查看最近的日志
docker logs --tail 100 office2html-server

# 导出日志
docker logs office2html-server > app.log
```

### 健康检查

```bash
# 手动检查健康状态
curl http://localhost:8080/health

# 查看健康检查历史
docker inspect office2html-server | grep -A 10 "Health"
```

## 🔧 Docker 命令参考

### 镜像管理

```bash
# 列出镜像
docker images

# 删除镜像
docker rmi office2html:latest

# 清理未使用的镜像
docker image prune
```

### 容器管理

```bash
# 启动容器
docker start office2html-server

# 停止容器
docker stop office2html-server

# 重启容器
docker restart office2html-server

# 删除容器
docker rm office2html-server

# 进入容器
docker exec -it office2html-server bash
```

### 数据管理

```bash
# 备份数据
docker cp office2html-server:/app/uploads ../backup/uploads
docker cp office2html-server:/app/outputs ../backup/outputs

# 恢复数据
docker cp ../backup/uploads office2html-server:/app/
docker cp ../backup/outputs office2html-server:/app/
```

## 🚨 故障排除

### 常见问题

1. **路径错误**
   ```bash
   # 确保在 docker/ 目录下执行命令
   cd docker
   docker-compose up -d
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep 8080
   
   # 修改端口映射
   docker run -p 8081:8080 office2html:latest
   ```

3. **权限问题**
   ```bash
   # 修复目录权限
   sudo chown -R 1000:1000 ../uploads ../outputs ../temp ../logs
   ```

4. **内存不足**
   ```bash
   # 增加 Docker 内存限制
   docker run --memory=2g office2html:latest
   ```

5. **网络问题**
   ```bash
   # 检查网络连接
   docker network ls
   docker network inspect bridge
   ```

6. **CentOS 特定问题**
   ```bash
   # 如果遇到 yum 相关问题，可以进入容器检查
   docker exec -it office2html-server bash
   yum repolist
   ```

### 调试技巧

```bash
# 查看容器详细信息
docker inspect office2html-server

# 查看容器进程
docker top office2html-server

# 查看容器文件系统
docker exec office2html-server ls -la /app

# 查看环境变量
docker exec office2html-server env

# 检查 CentOS 版本
docker exec office2html-server cat /etc/redhat-release
```

### 性能优化

1. **多阶段构建**
   - 使用 `Dockerfile` 进行生产构建
   - 使用 `Dockerfile.dev` 进行开发构建

2. **镜像优化**
   ```bash
   # 使用 .dockerignore 减少构建上下文
   # 合并 RUN 命令减少层数
   # 使用 CentOS 7 基础镜像
   ```

3. **资源限制**
   ```bash
   docker run \
     --memory=1g \
     --cpus=1.0 \
     office2html:latest
   ```

## 📝 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `APP_NAME` | Office2HtmlServer | 应用名称 |
| `DEBUG` | false | 调试模式 |
| `HOST` | 0.0.0.0 | 监听地址 |
| `PORT` | 8080 | 监听端口 |
| `UPLOAD_DIR` | uploads | 上传目录 |
| `OUTPUT_DIR` | outputs | 输出目录 |
| `TEMP_DIR` | temp | 临时目录 |
| `MAX_FILE_SIZE` | 50 | 最大文件大小(MB) |
| `LOG_LEVEL` | INFO | 日志级别 |
| `SECRET_KEY` | your-secret-key | 密钥 |
| `ALLOWED_EXTENSIONS` | docx,xlsx,pptx,md | 允许的文件类型 |

## 🔒 安全建议

1. **更改默认密钥**
   ```bash
   # 在 ../.env 文件中设置强密钥
   SECRET_KEY=your-very-secure-secret-key-here
   ```

2. **限制资源使用**
   ```bash
   docker run \
     --memory=1g \
     --cpus=1.0 \
     --security-opt=no-new-privileges \
     office2html:latest
   ```

3. **使用非 root 用户**
   ```dockerfile
   # 在 Dockerfile 中添加
   RUN adduser --disabled-password --gecos '' appuser
   USER appuser
   ```

4. **定期更新镜像**
   ```bash
   # 拉取最新基础镜像
   docker pull centos:7
   
   # 重新构建
   cd docker
   docker build -f Dockerfile --no-cache -t office2html:latest ..
   ```

## 📁 目录结构

```
project-root/
├── app/                    # 应用代码
├── uploads/               # 上传文件目录
├── outputs/               # 输出文件目录
├── temp/                  # 临时文件目录
├── logs/                  # 日志文件目录
├── requirements.txt       # Python 依赖
├── run.py                 # 启动脚本
├── env.example           # 环境变量示例
└── docker/               # Docker 相关文件
    ├── Dockerfile        # 生产环境 Dockerfile (CentOS 7)
    ├── Dockerfile.dev    # 开发环境 Dockerfile (CentOS 7)
    ├── docker-compose.yml # 生产环境 compose
    ├── docker-compose.dev.yml # 开发环境 compose
    ├── nginx.conf        # Nginx 配置
    ├── build-docker.sh   # 构建脚本
    ├── .dockerignore     # Docker 忽略文件
    └── DOCKER.md         # 本文档
```

## 🐧 CentOS 7 特性

- **基础镜像**: `centos:7`
- **包管理器**: `yum`
- **Python 版本**: Python 3.8 (通过 EPEL)
- **系统工具**: 使用 `yum` 安装系统依赖
- **清理命令**: `yum clean all && rm -rf /var/cache/yum`

## 📞 支持

如果遇到问题，请：

1. 查看容器日志：`docker logs office2html-server`
2. 检查健康状态：`curl http://localhost:8080/health`
3. 查看项目文档：`../README.md`
4. 提交 Issue 到项目仓库 