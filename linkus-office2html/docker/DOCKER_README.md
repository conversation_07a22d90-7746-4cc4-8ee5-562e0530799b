# Docker 快速使用指南 (CentOS 7版本)

Docker 相关文件已移至 `docker/` 目录。
**基础镜像：CentOS 7**

## 🚀 快速开始

### 1. 进入 Docker 目录
```bash
cd docker
```

### 2. 构建镜像
```bash
# 使用构建脚本（推荐）
./build-docker.sh all

# 或手动构建
docker build -f Dockerfile -t office2html:latest ..
docker build -f Dockerfile.dev -t office2html:dev ..
```

### 3. 启动服务
```bash
# 生产环境
docker-compose up -d

# 开发环境
docker-compose -f docker-compose.dev.yml up -d
```

### 4. 访问服务
- 生产环境：http://localhost:8080
- 开发环境：http://localhost:8080
- 带 Nginx：http://localhost (使用 `--profile nginx`)

## 📋 常用命令

```bash
# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 进入容器
docker exec -it office2html-server bash

# 检查CentOS版本
docker exec office2html-server cat /etc/redhat-release
```

## 🐧 CentOS 7 特性

- **基础镜像**: `centos:7`
- **包管理器**: `yum`
- **Python 版本**: Python 3.8 (通过 EPEL)
- **系统工具**: 使用 `yum` 安装系统依赖

## 📖 详细文档

查看 `docker/DOCKER.md` 获取完整的使用指南。

## 🔧 故障排除

1. **确保在 docker/ 目录下执行命令**
2. **检查端口 8080 是否被占用**
3. **确保 Docker 服务正在运行**
4. **查看容器日志：`docker logs office2html-server`**
5. **如果遇到yum问题，检查网络连接** 