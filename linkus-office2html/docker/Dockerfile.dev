# 开发环境Dockerfile - CentOS版本
FROM centos:7

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DEBUG=true

# 配置yum镜像源和安装Python 3.8
RUN sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*.repo && \
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*.repo && \
    yum update -y && \
    yum install -y epel-release && \
    yum groupinstall -y "Development Tools" && \
    yum install -y \
    python3 \
    python3-pip \
    python3-devel \
    libxml2-devel \
    libxslt-devel \
    libffi-devel \
    openssl-devel \
    curl \
    vim \
    && yum clean all && \
    rm -rf /var/cache/yum

# 升级pip
RUN python3 -m pip install --upgrade pip

# 复制依赖文件（从上级目录）
COPY ../requirements.txt .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 安装开发工具
RUN pip3 install --no-cache-dir \
    pytest \
    pytest-asyncio \
    black \
    flake8 \
    mypy

# 创建必要的目录
RUN mkdir -p uploads outputs temp logs

# 暴露端口
EXPOSE 8080

# 开发环境启动命令（启用热重载）
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"] 