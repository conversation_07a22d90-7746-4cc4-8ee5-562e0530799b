# 使用CentOS 7基础镜像
FROM centos:7

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 配置yum镜像源和安装Python 3.8
RUN sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*.repo && \
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*.repo && \
    yum update -y && \
    yum install -y epel-release && \
    yum groupinstall -y "Development Tools" && \
    yum install -y \
    python3 \
    python3-pip \
    python3-devel \
    libxml2-devel \
    libxslt-devel \
    libffi-devel \
    openssl-devel \
    curl \
    && yum clean all && \
    rm -rf /var/cache/yum

# 升级pip
RUN python3 -m pip install --upgrade pip

# 复制依赖文件（从上级目录）
COPY ../requirements.txt .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 复制应用代码（从上级目录）
COPY .. .

# 创建必要的目录
RUN mkdir -p uploads outputs temp logs

# 设置权限
RUN chmod +x run.py

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["python3", "run.py"] 