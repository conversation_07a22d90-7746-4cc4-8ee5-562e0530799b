#!/bin/bash

# Office2HtmlServer Docker 构建脚本 (多版本支持)
# 使用方法: ./build-docker.sh [dev|prod|all|alpine|ubuntu]
# 注意：此脚本需要在 docker/ 目录下运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Office2HtmlServer Docker 构建${NC}"
    echo -e "${BLUE}      多版本支持 (CentOS/Alpine/Ubuntu)${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "Dockerfile" ] || [ ! -f "docker-compose.yml" ]; then
        print_error "请在 docker/ 目录下运行此脚本"
        exit 1
    fi
    print_message "✓ 目录检查通过"
}

# 构建生产环境镜像 (CentOS)
build_prod() {
    print_message "构建生产环境镜像 (CentOS 7)..."
    docker build -f Dockerfile -t office2html:latest -t office2html:$(date +%Y%m%d) ..
    print_message "生产环境镜像构建完成"
}

# 构建开发环境镜像 (CentOS)
build_dev() {
    print_message "构建开发环境镜像 (CentOS 7)..."
    docker build -f Dockerfile.dev -t office2html:dev -t office2html:dev-$(date +%Y%m%d) ..
    print_message "开发环境镜像构建完成"
}

# 构建Alpine版本镜像
build_alpine() {
    print_message "构建Alpine版本镜像..."
    docker build -f Dockerfile.alpine -t office2html:alpine -t office2html:alpine-$(date +%Y%m%d) ..
    docker build -f Dockerfile.dev.alpine -t office2html:alpine-dev -t office2html:alpine-dev-$(date +%Y%m%d) ..
    print_message "Alpine版本镜像构建完成"
}

# 构建Ubuntu版本镜像
build_ubuntu() {
    print_message "构建Ubuntu版本镜像..."
    docker build -f Dockerfile.ubuntu -t office2html:ubuntu -t office2html:ubuntu-$(date +%Y%m%d) ..
    docker build -f Dockerfile.dev.ubuntu -t office2html:ubuntu-dev -t office2html:ubuntu-dev-$(date +%Y%m%d) ..
    print_message "Ubuntu版本镜像构建完成"
}

# 构建所有镜像
build_all() {
    build_prod
    build_dev
}

# 清理旧镜像
cleanup() {
    print_warning "清理未使用的 Docker 镜像..."
    docker image prune -f
    print_message "清理完成"
}

# 显示镜像信息
show_images() {
    print_message "当前镜像列表:"
    docker images | grep office2html || print_warning "未找到 office2html 镜像"
}

# 故障排除
troubleshoot() {
    print_message "故障排除模式..."
    echo ""
    print_warning "如果CentOS构建失败，请尝试："
    echo "1. 使用Alpine版本: ./build-docker.sh alpine"
    echo "2. 使用Ubuntu版本: ./build-docker.sh ubuntu"
    echo "3. 检查网络连接"
    echo "4. 使用代理设置"
    echo ""
    print_message "版本特点对比："
    echo "CentOS 7: 稳定，兼容性好，镜像较大"
    echo "Alpine: 小巧，快速，网络兼容性好"
    echo "Ubuntu: 通用，稳定，包管理简单"
}

# 测试构建
test_build() {
    print_message "测试构建模式..."
    echo ""
    print_message "测试不同版本的构建："
    
    # 测试Alpine
    print_message "测试Alpine版本..."
    if docker build -f Dockerfile.alpine -t test-alpine .. > /dev/null 2>&1; then
        print_message "✓ Alpine版本构建成功"
        docker rmi test-alpine > /dev/null 2>&1
    else
        print_warning "⚠ Alpine版本构建失败"
    fi
    
    # 测试Ubuntu
    print_message "测试Ubuntu版本..."
    if docker build -f Dockerfile.ubuntu -t test-ubuntu .. > /dev/null 2>&1; then
        print_message "✓ Ubuntu版本构建成功"
        docker rmi test-ubuntu > /dev/null 2>&1
    else
        print_warning "⚠ Ubuntu版本构建失败"
    fi
    
    # 测试CentOS
    print_message "测试CentOS版本..."
    if docker build -f Dockerfile -t test-centos .. > /dev/null 2>&1; then
        print_message "✓ CentOS版本构建成功"
        docker rmi test-centos > /dev/null 2>&1
    else
        print_warning "⚠ CentOS版本构建失败"
    fi
}

# 主函数
main() {
    print_header
    
    # 检查目录
    check_directory
    
    # 检查 Docker
    check_docker
    
    # 解析参数
    case "${1:-all}" in
        "prod")
            build_prod
            ;;
        "dev")
            build_dev
            ;;
        "all")
            build_all
            ;;
        "alpine")
            build_alpine
            ;;
        "ubuntu")
            build_ubuntu
            ;;
        "clean")
            cleanup
            ;;
        "list")
            show_images
            ;;
        "help")
            troubleshoot
            ;;
        "test")
            test_build
            ;;
        *)
            print_error "无效参数: $1"
            echo "使用方法: $0 [dev|prod|all|alpine|ubuntu|clean|list|help|test]"
            echo "  dev     - 构建开发环境镜像 (CentOS 7)"
            echo "  prod    - 构建生产环境镜像 (CentOS 7)"
            echo "  all     - 构建所有镜像 (默认)"
            echo "  alpine  - 构建Alpine版本镜像"
            echo "  ubuntu  - 构建Ubuntu版本镜像"
            echo "  clean   - 清理未使用的镜像"
            echo "  list    - 显示当前镜像"
            echo "  help    - 故障排除帮助"
            echo "  test    - 测试所有版本构建"
            echo ""
            echo "注意：此脚本需要在 docker/ 目录下运行"
            echo "支持的基础镜像：CentOS 7, Alpine 3.18, Ubuntu 20.04"
            exit 1
            ;;
    esac
    
    # 显示构建结果
    show_images
    
    print_message "构建完成！"
    echo ""
    print_message "下一步操作："
    echo "  1. 启动生产环境: docker-compose up -d"
    echo "  2. 启动开发环境: docker-compose -f docker-compose.dev.yml up -d"
    echo "  3. 查看日志: docker-compose logs -f"
    echo ""
    print_warning "注意：请在 docker/ 目录下运行 docker-compose 命令"
    print_message "支持的基础镜像：CentOS 7, Alpine 3.18, Ubuntu 20.04"
}

# 执行主函数
main "$@" 