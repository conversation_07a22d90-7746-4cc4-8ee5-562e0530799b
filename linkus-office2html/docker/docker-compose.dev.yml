version: '3.8'

services:
  office2html-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: office2html-dev
    ports:
      - "8080:8080"
    environment:
      - APP_NAME=Office2HtmlServer-Dev
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8080
      - UPLOAD_DIR=uploads
      - OUTPUT_DIR=outputs
      - TEMP_DIR=temp
      - MAX_FILE_SIZE=50
      - LOG_LEVEL=DEBUG
      - LOG_FILE=logs/app.log
      - SECRET_KEY=dev-secret-key
      - ALLOWED_EXTENSIONS=docx,xlsx,pptx,md
      - DEFAULT_TEMPLATE=base.html
      - ENABLE_CACHE=false
      - CACHE_TTL=3600
    volumes:
      - ..:/app
      - ../uploads:/app/uploads
      - ../outputs:/app/outputs
      - ../temp:/app/temp
      - ../logs:/app/logs
    restart: unless-stopped
    stdin_open: true
    tty: true

  # 开发环境测试服务
  test:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: office2html-test
    command: ["pytest", "tests/", "-v"]
    volumes:
      - ..:/app
    depends_on:
      - office2html-dev
    profiles:
      - test 