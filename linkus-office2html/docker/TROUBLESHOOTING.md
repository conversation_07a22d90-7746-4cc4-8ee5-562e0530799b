# Docker 故障排除指南

## 🚨 常见问题

### 1. CentOS 镜像源问题

**错误信息：**
```
Error: Failed to download metadata for repo 'appstream': Cannot prepare internal mirrorlist
```

**解决方案：**

#### 方案A：使用修改后的Dockerfile（推荐）
```bash
# 使用已修复的Dockerfile
cd docker
./build-docker.sh prod
```

#### 方案B：使用Alpine版本
```bash
# Alpine版本更稳定
cd docker
./build-docker.sh alpine
```

#### 方案C：手动修复镜像源
```bash
# 进入容器手动修复
docker run -it centos:7 bash
sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*.repo
sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*.repo
yum update -y
```

### 2. 网络连接问题

**错误信息：**
```
Could not resolve host: mirrorlist.centos.org
```

**解决方案：**

#### 检查网络连接
```bash
# 测试网络连接
ping mirrorlist.centos.org

# 检查DNS
nslookup mirrorlist.centos.org
```

#### 使用代理（如果需要）
```bash
# 设置Docker代理
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080
docker build --build-arg HTTP_PROXY=$HTTP_PROXY --build-arg HTTPS_PROXY=$HTTPS_PROXY -f Dockerfile -t office2html:latest ..
```

### 3. 权限问题

**错误信息：**
```
Permission denied
```

**解决方案：**
```bash
# 修复目录权限
sudo chown -R $USER:$USER ../uploads ../outputs ../temp ../logs

# 或者使用sudo运行
sudo docker build -f Dockerfile -t office2html:latest ..
```

### 4. 内存不足

**错误信息：**
```
No space left on device
```

**解决方案：**
```bash
# 清理Docker缓存
docker system prune -a

# 清理未使用的镜像
docker image prune -a

# 增加Docker内存限制
docker build --memory=2g -f Dockerfile -t office2html:latest ..
```

## 🔧 构建选项

### CentOS 7 版本（默认）
```bash
# 生产环境
./build-docker.sh prod

# 开发环境
./build-docker.sh dev

# 全部构建
./build-docker.sh all
```

### Alpine 版本（备用）
```bash
# 构建Alpine版本
./build-docker.sh alpine
```

**Alpine版本优势：**
- ✅ 更小的镜像大小（约50MB vs 200MB+）
- ✅ 更快的构建速度
- ✅ 更好的网络兼容性
- ✅ 更少的依赖问题

## 🛠️ 调试技巧

### 1. 查看构建日志
```bash
# 详细构建日志
docker build --progress=plain -f Dockerfile -t office2html:latest ..

# 查看构建历史
docker history office2html:latest
```

### 2. 进入容器调试
```bash
# 进入运行中的容器
docker exec -it office2html-server bash

# 检查CentOS版本
cat /etc/redhat-release

# 检查Python版本
python3 --version

# 检查yum仓库
yum repolist
```

### 3. 测试网络连接
```bash
# 在容器内测试网络
docker run --rm centos:7 curl -I http://vault.centos.org

# 测试DNS解析
docker run --rm centos:7 nslookup vault.centos.org
```

## 📋 环境检查清单

在构建前检查：

- [ ] Docker服务正在运行
- [ ] 网络连接正常
- [ ] 磁盘空间充足
- [ ] 目录权限正确
- [ ] 在正确的目录（docker/）

## 🚀 快速修复

如果遇到问题，按以下顺序尝试：

1. **使用Alpine版本**
   ```bash
   ./build-docker.sh alpine
   ```

2. **清理并重新构建**
   ```bash
   ./build-docker.sh clean
   ./build-docker.sh all
   ```

3. **检查网络**
   ```bash
   ping google.com
   ```

4. **查看帮助**
   ```bash
   ./build-docker.sh help
   ```

## 📞 获取帮助

如果问题仍然存在：

1. 查看详细错误日志
2. 检查Docker版本：`docker --version`
3. 检查系统信息：`uname -a`
4. 提交Issue到项目仓库

## 🔄 版本对比

| 特性 | CentOS 7 | Alpine |
|------|----------|--------|
| 镜像大小 | ~200MB | ~50MB |
| 构建速度 | 较慢 | 较快 |
| 网络兼容性 | 一般 | 优秀 |
| 包管理器 | yum | apk |
| Python版本 | 3.8 | 3.11 |
| 稳定性 | 高 | 高 | 