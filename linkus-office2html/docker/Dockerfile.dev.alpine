# 开发环境Dockerfile - Alpine版本（备用方案）
FROM alpine:3.18

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DEBUG=true

# 安装Python和系统依赖
RUN apk update && \
    apk add --no-cache \
    python3 \
    python3-dev \
    py3-pip \
    gcc \
    g++ \
    libxml2-dev \
    libxslt-dev \
    libffi-dev \
    openssl-dev \
    curl \
    vim \
    && rm -rf /var/cache/apk/*

# 升级pip
RUN python3 -m pip install --upgrade pip

# 复制依赖文件（从上级目录）
COPY ../requirements.txt .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 安装开发工具
RUN pip3 install --no-cache-dir \
    pytest \
    pytest-asyncio \
    black \
    flake8 \
    mypy

# 复制应用代码（从上级目录）
COPY .. .

# 创建必要的目录
RUN mkdir -p uploads outputs temp logs

# 设置权限
RUN chmod +x run.py && \
    chmod +x app/main.py

# 暴露端口
EXPOSE 8080

# 开发环境启动命令（启用热重载）
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"] 