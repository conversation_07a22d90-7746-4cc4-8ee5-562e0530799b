#!/usr/bin/env python3
"""
测试Docker启动配置的脚本
"""
import os
import sys
from pathlib import Path

print("🧪 测试Docker启动配置...")

# 模拟Docker环境
os.environ['PYTHONPATH'] = '/app'
os.chdir('/app') if os.path.exists('/app') else None

# 获取当前目录
current_dir = os.getcwd()
print(f"📁 当前工作目录: {current_dir}")

# 检查app目录
app_dir = Path(current_dir) / "app"
print(f"🔍 app目录是否存在: {app_dir.exists()}")

if app_dir.exists():
    print(f"🔍 app目录内容: {list(app_dir.iterdir())}")
    
    # 检查main.py
    main_py = app_dir / "main.py"
    print(f"🔍 main.py是否存在: {main_py.exists()}")
    
    if main_py.exists():
        print("✅ main.py文件存在，可以启动")
        
        # 测试导入
        try:
            sys.path.insert(0, current_dir)
            from app.main import app
            print("✅ app.main模块导入成功")
            print("✅ Docker启动配置正确！")
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
    else:
        print("❌ main.py文件不存在")
else:
    print("❌ app目录不存在")

print("\n📋 启动命令建议:")
print("生产环境: python3 app/main.py")
print("开发环境: uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload") 