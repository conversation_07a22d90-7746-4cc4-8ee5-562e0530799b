#!/usr/bin/env python3
"""
下载Swagger UI静态文件到本地
"""
import os
import requests
import zipfile
from pathlib import Path

def download_swagger_ui():
    """下载Swagger UI文件到本地"""
    # 创建目录
    static_dir = Path("app/static/swagger-ui")
    static_dir.mkdir(parents=True, exist_ok=True)
    
    # Swagger UI CDN文件列表
    files = [
        {
            "url": "https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui.css",
            "local_path": "swagger-ui.css"
        },
        {
            "url": "https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-bundle.js",
            "local_path": "swagger-ui-bundle.js"
        },
        {
            "url": "https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-standalone-preset.js",
            "local_path": "swagger-ui-standalone-preset.js"
        }
    ]
    
    print("开始下载Swagger UI文件...")
    
    for file_info in files:
        url = file_info["url"]
        local_path = static_dir / file_info["local_path"]
        
        print(f"下载: {url}")
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 已保存到: {local_path}")
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return False
    
    # 更新HTML模板使用本地文件
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>API文档 - Office2Html服务</title>
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="./swagger-ui-bundle.js"></script>
    <script src="./swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // 获取当前页面的API文档URL
            const apiUrl = window.location.origin + '/openapi.json';
            
            const ui = SwaggerUIBundle({
                url: apiUrl,
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    return request;
                },
                responseInterceptor: function(response) {
                    return response;
                }
            });
        };
    </script>
</body>
</html>'''
    
    with open(static_dir / "index.html", 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ HTML模板已更新为使用本地文件")
    print("✅ Swagger UI文件下载完成！")
    return True

if __name__ == "__main__":
    download_swagger_ui() 