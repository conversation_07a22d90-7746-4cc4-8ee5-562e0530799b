# Spring Cloud Gateway 访问日志系统

<div align="center">

![Spring Cloud Gateway](https://img.shields.io/badge/Spring%20Cloud%20Gateway-3.x-green)
![Java](https://img.shields.io/badge/Java-8+-blue)
![MongoDB](https://img.shields.io/badge/MongoDB-4.x+-orange)
![SkyWalking](https://img.shields.io/badge/SkyWalking-8.x+-purple)

**为 Spring Cloud Gateway 提供完整的访问日志记录功能，支持控制台日志输出和MongoDB数据存储，具备灵活的配置控制和智能过滤机制。**

[快速开始](#-快速开始) • [配置详解](#️-配置详解) • [使用场景](#-使用场景) • [常见查询](#-常见查询)

</div>

---

## 📋 目录

- [概述](#-概述)
- [核心特性](#-核心特性)
- [快速开始](#-快速开始)
- [配置详解](#️-配置详解)
- [日志格式与数据结构](#-日志格式与数据结构)
- [存储管理](#-存储管理)
- [使用场景](#-使用场景)
- [常见查询](#-常见查询)
- [注意事项与故障排除](#️-注意事项与故障排除)
- [扩展功能](#-扩展功能)

## 📖 概述

Spring Cloud Gateway 访问日志系统是一个高性能、可扩展的日志记录解决方案，专为微服务架构设计。系统提供完整的请求-响应生命周期记录，支持多种输出方式，并具备智能过滤和安全脱敏功能。

### 🎯 设计目标

- **完整性**：记录请求-响应的完整生命周期信息
- **高性能**：最小化对网关性能的影响
- **可扩展**：支持多种存储方式和自定义扩展
- **安全性**：自动脱敏敏感信息，保护数据安全
- **易用性**：提供灵活的配置选项和丰富的查询功能

## ✨ 核心特性

### 🔍 完整信息记录

| 类别 | 记录内容 | 示例 |
|------|----------|------|
| **请求信息** | HTTP方法、URL、客户端IP、User-Agent、Referer、登录用户 | `GET /api/users?page=1` |
| **响应信息** | 状态码、处理时间、响应时间戳 | `200 OK, 150ms` |
| **追踪信息** | 请求ID、SkyWalking链路追踪 | `traceId=abc123, segmentId=def456` |
| **安全信息** | 敏感数据自动脱敏 | `password=***, Authorization=***` |

### 🎯 智能过滤机制

```yaml
# 路径过滤 - 支持Ant路径匹配
exclude-paths:
  - "/linkusMonitor/**"           # 监控端点
  - "/linkus-*/linkusMonitor/**"  # 微服务监控端点
  - "/health/**"                  # 健康检查

# 静态文件过滤 - 正则表达式识别
static-file-pattern: "\\.(css|js|png|jpg|jpeg|gif|bmp|ico|svg|woff|woff2|ttf|eot|otf)$"

# 方法过滤 - 排除特定HTTP方法
exclude-methods:
  - "OPTIONS"
  - "HEAD"

# 状态码过滤 - 排除错误状态码
exclude-status-codes:
  - 404
  - 405
```

### 🔧 灵活配置控制

- **功能开关**：独立控制访问日志、控制台输出、MongoDB存储
- **微服务支持**：支持`linkus-`前缀的context-path架构
- **SkyWalking集成**：可配置是否使用SkyWalking traceId作为请求ID
- **环境适配**：针对开发、测试、生产环境提供不同配置模板

## 🚀 快速开始

### 1. 添加配置

在 `application.yml` 中添加基础配置：

```yaml
spring:
  cloud:
    gateway:
      access-log:
        enabled: true                    # 启用访问日志
        use-sky-walking-trace-id: true   # 使用SkyWalking traceId
        enable-console-log: true         # 启用控制台日志
        enable-mongo-storage: true       # 启用MongoDB存储
```

### 2. 查看日志

#### 控制台日志
```bash
# 实时查看访问日志
tail -f logs/linkus-gateway/access.log

# 查看特定请求ID
grep "RequestId=abc123" logs/linkus-gateway/access.log
```

#### MongoDB查询
```javascript
// 查询特定请求
db.tssAccessLog_202401.findOne({requestId: "abc123"})

// 时间范围查询
db.tssAccessLog_202401.find({
  timestamp: {$gte: new Date("2024-01-01"), $lte: new Date("2024-01-02")}
})
```

## ⚙️ 配置详解

### 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | true | 是否启用访问日志功能 |
| `use-sky-walking-trace-id` | boolean | true | 是否使用SkyWalking traceId作为请求ID |
| `enable-console-log` | boolean | true | 是否输出控制台日志 |
| `enable-mongo-storage` | boolean | true | 是否保存到MongoDB |

### 过滤规则配置

```yaml
spring:
  cloud:
    gateway:
      access-log:
        # 路径过滤 - 支持Ant路径匹配
        exclude-paths:
          - "/linkusMonitor/**"           # 监控端点
          - "/linkus-*/linkusMonitor/**"  # 微服务监控端点
          - "/health/**"                  # 健康检查

        # 静态文件过滤 - 正则表达式识别
        static-file-pattern: "\\.(css|js|png|jpg|jpeg|gif|bmp|ico|svg|woff|woff2|ttf|eot|otf)$"

        # 方法过滤 - 排除特定HTTP方法
        exclude-methods:
          - "OPTIONS"
          - "HEAD"

        # 状态码过滤 - 排除错误状态码
        exclude-status-codes:
          - 404
          - 405
```

### 安全配置

```yaml
spring:
  cloud:
    gateway:
      access-log:
        # 敏感信息脱敏
        sensitive-headers:
          - "Authorization"
          - "Cookie"
          - "JWT-TOKEN"
        sensitive-params:
          - "password"
          - "token"
          - "secret"
```

## 📊 日志格式与数据结构

### 控制台日志格式

```
ACCESS_LOG RequestId=abc123, Method=GET, Domain=http://example.com, Path=/api/users, Query=page=1&size=10, ClientIP=***********, UserAgent=Mozilla/5.0, LoginName=admin, Referer=http://example.com, StatusCode=200, Duration=150ms, SkyWalkingTraceId=abc123, Timestamp=2024-01-01 12:00:00.150
```

### MongoDB文档结构

```json
{
  "_id": "ObjectId",
  "requestId": "abc123",
  "method": "GET",
  "requestUri": {
    "scheme": "http",
    "host": "example.com",
    "port": 8080,
    "path": "/api/users",
    "query": "page=1&size=10"
  },
  "clientIp": "***********",
  "userAgent": "Mozilla/5.0",
  "loginUser": {
    "userId": "507f1f77bcf86cd799439011",
    "loginName": "admin",
    "userName": "管理员",
    "jobCode": "EMP001"
  },
  "referer": "http://example.com",
  "statusCode": 200,
  "duration": 150,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "skyWalkingInfo": {
    "skyWalkingTraceId": "abc123",
    "skyWalkingSegmentId": "def456",
    "skyWalkingSpanId": "ghi789"
  },
  "sensitiveHeaders": "Authorization=***, Cookie=***",
  "sensitiveParams": "password=***, token=***"
}
```

### 数据结构说明

#### 主要字段
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `requestId` | String | 请求唯一标识 | `abc123` |
| `method` | String | HTTP方法 | `GET`、`POST` |
| `requestUri` | Object | 请求URI信息 | 包含scheme、host、port、path、query |
| `clientIp` | String | 客户端IP地址 | `***********` |
| `loginUser` | Object | 登录用户信息 | 包含userId、loginName、userName、jobCode |
| `statusCode` | Integer | HTTP状态码 | `200`、`404` |
| `duration` | Long | 请求处理时间（毫秒） | `150` |
| `timestamp` | Date | 请求时间戳 | `2024-01-01T12:00:00.000Z` |
| `skyWalkingInfo` | Object | 链路追踪信息 | 包含traceId、segmentId、spanId |

## 💾 存储管理

### 按月分表策略

| 特性 | 说明 |
|------|------|
| **表命名规则** | `tssAccessLog_YYYYMM`（如：`tssAccessLog_202401`） |
| **自动分表** | 系统根据访问日志的时间戳自动存储到对应月份的表 |
| **查询支持** | 支持跨月份查询和历史数据检索 |
| **索引优化** | 为每个分表建立合适的索引，提高查询性能 |

### 数据归档策略

**归档条件：** 数据库空间使用率超过80%、历史数据查询频率降低、存储成本优化需求

```javascript
// 归档脚本示例
function archiveAccessLog(targetMonth) {
  const sourceCollection = `tssAccessLog_${targetMonth}`;
  const archiveCollection = `tssAccessLog_archive_${targetMonth}`;

  // 迁移数据到归档表
  db[sourceCollection].aggregate([{$out: archiveCollection}]);

  // 清理原始表数据
  db[sourceCollection].drop();

  print(`归档完成: ${sourceCollection} -> ${archiveCollection}`);
}

// 查看存储使用情况
db.getCollectionNames()
  .filter(name => name.startsWith("tssAccessLog_"))
  .forEach(table => {
    const count = db[table].count();
    const stats = db[table].stats();
    print(`${table}: ${count} 条记录, ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
  });
```



## 🎯 使用场景

### 🛠️ 开发环境

```yaml
access-log:
  enabled: true
  enable-console-log: true      # 开启控制台日志，便于调试
  enable-mongo-storage: false   # 关闭MongoDB存储，节省资源
  exclude-paths:
    - "/linkusMonitor/**"
    - "/health/**"
```

**特点：**
- 实时查看请求日志，便于调试
- 减少存储开销
- 保留必要的过滤规则

### 🧪 测试环境

```yaml
access-log:
  enabled: true
  enable-console-log: true      # 开启控制台日志
  enable-mongo-storage: true    # 开启MongoDB存储，便于分析
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
```

**特点：**
- 完整的日志记录
- 便于性能测试和问题分析
- 支持数据统计和监控

### 🚀 生产环境

```yaml
access-log:
  enabled: true
  enable-console-log: false     # 关闭控制台日志，提高性能
  enable-mongo-storage: true    # 开启MongoDB存储，便于监控
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
    - "/favicon.ico"
    - "/robots.txt"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
  exclude-status-codes:
    - 404
    - 405
```

**特点：**
- 优化性能，减少日志输出开销
- 完整的存储记录，便于监控和分析
- 严格的过滤规则，减少无效数据

### ⚡ 性能优化模式

```yaml
access-log:
  enabled: true
  enable-console-log: false     # 关闭控制台日志
  enable-mongo-storage: false   # 关闭MongoDB存储
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
    - "/favicon.ico"
    - "/robots.txt"
    - "/static/**"
    - "/assets/**"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
    - "TRACE"
  exclude-status-codes:
    - 404
    - 405
    - 406
```

**特点：**
- 最小化性能影响
- 仅记录关键业务请求
- 适用于高并发场景

## 🔍 常见查询

### 性能分析

```javascript
// 查询响应时间超过1秒的请求
db.tssAccessLog_202401.find({duration: {$gt: 1000}})

// 统计平均响应时间
db.tssAccessLog_202401.aggregate([
  {$group: {_id: null, avgDuration: {$avg: "$duration"}}}
])

// 查询最慢的10个请求
db.tssAccessLog_202401.find({})
  .sort({duration: -1})
  .limit(10)
  .project({"requestUri.path": 1, duration: 1, method: 1, timestamp: 1})

// 按路径统计平均响应时间
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: "$requestUri.path",
    avgDuration: {$avg: "$duration"},
    count: {$sum: 1}
  }},
  {$sort: {avgDuration: -1}},
  {$limit: 10}
])
```

### 用户行为分析

```javascript
// 查询特定用户的访问记录
db.tssAccessLog_202401.find({"loginUser.loginName": "admin"})

// 统计最活跃的IP地址
db.tssAccessLog_202401.aggregate([
  {$group: {_id: "$clientIp", count: {$sum: 1}}},
  {$sort: {count: -1}},
  {$limit: 10}
])

// 按小时统计访问量趋势
db.tssAccessLog_202401.aggregate([
  {
    $group: {
      _id: {
        year: {$year: "$timestamp"},
        month: {$month: "$timestamp"},
        day: {$dayOfMonth: "$timestamp"},
        hour: {$hour: "$timestamp"}
      },
      count: {$sum: 1}
    }
  },
  {$sort: {"_id": 1}},
  {$limit: 24}
])
```

### 错误监控

```javascript
// 查询所有错误请求
db.tssAccessLog_202401.find({statusCode: {$gte: 400}})

// 统计错误率
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: null,
    total: {$sum: 1},
    errors: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}}
  }},
  {$project: {errorRate: {$divide: ["$errors", "$total"]}}}
])

// 按状态码统计错误分布
db.tssAccessLog_202401.aggregate([
  {$match: {statusCode: {$gte: 400}}},
  {$group: {_id: "$statusCode", count: {$sum: 1}}},
  {$sort: {count: -1}}
])
```

### 链路追踪分析

```javascript
// 查询特定SkyWalking traceId的请求
db.tssAccessLog_202401.find({"skyWalkingInfo.skyWalkingTraceId": "abc123"})

// 统计链路追踪覆盖率
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: null,
    total: {$sum: 1},
    withTraceId: {$sum: {$cond: [{$ne: ["$skyWalkingInfo.skyWalkingTraceId", null]}, 1, 0]}}
  }},
  {$project: {coverage: {$divide: ["$withTraceId", "$total"]}}}
])
```

## ⚠️ 注意事项与故障排除

### 性能优化建议

| 建议 | 说明 | 影响 |
|------|------|------|
| **合理配置排除规则** | 避免记录不必要的请求 | 减少日志量，提高性能 |
| **生产环境关闭控制台日志** | 减少I/O开销 | 显著提升网关性能 |
| **定期清理历史数据** | 控制MongoDB存储大小 | 避免存储空间不足 |
| **优化索引策略** | 为常用查询字段建立索引 | 提高查询性能 |

### 安全注意事项

| 建议 | 说明 | 风险 |
|------|------|------|
| **确保敏感信息脱敏** | 配置完整的脱敏规则 | 避免敏感数据泄露 |
| **定期检查脱敏配置** | 验证脱敏功能正常工作 | 防止配置遗漏 |
| **控制日志访问权限** | 限制日志数据的访问范围 | 防止未授权访问 |

### 常见问题排查

#### 日志不记录
**症状：** 访问请求没有产生日志记录

**解决方案：**
1. 检查`enabled`配置是否为`true`
2. 检查路径是否被排除规则过滤
3. 确认微服务context-path配置正确

```yaml
# 临时开启调试模式
access-log:
  enabled: true
  enable-console-log: true
  exclude-paths: []  # 清空排除规则进行测试
```

#### 性能问题
**症状：** 网关响应时间明显增加

**解决方案：**
```yaml
# 性能优化配置
access-log:
  enable-console-log: false     # 关闭控制台日志
  exclude-paths:
    - "/linkusMonitor/**"
    - "/static/**"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
  exclude-status-codes:
    - 404
    - 405
```

#### 敏感信息泄露
**症状：** 日志中出现敏感信息

**解决方案：**
```yaml
# 完善敏感信息配置
access-log:
  sensitive-headers:
    - "Authorization"
    - "Cookie"
    - "JWT-TOKEN"
    - "X-Auth-Token"
  sensitive-params:
    - "password"
    - "token"
    - "secret"
    - "key"
```



## 📈 扩展功能

### 自定义日志格式

可以修改`AccessLogGlobalFilter`中的日志格式，添加自定义字段：

```java
// 自定义日志格式示例
String logMessage = String.format(
    "ACCESS_LOG RequestId=%s, Method=%s, Path=%s, ClientIP=%s, " +
    "CustomField=%s, StatusCode=%d, Duration=%dms",
    requestId, method, path, clientIp, customValue, statusCode, duration
);
```

### 新增存储方式

可以扩展`IMongoStorageDao`接口，支持其他数据库存储：

```java
public interface IStorageDao {
    void save(AccessLogInfo logInfo);
    List<AccessLogInfo> query(AccessLogQuery query);
    void delete(String requestId);
}

// 实现类示例
@Component
public class RedisStorageDao implements IStorageDao {
    // Redis存储实现
}
```

### 实时监控

可以基于MongoDB数据构建实时监控面板：

```javascript
// 实时统计脚本
function getRealTimeStats() {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

  return db.tssAccessLog_202401.aggregate([
    {$match: {timestamp: {$gte: oneHourAgo}}},
    {$group: {
      _id: null,
      totalRequests: {$sum: 1},
      avgResponseTime: {$avg: "$duration"},
      errorCount: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}}
    }},
    {$project: {
      totalRequests: 1,
      avgResponseTime: {$round: ["$avgResponseTime", 2]},
      errorRate: {$divide: ["$errorCount", "$totalRequests"]}
    }}
  ]).toArray()[0];
}
```

### 告警机制

基于访问日志数据设置告警规则：

```javascript
// 告警检查脚本
function checkAlerts() {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

  // 检查错误率
  const errorStats = db.tssAccessLog_202401.aggregate([
    {$match: {timestamp: {$gte: fiveMinutesAgo}}},
    {$group: {
      _id: null,
      total: {$sum: 1},
      errors: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}}
    }},
    {$project: {errorRate: {$divide: ["$errors", "$total"]}}}
  ]).toArray()[0];

  if (errorStats && errorStats.errorRate > 0.05) {
    print(`🚨 告警: 错误率过高 ${(errorStats.errorRate * 100).toFixed(2)}%`);
  }
}
```

---

<div align="center">

**Spring Cloud Gateway 访问日志系统** - 为您的微服务架构提供强大的日志记录能力

[返回顶部](#spring-cloud-gateway-访问日志系统)

</div> 
