package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @Author: feizj
 * @Description: 实施计划偏差
 * @Date: 2023/8/30 16:44
 */
@Data
public class PrjImplPlanDvtVo {

    @ExcelIgnore()
    private ObjectId id;

    @ExcelIgnore()
    private ObjectId typeId;

    @ExcelIgnore()
    private Integer lineNo;

    @ExcelIgnore()
    private ObjectId provId;

    @ExcelIgnore
    private ObjectId prjId;

    @ExcelIgnore()
    private ObjectId causeTypeId;

    @ExcelIgnore()
    private ObjectId causeSubTypeId;

    @ExcelProperty({"是否填写"})
    private String isFillIn;

    @ExcelProperty({"发起邮件反馈"})
    private String isMailed;

    @ExcelProperty({"大区"})
    private String bigRegion;

    @ExcelProperty({"区域"})
    private String region;

    @ExcelProperty({"工程部"})
    private String engDept;

    @ExcelProperty({"省份"})
    private String prov;

    @ExcelProperty({"跟踪人"})
    private String trackUserName;

    @ExcelProperty({"预测流水号"})
    private String subOrderCode;

    @ExcelProperty({"净销售额(K)"})
    private String netSaleAmt;

    @ExcelProperty({"项目编码"})
    private String subPrjCode;

    @ExcelProperty({"项目集编码"})
    private String prjCode;

    @ExcelProperty({"项目集名称"})
    private String prjName;
    @ExcelProperty({"项目集状态"})
    private String statusName;

    @ExcelProperty({"项目集创建日期"})
    private String addTime;


    @ExcelProperty({"基准发布日期"})
    private String bmkPublishDate;

    @ExcelProperty({"项目经理"})
    private String pmUserName;

    @ExcelProperty({"分类"})
    private String levelName;

    @ExcelProperty({"POM状态"})
    private String subPomStatus;

    @ExcelProperty({"管理月份"})
    private String ym;

    @ExcelProperty({"ABP净销售额(K)"})
    private Double abpSalesM;

    @ExcelProperty({"ABP GM(%)"})
    private String abpGm;

    @ExcelProperty({"基准预测收入(K)"})
    private Double bmkForecastIncome;

    @ExcelProperty({"基准GM(%)"})
    private String bmkGm;

    @ExcelProperty({"ABP正式"})
    private Double abpOopEmpNum;

    @ExcelProperty({"ABP外包"})
    private Double abpOopOsEmpNum;

    @ExcelProperty({"基准正式"})
    private Double sumEmpNum;

    @ExcelProperty({"基准外包"})
    private Double sumOsEmpNum;

    @ExcelProperty({"正式+外包偏差"})
    private Double diff;

    @ExcelProperty({"正式+外包偏差率（%）"})
    private String diffRate;

    @ExcelProperty({"是否偏差"})
    private String isOverSpend;

    @ExcelProperty({"人力偏差情况"})
    private String hrDvtDesc;

    @ExcelProperty({"偏差原因分析"})
    private String desc;

    @ExcelProperty({"整改措施"})
    private String rectifyAction;

    @ExcelProperty({"整改完成日期"})
    private String rectifyEndDate;

    @ExcelProperty({"原因大类"})
    private String causeType;
    @ExcelProperty({"病因"})
    private String causeSubType;
    @ExcelProperty({"药方"})
    private String causeSub2Type;
    @ExcelProperty({"是否分析"})
    private String hasAnalysis;
    @ExcelIgnore()
    private String effectHasConform;
    @ExcelProperty({"原因分析"})
    private String notes;
    @ExcelProperty({"整改措施-评审"})
    private String rectifyActionReviewed;
    @ExcelProperty({"状态"})
    private String rectifyStatus;
    @ExcelProperty({"严重程度"})
    private String colorSign;
    @ExcelIgnore()
    private Double blIncomePer100;//基准“百元人工-收入”
    @ExcelIgnore()
    private Double actIncomePer100; //实际“百元人工-收入”
    @ExcelIgnore()
    private Double blIncome;//基准收入
    @ExcelIgnore()
    private Double actIncome;//实际收入
    @ExcelIgnore()
    private Double blRgFee;//基准人工费
    @ExcelIgnore()
    private Double actRgFee;//实际人工费
    @ExcelIgnore
    private Double effectDeviation;//效能偏差
    @ExcelIgnore
    private Double effectFactorDiff;//实际VS基准效能因子-差值
    @ExcelIgnore
    private String symptom;//病症
    @ExcelIgnore
    private ObjectId causeSub2TypeId;//药方
    @ExcelIgnore
    private List<String> descList;
    @ExcelIgnore
    private List<String> rectifyActionList;
    @ExcelIgnore
    private String firstVerPlanEndDate;
    @ExcelIgnore
    private String needCheck;
    @ExcelIgnore
    private String mgtAction;

    //全周期-合计
    @ExcelIgnore
    private Double bmkAllCycleTotal;
    @ExcelIgnore
    private Double actAllCycleTotal;
    @ExcelIgnore
    private Double allCycleDiff;
    @ExcelIgnore
    private Double allCycleDiffRate;
    //全周期-正式
    @ExcelIgnore
    private Double bmkAllCycleEmpNumTotal;
    @ExcelIgnore
    private Double actAllCycleEmpNumTotal;
    @ExcelIgnore
    private Double allCycleEmpNumDiff;
    @ExcelIgnore
    private Double allCycleEmpNumDiffRate;
    //全周期-外包
    @ExcelIgnore
    private Double bmkAllCycleOsEmpNumTotal;
    @ExcelIgnore
    private Double actAllCycleOsEmpNumTotal;
    @ExcelIgnore
    private Double allCycleOsEmpNumDiff;
    @ExcelIgnore
    private Double allCycleOsEmpNumDiffRate;

    //历史年度-合计
    @ExcelIgnore
    private Double bmkHistoryTotal;
    @ExcelIgnore
    private Double actHistoryTotal;
    @ExcelIgnore
    private Double historyDiff;
    @ExcelIgnore
    private Double historyDiffRate;
    //历史年度-正式
    @ExcelIgnore
    private Double bmkHistoryEmpNumTotal;
    @ExcelIgnore
    private Double actHistoryEmpNumTotal;
    @ExcelIgnore
    private Double historyEmpNumDiff;
    @ExcelIgnore
    private Double historyEmpNumDiffRate;
    //历史年度-外包
    @ExcelIgnore
    private Double bmkHistoryOsEmpNumTotal;
    @ExcelIgnore
    private Double actHistoryOsEmpNumTotal;
    @ExcelIgnore
    private Double historyOsEmpNumDiff;
    @ExcelIgnore
    private Double historyOsEmpNumDiffRate;

    //本年度-合计
    @ExcelIgnore
    private Double bmkThisYearTotal;
    @ExcelIgnore
    private Double actThisYearTotal;
    @ExcelIgnore
    private Double thisYearDiff;
    @ExcelIgnore
    private Double thisYearDiffRate;
    //本年度-正式
    @ExcelIgnore
    private Double bmkThisYearEmpNumTotal;
    @ExcelIgnore
    private Double actThisYearEmpNumTotal;
    @ExcelIgnore
    private Double thisYearEmpNumDiff;
    @ExcelIgnore
    private Double thisYearEmpNumDiffRate;
    //本年度-外包
    @ExcelIgnore
    private Double bmkThisYearOsEmpNumTotal;
    @ExcelIgnore
    private Double actThisYearOsEmpNumTotal;
    @ExcelIgnore
    private Double thisYearOsEmpNumDiff;
    @ExcelIgnore
    private Double thisYearOsEmpNumDiffRate;

    //未来年度-合计
    @ExcelIgnore
    private Double bmkFutureTotal;
    @ExcelIgnore
    private Double actFutureTotal;
    @ExcelIgnore
    private Double futureDiff;
    @ExcelIgnore
    private Double futureDiffRate;
    //未来年度-正式
    @ExcelIgnore
    private Double bmkFutureEmpNumTotal;
    @ExcelIgnore
    private Double actFutureEmpNumTotal;
    @ExcelIgnore
    private Double futureEmpNumDiff;
    @ExcelIgnore
    private Double futureEmpNumDiffRate;
    //未来年度-外包
    @ExcelIgnore
    private Double bmkFutureOsEmpNumTotal;
    @ExcelIgnore
    private Double actFutureOsEmpNumTotal;
    @ExcelIgnore
    private Double futureOsEmpNumDiff;
    @ExcelIgnore
    private Double futureOsEmpNumDiffRate;
    @ExcelIgnore()
    private ObjectId rectifyStatusId;
}
