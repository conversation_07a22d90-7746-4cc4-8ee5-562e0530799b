package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.linkus.base.db.mongo.model.TeIdName;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

@Data
@HeadRowHeight(25)
public class PrjDvtMgtCostOverABCGVo {
    @ExcelProperty({"是否填写"})
    private String isFillIn;

    @ExcelProperty({"发起邮件反馈"})
    private String isMailed;

    @ExcelProperty({"大区"})
    private String bigRegion;

    @ExcelProperty({"区域"})
    private String region;

    @ExcelProperty({"工程部"})
    private String engDept;

    @ExcelProperty({"省份"})
    private String prov;

    @ExcelProperty({"跟踪人"})
    private String trackUserName;

    @ExcelProperty({"预测流水号"})
    private String subOrderCode;

    @ExcelProperty({"净销售额(K)"})
    private String netSaleAmt;

    @ExcelProperty({"项目编码"})
    private String subPrjCode;

    @ExcelProperty({"项目集编码"})
    private String prjCode;

    @ExcelProperty({"项目集名称"})
    private String prjName;

    @ExcelProperty({"项目集状态"})
    private String statusName;

    @ExcelProperty({"项目集创建日期"})
    private String addTime;

    @ExcelProperty({"基准发布日期"})
    private String bmkPublishDate;

    @ExcelProperty({"项目经理"})
    private String pmUserName;

    @ExcelProperty({"分类"})
    private String levelName;

    @ExcelProperty({"管理月份"})
    private String ym;


    @ExcelProperty({"累计人力资源", "基准累计人力（人月）"})
    private Double bmkAllEmpNum;

    @ExcelProperty({"累计人力资源", "基准正式（人月）"})
    private Double bmkEmpNum;

    @ExcelProperty({"累计人力资源", "基准外包（人月）"})
    private Double bmkOsEmpNum;

    @ExcelProperty({"累计人力资源", "实际累计人力（人月）"})
    private Double actualAllEmpNum;

    @ExcelProperty({"累计人力资源", "实际正式（人月）"})
    private Double actualEmpNum;

    @ExcelProperty({"累计人力资源", "实际外包（人月）"})
    private Double actualOsEmpNum;

    @ExcelProperty({"累计人力资源", "实际VS基准（人月）"})
    private Double empNumDiff;

    @ExcelProperty({"累计人力资源", "偏差率（%）"})
    private Double empNumDiffRate;



    @ExcelProperty({"累计人工费&技术分包费", "基准人工费（K）"})
    private Double bmkAllEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际人工费（K）"})
    private Double actualAllEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际VS基准（K）"})
    private Double allEmpFeeDiff;

    @ExcelProperty({"累计人工费&技术分包费", "基准正式人工费（K）"})
    private Double bmkEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际正式人工费（K）"})
    private Double actualEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "基准外包人工费（K）"})
    private Double bmkOsEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际外包人工费（K）"})
    private Double actualOsEmpFee;

    @ExcelProperty({"累计人工费&技术分包费", "基准实习人工费（K）"})
    private Double bmkTraineeFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际实习人工费（K）"})
    private Double actualTraineeFee;

    @ExcelProperty({"累计人工费&技术分包费", "基准技术分包费（K）"})
    private Double bmkJslfbFee;

    @ExcelProperty({"累计人工费&技术分包费", "实际技术分包费（K）"})
    private Double actualJslfbFee;



    @ExcelProperty({"累计直接费用", "基准直接成本（K）"})
    private Double bmkDirectFee;

    @ExcelProperty({"累计直接费用", "实际直接成本（K）"})
    private Double actualDirectFee;

    @ExcelProperty({"累计直接费用", "实际VS基准-直接成本（K）"})
    private Double directFeeDiff;

    @ExcelProperty({"累计直接费用", "直接成本偏差率（%）"})
    private String directFeeDiffRate;

    @ExcelProperty({"累计直接费用", "基准差旅费（K）"})
    private Double bmkTravelFee;

    @ExcelProperty({"累计直接费用", "实际差旅费（K）"})
    private Double actualTravelFee;

    @ExcelProperty({"累计直接费用", "实际VS基准-差旅费（K）"})
    private Double travelFeeDiff;

    @ExcelProperty({"累计直接费用", "差旅偏差率（%）"})
    private Double travelFeeDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均差旅费（元）"})
    private Double bmkTravelFeePerAvg;

    @ExcelProperty({"累计直接费用", "实际人月均差旅费（元）"})
    private Double actualTravelFeePerAvg;

    @ExcelProperty({"累计直接费用", "人月均差旅偏差率（%）"})
    private String travelFeePerAvgDiffRate;

    @ExcelProperty({"累计直接费用", "基准餐费（K）"})
    private Double bmkDiningFee;

    @ExcelProperty({"累计直接费用", "实际餐费（K）"})
    private Double actualDiningFee;

    @ExcelProperty({"累计直接费用", "实际VS基准-餐费（K）"})
    private Double diningFeeDiff;

    @ExcelProperty({"累计直接费用", "餐费偏差率（%）"})
    private Double diningFeeDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均餐费（元）"})
    private Double bmkDiningFeePerAvg;

    @ExcelProperty({"累计直接费用", "实际人月均餐费（元）"})
    private Double actualDiningFeePerAvg;

    @ExcelProperty({"累计直接费用", "人月均餐费偏差率（%）"})
    private Double diningFeePerAvgDiffRate;

    @ExcelProperty({"累计直接费用", "基准其它费（K）"})
    private Double bmkOtherFee;

    @ExcelProperty({"累计直接费用", "实际其它费（K）"})
    private Double actualOtherFee;

    @ExcelProperty({"累计直接费用", "实际劳务费（K）"})
    private Double actualServiceFee;

    @ExcelProperty({"累计直接费用", "实际VS基准-其它费（K）"})
    private Double otherFeeDiff;

    @ExcelProperty({"累计直接费用", "其它费偏差率（%）"})
    private Double otherFeeDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均其它费（元）"})
    private Double bmkOtherFeePerAvg;

    @ExcelProperty({"累计直接费用", "实际人月均其它费（元）"})
    private Double actualOtherFeePerAvg;
    @ExcelProperty({"累计直接费用", "人月均其它费偏差率（%）"})
    private String otherFeePerAvgDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均差旅住宿费（元）"})
    private Double blTravelHotelFeePem;

    @ExcelProperty({"累计直接费用", "实际人月均差旅住宿费（元）"})
    private Double actTravelHotelFeePem;

    @ExcelProperty({"累计直接费用", "人月均差旅住宿费偏差"})
    private Double travelHotelFeePemDiff;

    @ExcelProperty({"累计直接费用", "人月均差旅住宿费偏差率（%）"})
    private String travelHotelFeePemDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均差旅交通费（元）"})
    private Double bmkTravelCarFeePem;

    @ExcelProperty({"累计直接费用", "实际人月均差旅交通费（元）"})
    private Double actualTravelCarFeePem;

    @ExcelProperty({"累计直接费用", "人月均差旅交通费偏差"})
    private Double travelCarFeePemDiff;

    @ExcelProperty({"累计直接费用", "人月均差旅交通费偏差率（%）"})
    private String travelCarFeePemDiffRate;

    @ExcelProperty({"累计直接费用", "基准人月均市内交通费（元）"})
    private Double bmkCityCarFeePerEmpm;

    @ExcelProperty({"累计直接费用", "实际人月均市内交通费（元）"})
    private Double actualCityCarFeePerEmp;

    @ExcelProperty({"累计直接费用", "人月均市内交通费偏差"})
    private Double cityCarFeePerEmpDiff;

    @ExcelProperty({"累计直接费用", "人月均市内交通费偏差率（%）"})
    private String cityCarFeePerEmpDiffRate;


    @ExcelProperty({"累计总成本", "基准总成本（K）"})
    private Double bmkAllCost;

    @ExcelProperty({"累计总成本", "实际总成本（K）"})
    private Double actualAllCost;

    @ExcelProperty({"累计总成本", "实际VS基准（K）"})
    private Double allCostDiff;


    @ExcelProperty({"比上月成本变化", "累计人力（人月）"})
    private Double allEmpNumDiffLastMoth;

    @ExcelProperty({"比上月成本变化", "累计餐费（K）"})
    private Double diningFeeDiffLastMoth;

    @ExcelProperty({"比上月成本变化", "累计人月均差旅费（元）"})
    private Double travelFeePerAvgDiffLastMoth;

    @ExcelProperty({"比上月成本变化", "累计人月均餐费（元）"})
    private Double diningFeePerAvgDiffLastMoth;

    @ExcelProperty({"比上月成本变化", "累计人月均其它（元）"})
    private Double otherFeePerAvgDiffLastMoth;


    @ExcelProperty({"比上月偏差趋势", "累计人力"})
    private String allEmpNumDiffRateLastMoth;

    @ExcelProperty({"比上月偏差趋势", "累计餐费"})
    private String diningFeeDiffRateLastMoth;

    @ExcelProperty({"比上月偏差趋势", "累计人月均差旅费"})
    private String travelFeePerAvgRateDiffLastMoth;

    @ExcelProperty({"比上月偏差趋势", "累计人月均餐费"})
    private String diningFeePerAvgRateDiffLastMoth;

    @ExcelProperty({"比上月偏差趋势", "累计人月均其它费"})
    private String otherFeePerAvgRateDiffLastMoth;


    @ExcelProperty({"全周期基准", "总成本（K）"})
    private Double lcBlFee;
    @ExcelProperty({"全周期基准", "人力资源（人月）"})
    private Double lcBlMm;
    @ExcelProperty({"全周期基准", "直接费用（K）"})
    private Double lcBlDirectFee;
    @ExcelProperty({"全周期基准", "差旅费（K）"})
    private Double lcBlTravelFee;
    @ExcelProperty({"全周期基准", "餐费（K）"})
    private Double lcBlDiningFee;
    @ExcelProperty({"全周期基准", "其它费（K）"})
    private Double lcBlOtherFee;

    @ExcelProperty({"基准GM值"})
    private String bmkGm;

    @ExcelProperty({"预估GM值"})
    private String actualGm;


    @ExcelProperty({"偏差类型","偏差类型"})
    private String devType;
    @ExcelProperty({"上月原因","上月原因"})
    private String lastMonthRptDesc;
    @ColumnWidth(20)
    @ExcelProperty({"上月考核措施","上月考核措施"})
    private String lastMonthNeedCheck;
    @ColumnWidth(20)
    @ExcelProperty({"上月管控措施","上月管控措施"})
    private String lastMonthMgtAction;
    @ColumnWidth(20)
    @ExcelProperty({"偏差原因分析","偏差原因分析"})
    private String desc;

    @ExcelProperty({"整改措施"})
    private String rectifyAction;

    @ExcelProperty({"整改完成日期"})
    private String rectifyEndDate;

    @ExcelProperty({"病症","病症"})
    private String symptom;
    @ExcelProperty({"原因大类","原因大类"})
    private String causeType;
    @ExcelProperty({"原因小类","原因小类"})
    private String causeSubType;
    @ExcelProperty({"药方","药方"})
    private String causeSub2Type;
    @ExcelProperty({"是否考核","是否考核"})
    private String needCheck;
    @ExcelProperty({"管理动作","管理动作"})
    private String mgtAction;
    @ExcelProperty({"是否分析","是否分析"})
    private String hasAnalysis;
    @ExcelProperty({"原因分析"})
    private String notes;
    @ExcelProperty({"整改措施-评审"})
    private String rectifyActionReviewed;
    @ExcelProperty({"状态"})
    private String rectifyStatus;
    @ExcelProperty({"严重程度"})
    private String colorSign;

    @ExcelIgnore()
    private ObjectId id;
    @ExcelIgnore()
    private ObjectId typeId;
    @ExcelIgnore()
    private Integer lineNo;
    @ExcelIgnore()
    private ObjectId provId;
    //人月均餐费偏差率
    @ExcelIgnore
    private Double diningFeePerAvgDiff;
    //人月均差旅偏差率
    @ExcelIgnore
    private Double travelFeePerAvgDiff;
    //人月均其他费偏差率
    @ExcelIgnore
    private Double otherFeePerAvgDiff;
    @ExcelIgnore()
    private ObjectId causeTypeId;
    @ExcelIgnore()
    private ObjectId causeSubTypeId;
    @ExcelIgnore()
    private ObjectId causeSub2TypeId;
    @ExcelIgnore()
    private ObjectId rectifyStatusId;
    @ExcelIgnore
    private ObjectId prjId;
    @ExcelIgnore
    private List<String> descList;
    @ExcelIgnore
    private List<String> rectifyActionList;

    @ExcelIgnore
    private Double travelFeeDiffLastMoth;
    @ExcelIgnore
    private Double otherFeeDiffLastMoth;
    //首版计划完成时间
    @ExcelIgnore
    private String firstVerPlanEndDate;
}
