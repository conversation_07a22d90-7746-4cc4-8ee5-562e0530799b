package com.linkus.prjMd.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.model.bo.MongoCountBo;
import com.linkus.base.response.BusinessException;
import com.linkus.common.model.TeDeptAdmin;
import com.linkus.common.model.TeIdNameCn;
import com.linkus.common.model.TeUser;
import com.linkus.common.util.*;
import com.linkus.common.web.CommonResult;
import com.linkus.itf.api.client.AiPrjFeignClient;
import com.linkus.itf.api.client.ItfAiEmpFeignClient;
import com.linkus.itf.api.client.ItfAiEmpHistFeignClient;
import com.linkus.itf.api.client.ItfDeptFeignClient;
import com.linkus.itf.api.model.*;
import com.linkus.msg.api.client.MailFeignClient;
import com.linkus.msg.api.model.ItfMailInfo;
import com.linkus.prj.api.model.AdminUser;
import com.linkus.prj.api.model.BuPfmAdmin;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.ISysUserPfmDao;
import com.linkus.prj.model.TeSysUserPfm;
import com.linkus.prj.model.TeSysUserPfm2InfoItem;
import com.linkus.prj.util.CellStyleHandler;
import com.linkus.prjMd.constant.PrjMdConstant;
import com.linkus.prjMd.dao.IPrjEmpMdPfmDao;
import com.linkus.prjMd.dao.IPrjEmpYmPfmDao;
import com.linkus.prjMd.model.TePrjEmpMdPfm;
import com.linkus.prjMd.model.TePrjEmpMdPfm2OprtInfo;
import com.linkus.prjMd.model.TePrjEmpMdPfm2ToEvalUsers;
import com.linkus.prjMd.model.TePrjEmpYmPfm;
import com.linkus.prjMd.service.IPrjEmpMdPfmService2;
import com.linkus.prjMd.util.FreemakerUtil;
import com.linkus.prjMd.vo.*;
import com.linkus.rms.api.client.RmsEmpDateFeignClient;
import com.linkus.rms.api.model.QueryContentVo;
import com.linkus.rms.api.model.RmsEmpDate;
import com.linkus.rms.api.model.RmsWorkRptVo;
import com.linkus.sys.api.client.*;
import com.linkus.sys.api.constants.SysDefType;
import com.linkus.sys.api.model.*;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysCal;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.linkus.common.util.DateUtil.parseDate;

/**
 * 绩效业务实现
 */
@Service
@Slf4j
public class PrjEmpMdPfmServiceImpl2 implements IPrjEmpMdPfmService2 {

    @Autowired
    private IPrjEmpMdPfmDao pfmDao;

    @Autowired
    private IPrjEmpYmPfmDao ymPfmDao;

    @Autowired
    private AiPrjFeignClient prjFeignClient;

    @Autowired
    private RmsEmpDateFeignClient rmsEmpDateFeignClient;

    @Autowired
    private ItfAiEmpFeignClient itfAiEmpFeignClient;

    @Autowired
    private ItfDeptFeignClient itfDeptFeignClient;

    @Autowired
    private ItfAiEmpHistFeignClient itfAiEmpHistFeignClient;

    @Autowired
    private MailFeignClient mailFeignClient;

    @Autowired
    private SysDefFeignClient sysDefFeignClient;

    @Autowired
    private SysDefCnfgFeignClient sysDefCnfgFeignClient;

    @Autowired
    private SysUserFeignClient sysUserFeignClient;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysDefRoleUserFeignClient roleUserFeignClient;

    @Autowired
    private SysDeptFeignClient sysDeptFeignClient;

    @Autowired
    private ISysCalDao sysCalDao;

    @Autowired
    private ISysUserPfmDao sysUserPfmDao;

    @Autowired
    private SysCalFeignClient sysCalFeignClient;

    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoConverter mongoConverter;

    @Override
    public List<TePrjEmpMdPfm> listMdPfms(PrjEmpMdPfmQuery query) {
        if (query == null) {
            return Collections.emptyList();
        }
        List<IDbCondition> conds = new ArrayList<>();
        Boolean isValid = query.getIsValid();
        if (isValid != null) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, isValid));
        }
        String ym = query.getYm();
        if (StringUtil.isNotEmpty(ym)) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, ym));
        }
        ObjectId statusId = query.getStatusId();
        if (Objects.nonNull(statusId)) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), statusId));
        }
        Boolean isLocked = query.getIsLocked();
        if (isLocked != null) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, isLocked));
        }
        String prjBgId = query.getPrjBgId();
        if (prjBgId != null) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, prjBgId));
        }
        Boolean notPfmMd = query.getNotPfmMd();
        if (notPfmMd != null) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.notPfmMd, notPfmMd));
        }
        List<String> prjBuList = query.getPrjBuIdList();
        if (CollectionUtils.isNotEmpty(prjBuList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.prjBu, prjBuList));
        }
        List<String> prjCodeList = query.getPrjCodeList();
        if (CollectionUtils.isNotEmpty(prjCodeList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.prj.dot(DFN.common_cn), prjCodeList));
        }
        List<String> empBuList = query.getEmpBuIdList();
        if (CollectionUtils.isNotEmpty(empBuList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.empBu, empBuList));
        }
        List<String> empCcList = query.getEmpCcIdList();
        if (CollectionUtils.isNotEmpty(empCcList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.empCc, empCcList));
        }
        List<String> jobCodeList = query.getJobCodeList();
        if (CollectionUtils.isNotEmpty(jobCodeList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCodeList));
        }
        List<String> employeeTypeList = query.getEmployeeTypeList();
        if (CollectionUtils.isNotEmpty(employeeTypeList)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.employeeType, employeeTypeList));
        }

        if (CollectionUtils.isEmpty(conds)) {
            return Collections.emptyList();
        }

        Map<String, String> sortMap = query.getSortMap();
        // sort
        Sort sort = null;
        if (null != sortMap && !sortMap.isEmpty()) {
            List<Sort.Order> orderList = new ArrayList<>();
            for (String fieldName : sortMap.keySet()) {
                Sort.Direction direction = this.getApplyDirection(sortMap.get(fieldName));
                orderList.add(new Sort.Order(direction, fieldName));
            }
            sort = Sort.by(orderList);
        }
        return pfmDao.findByFieldAndConds(conds, null, sort);
    }

    private Sort.Direction getApplyDirection(String value) {
        return Sort.Direction.DESC.name().equals(value) ? Sort.Direction.DESC : Sort.Direction.ASC;
    }

    @Override
    public String getPfmCloseTime(String bgId) {
        List<ItfSysDef> data = sysDefFeignClient
                .listDefBySrcDefCodeNameAndDefType(bgId, SysDefType.RMS_CLOSE_TIME.getCodeName()).getData(true);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0).getValue();
    }

    @Override
    public String updatePfmCloseTime(String bgId, String forwardDays) {
        if (StringUtil.isEmpty(bgId) || StringUtil.isEmpty(forwardDays)) {
            throw BusinessException.initExc("入参数据不可为空");
        }
        if (Integer.parseInt(forwardDays) < 1) {
            throw BusinessException.initExc("入参必须大于0");
        }
        String pfmCloseTime = this.getPfmCloseTime(bgId);
        String[] split = pfmCloseTime.split(",");
        String closeTime = "";
        if (split.length == 2) {
            String time = split[1];
            closeTime = forwardDays + "," + time;
        } else {
            closeTime = forwardDays;
        }
        sysDefFeignClient.updatePfmCloseTime(bgId, closeTime);

        String evalYm = getEvalYm(bgId);
        PrjEmpMdPfmQuery pfmQuery = PrjEmpMdPfmQuery.builder().isValid(true).isLocked(true).ym(evalYm).build();
        List<TePrjEmpMdPfm> prjEmpMdPfms = listMdPfms(pfmQuery);
        if (CollectionUtils.isEmpty(prjEmpMdPfms)) {
            // 当前考评月未关闭
            return "绩效打分开启时间修改成功，将于" + evalYm + "月生效！";
        } else {
            // 当前考评月已关闭
            String evalNextYm = DateUtil.format(new Date(), DateUtil.DATE_MONTH_FOTMAT);
            return "绩效打分开启时间修改成功，将于" + evalNextYm + "月生效！";
        }
    }

    @Override
    public void initPfmData(String bgId, String ym) {

    }

    public static void main(String[] args) {
        List<ItfAiEmp> pmList = new ArrayList<>();
        ItfAiEmp emp = new ItfAiEmp();
        emp.setPersonId("1");
        emp.setLastName("1-1");
        pmList.add(emp);
        emp = new ItfAiEmp();
        emp.setPersonId("2");
        emp.setLastName("2-2");
        pmList.add(emp);
        emp = new ItfAiEmp();
        emp.setPersonId("3");
        emp.setLastName("3-3");
        pmList.add(emp);

        Map<String, String> pmJobCodeUserMap = pmList.stream()
                .collect(Collectors.toMap(item -> item.getPersonId(), ItfAiEmp::getLastName));
        System.out.println(pmJobCodeUserMap);

        Map<String, List<ItfAiEmp>> pmUserMap = pmList.stream().collect(Collectors.groupingBy(ItfAiEmp::getPersonId));
        System.out.println(pmUserMap);

        Map<String, List<String>> pmJobCodeUserList = new HashMap<>();
        for (ItfAiEmp aiEmp : pmList) {
            List<String> dataList = pmJobCodeUserList.computeIfAbsent(aiEmp.getPersonId(), a -> new ArrayList<>());
            dataList.add(aiEmp.getPersonId());
        }
        System.out.println(pmJobCodeUserList);
    }

    // 获取在人员历史表中考评月最后一个自然日中currentFlag为Y且timeSheetFlag为Y的人员
    private List<ItfAiEmpHist> listUserHistByEvalYm(String evalYm) {

        Date lastDay = DateUtil.getLastDayByMonth(DateUtil.parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT));
        String lastDayValue = DateUtil.format(lastDay, DateUtil.DATE_FORMAT);

        // 从人员历史表获取
        ItfAiEmpHistQuery histQuery = ItfAiEmpHistQuery.builder()
                .currentFlag("Y").timeSheetFlag("Y")
                .dates(Collections.singletonList(lastDayValue))
                .build();
        return itfAiEmpHistFeignClient.queryDataByConds(histQuery).getData(true);
    }

    private void addBasicInfos(List<RmsEmpDate> mdList, Map<ObjectId, ItfAiEmpHist> empHistMap, String evalYm) {

        Map<String, List<RmsEmpDate>> prjEmpMdMap = new HashMap<>();
        Set<String> empJobCodeList = new HashSet<>();
        Set<String> mdPrjCodeSet = new HashSet<>();
        for (RmsEmpDate empDate : mdList) {
            TeIdNameCn prj = empDate.getPrj();
            TeUser emp = empDate.getEmp();
            if (Objects.isNull(prj) || Objects.isNull(emp) || Objects.isNull(prj.getCid())
                    || Objects.isNull(emp.getUserId())) {
                continue;
            }
            // 根据prj.cid和emp.userId进行分组
            String key = prj.getCid().toHexString() + "_" + emp.getUserId().toHexString();
            List<RmsEmpDate> prjEmpMdList = prjEmpMdMap.computeIfAbsent(key, a -> new ArrayList<>());
            prjEmpMdList.add(empDate);
            empJobCodeList.add(emp.getJobCode());
            mdPrjCodeSet.add(prj.getCodeName());
        }

        // 查询人员对应管理员
        List<ItfSysUser> sysUserList = sysUserFeignClient.queryUserByJobCodes(new ArrayList<>(empJobCodeList))
                .getData(true);
        Map<String, TeUser> userManagerMap = sysUserList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getJobCode()) && Objects.nonNull(item.getManager()))
                .collect(Collectors.toMap(ItfSysUser::getJobCode, ItfSysUser::getManager, (v1, v2) -> v2));

        // 获取 项目id -> 项目经理jobCode
        List<ItfAiPrj> aiPrjs = prjFeignClient.queryAiPrjsByCode(new ArrayList<>(mdPrjCodeSet)).getData(true);
        List<String> pmJobCodeList = aiPrjs.stream().map(ItfAiPrj::getPmEmployeeNumber).distinct()
                .collect(Collectors.toList());
        List<ItfAiEmp> pmList = itfAiEmpFeignClient.listUserByEmployeeNumber(pmJobCodeList).getData(true);

        // 保存基本信息
        addBasicInfo(prjEmpMdMap, empHistMap, evalYm, aiPrjs, userManagerMap, pmList);
    }

    private void addBasicInfo(Map<String, List<RmsEmpDate>> prjEmpMdMap, Map<ObjectId, ItfAiEmpHist> empHistMap,
                              String evalYm, List<ItfAiPrj> aiPrjs, Map<String, TeUser> userManagerMap, List<ItfAiEmp> pmList) {

        Map<ObjectId, String> prjIdPmMap = aiPrjs.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getPmEmployeeNumber())
                        && Objects.nonNull(item.getDmpPrj())
                        && Objects.nonNull(item.getDmpPrj().getCid()))
                .collect(Collectors.toMap(item -> item.getDmpPrj().getCid(), ItfAiPrj::getPmEmployeeNumber));

        Map<String, TeUser> pmJobCodeUserMap = pmList.stream()
                .filter(item -> item.getDmpUser() != null && item.getDmpUser().getJobCode() != null)
                .collect(
                        Collectors.toMap(item -> item.getDmpUser().getJobCode(), ItfAiEmp::getDmpUser, (k1, k2) -> k1));

        Set<String> specialPrjCodes = new HashSet<>();
        // 获取项目对应的子类型
        Map<String, String> prjCodeToSubType = new HashMap<>();
        for (ItfAiPrj aiPrj : aiPrjs) {
            String projectType = aiPrj.getProjectType();
            String projectCode = aiPrj.getProjectCode();

            if ("工程项目".equals(projectType)) {
                prjCodeToSubType.put(projectCode, aiPrj.getPdProjectType());
                if ("其它类".equals(aiPrj.getPdProjectType())) {
                    specialPrjCodes.add(projectCode);
                }
            } else if ("研发项目".equals(projectType)) {
                prjCodeToSubType.put(projectCode, aiPrj.getRdProjectType());
                if ("P0类".equals(aiPrj.getRdProjectType())) {
                    specialPrjCodes.add(projectCode);
                }
            } else if ("售前项目".equals(projectType)) {
                specialPrjCodes.add(projectCode);
            }
        }

        List<TePrjEmpMdPfm> toSaveData = new ArrayList<>();
        Set<String> prjUserIds = prjEmpMdMap.keySet();
        List<TePrjEmpMdPfm2OprtInfo> oprtInfo;
        TePrjEmpMdPfm prjEmpPfm;
        Date now = new Date();
        for (String prjUserId : prjUserIds) {
            String[] split = prjUserId.split("_");
            ObjectId prjId = new ObjectId(split[0]);
            ObjectId userId = new ObjectId(split[1]);
            List<RmsEmpDate> rmsEmpDates = prjEmpMdMap.get(prjUserId);
            ItfAiEmpHist itfAiEmpHist = empHistMap.get(userId);

            if (CollectionUtils.isEmpty(rmsEmpDates) || Objects.isNull(itfAiEmpHist)) {
                continue;
            }
            RmsEmpDate rmsEmpDate = rmsEmpDates.get(0);
            prjEmpPfm = new TePrjEmpMdPfm();
            prjEmpPfm.setIsValid(true);
            prjEmpPfm.setNotPfmMd(false);
            prjEmpPfm.setYm(evalYm);
            prjEmpPfm.setIsLocked(false);
            prjEmpPfm.setPrjBg(rmsEmpDate.getPrjBgId());
            prjEmpPfm.setPrjBu(rmsEmpDate.getPrjBuId());
            prjEmpPfm.setPrj(rmsEmpDate.getPrj());

            String pmJobCode = prjIdPmMap.get(prjId);
            if (StringUtil.isNotNull(pmJobCode)) {
                TeUser teUser = pmJobCodeUserMap.get(pmJobCode);
                prjEmpPfm.setPm(teUser);
            }
            prjEmpPfm.setEmpBu(itfAiEmpHist.getSbuId());
            prjEmpPfm.setEmpCc(itfAiEmpHist.getCostCenterId());
            prjEmpPfm.setEmployeeType(itfAiEmpHist.getEmployeeType());
            prjEmpPfm.setEmp(itfAiEmpHist.getDmpUser());
            prjEmpPfm.setRole(rmsEmpDate.getRole());
            prjEmpPfm.setMd(rmsEmpDates.size());

            prjEmpPfm.setLastUpdUser(rmsEmpDate.getAddUser());
            prjEmpPfm.setLastUpdTime(now);
            prjEmpPfm.setPrjGroup(rmsEmpDate.getPrjGroup());
            prjEmpPfm.setStatus(PrjMdConstant.TODO);
            // 设置oprtInfo
            TePrjEmpMdPfm2OprtInfo oprtItem = TePrjEmpMdPfm2OprtInfo.builder().oprtTime(now)
                    .oprtType(PrjMdConstant.SYSTEM_INIT).build();
            oprtInfo = new ArrayList<>();
            oprtInfo.add(oprtItem);
            prjEmpPfm.setOprtInfo(oprtInfo);
            TeUser emp = rmsEmpDate.getEmp();
            if (Objects.nonNull(emp)) {
                prjEmpPfm.setEmpManager(userManagerMap.get(emp.getJobCode()));
            }
            prjEmpPfm.setPrjType(rmsEmpDate.getPrjType());
            if ("工程项目".equals(rmsEmpDate.getPrjType()) || "研发项目".equals(rmsEmpDate.getPrjType())) {
                prjEmpPfm.setSubPrjType(prjCodeToSubType.get(rmsEmpDate.getPrj().getCodeName()));
            }
            if (specialPrjCodes.contains(rmsEmpDate.getPrj().getCodeName())) {
                // 重新设置isValid、notPfmMd、status
                prjEmpPfm.setIsValid(false);
                // prjEmpPfm.setNotPfmMd(true);
                prjEmpPfm.setStatus(null);
            }
            String pfmPrjBg = prjEmpPfm.getPrjBg();
            String pfmPrjType = prjEmpPfm.getPrjType();
            String pfmSubPrjType = prjEmpPfm.getSubPrjType();
            if ("30802".equals(pfmPrjBg)) {
                if (("工程项目".equals(pfmPrjType) && "其它类".equals(pfmSubPrjType))
                        || ("研发项目".equals(pfmPrjType) && !"P0(PRD)".equals(pfmSubPrjType)
                        && !"P1类".equals(pfmSubPrjType))
                        || ("售前项目".equals(pfmPrjType))) {
                    prjEmpPfm.setNotPfmMd(true);
                }
            } else if ("53249".equals(pfmPrjBg)) {
                if ("售前项目".equals(pfmPrjType)) {
                    prjEmpPfm.setNotPfmMd(true);
                }
            }
            toSaveData.add(prjEmpPfm);
        }
        if (CollectionUtils.isNotEmpty(toSaveData)) {
            pfmDao.batchSave(toSaveData);
        }
    }

    // 初始化考评人信息
    private void addToEvalUsers(List<RmsEmpDate> rmsEmpDateList, List<ObjectId> prjIdList, String evalYm, String bgId,
                                boolean isSendEmail) {

        // 项目经理
        ItfRoleUserCommonQuery pmQuery = ItfRoleUserCommonQuery.builder()
                .roleIdList(Collections.singletonList(PrjMdConstant.ROLE_PRJ_PM.getCid()))
                .defIdList(prjIdList)
                .build();
        List<ItfSysDefRoleUser> prjManagerList = roleUserFeignClient.queryRoleUserList(pmQuery).getData(true);
        Map<ObjectId, List<ItfSysDefRoleUser>> prjIdToPms = prjManagerList.stream()
                .filter(item -> Objects.nonNull(item.getDefId()))
                .collect(Collectors.groupingBy(ItfSysDefRoleUser::getDefId));

        // 项目助理
        // ItfRoleUserCommonQuery asstQuery = ItfRoleUserCommonQuery.builder()
        // .defTypeIdList(Collections.singletonList(PrjMdConstant.DEF_PRJ_GROUP.getCid()))
        // .srcDefIdList(prjIdList)
        // .roleIdList(Collections.singletonList(PrjMdConstant.ROLE_PRJ_ASST.getCid()))
        // .build();
        // List<ItfSysDefRoleUser> asstList =
        // roleUserFeignClient.queryRoleUserList(asstQuery).getData(true);
        // Map<ObjectId, List<ItfSysDefRoleUser>> prjIdToAssts = asstList.stream()
        // .filter(item -> Objects.nonNull(item.getSrcDef()) &&
        // Objects.nonNull(item.getSrcDef().getSrcDefId()))
        // .collect(Collectors.groupingBy(item -> item.getSrcDef().getSrcDefId()));

        // 项目组长
        ItfRoleUserCommonQuery groupRespQuery = ItfRoleUserCommonQuery.builder()
                .defTypeIdList(Collections.singletonList(PrjMdConstant.DEF_PRJ_GROUP.getCid()))
                .srcDefIdList(prjIdList)
                .roleIdList(Collections.singletonList(PrjMdConstant.ROLE_EVALUATOR_RESP.getCid()))
                .build();
        List<ItfSysDefRoleUser> groupRespList = roleUserFeignClient.queryRoleUserList(groupRespQuery).getData(true);
        Map<ObjectId, List<ItfSysDefRoleUser>> groupIdToGroupResp = groupRespList.stream()
                .filter(item -> Objects.nonNull(item.getDefId()) && Objects.nonNull(item.getSrcDef())
                        && Objects.nonNull(item.getSrcDef().getSrcDefId()))
                .collect(Collectors.groupingBy(ItfSysDefRoleUser::getDefId));

        // 添加项目经理和项目助理
        this.addPmAndAsst(prjIdList, evalYm, prjIdToPms, Collections.EMPTY_MAP);
        // 添加项目组长
        this.addPrjResp(rmsEmpDateList, evalYm, groupIdToGroupResp, null);

        // 发送邮件
        if (isSendEmail) {
            this.sendMailToEvaluators(bgId, evalYm, prjIdToPms, groupIdToGroupResp);
        }
    }

    private void addPmAndAsst(List<ObjectId> prjIdList, String evalYm,
                              Map<ObjectId, List<ItfSysDefRoleUser>> prjIdToPms,
                              Map<ObjectId, List<ItfSysDefRoleUser>> prjIdToAssts) {

        List<List<UpdataData>> updatesAll = new ArrayList<>();
        List<List<IDbCondition>> condsAll = new ArrayList<>();

        for (ObjectId prjId : prjIdList) {

            List<IDbCondition> conds = new ArrayList<>();
            List<UpdataData> updates = new ArrayList<>();
            conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
            conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.common_cid), prjId));
            condsAll.add(conds);

            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUser = new ArrayList<>();
            List<ItfSysDefRoleUser> pmRoleUser = prjIdToPms.get(prjId);
            if (CollectionUtils.isNotEmpty(pmRoleUser)) {
                for (ItfSysDefRoleUser defRoleUser : pmRoleUser) {
                    toEvalUser.add(TePrjEmpMdPfm2ToEvalUsers.builder().role(PrjMdConstant.ROLE_PRJ_PM)
                            .user(defRoleUser.getRoleUser()).build());
                }
            }
            List<ItfSysDefRoleUser> itfSysDefRoleUsers = prjIdToAssts.get(prjId);
            if (CollectionUtils.isNotEmpty(itfSysDefRoleUsers)) {
                itfSysDefRoleUsers = itfSysDefRoleUsers.stream().distinct().collect(Collectors.toList());
                for (ItfSysDefRoleUser defRoleUser : itfSysDefRoleUsers) {
                    toEvalUser.add(TePrjEmpMdPfm2ToEvalUsers.builder().role(PrjMdConstant.ROLE_PRJ_ASST)
                            .user(defRoleUser.getRoleUser()).build());
                }
            }
            updates.add(new UpdataData(DFN.PrjEmpMdPfm.toEvalUsers, toEvalUser));
            updatesAll.add(updates);
        }
        if (CollectionUtils.isNotEmpty(condsAll)) {
            pfmDao.batchUpdate(condsAll, updatesAll, false);
        }
    }

    private void addPrjResp(List<RmsEmpDate> rmsEmpDateList, String evalYm,
                            Map<ObjectId, List<ItfSysDefRoleUser>> groupIdToGroupResp, List<ObjectId> prjIds) {
        // 获取数据里的小组id
        List<ObjectId> groupIdList = rmsEmpDateList.stream()
                .filter(item -> Objects.nonNull(item.getPrjGroup()) && Objects.nonNull(item.getPrjGroup().getCid()))
                .map(item -> item.getPrjGroup().getCid()).distinct().collect(Collectors.toList());

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        if (CollectionUtils.isNotEmpty(prjIds)) {
            conds.add(new DC_I<>(DFN.PrjEmpMdPfm.prj.dot(DFN.common_cid), prjIds));
        }
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.prjGroup.dot(DFN.common_cid), groupIdList));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.PrjEmpMdPfm.toEvalUsers);
        fieldNames.add(DFN.PrjEmpMdPfm.prjGroup);
        List<TePrjEmpMdPfm> hasGroupData = pfmDao.findByFieldAndConds(conds, fieldNames);
        Map<ObjectId, List<TePrjEmpMdPfm>> groupIdToData = hasGroupData.stream()
                .filter(item -> Objects.nonNull(item.getPrjGroup()) && Objects.nonNull(item.getPrjGroup().getCid()))
                .collect(Collectors.groupingBy(item -> item.getPrjGroup().getCid()));

        List<List<UpdataData>> updatesAll = new ArrayList<>();
        List<List<IDbCondition>> condsAll = new ArrayList<>();

        for (ObjectId groupId : groupIdToData.keySet()) {
            List<TePrjEmpMdPfm> groupData = groupIdToData.get(groupId);
            if (CollectionUtils.isEmpty(groupData)) {
                continue;
            }

            updatesAll.clear();
            condsAll.clear();

            // 该小组下的数据 id -> toEvalUsers
            Map<ObjectId, List<TePrjEmpMdPfm2ToEvalUsers>> idToEvalUsers = groupData.stream()
                    .collect(Collectors.toMap(TePrjEmpMdPfm::getId, TePrjEmpMdPfm::getToEvalUsers));
            Set<ObjectId> ids = idToEvalUsers.keySet();
            // 将该小组下的toEvalUsers里添加该小组的组长
            for (ObjectId pfmId : ids) {
                List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = idToEvalUsers.getOrDefault(pfmId, new ArrayList<>());
                List<ItfSysDefRoleUser> groupResps = groupIdToGroupResp.get(groupId);
                if (CollectionUtils.isNotEmpty(groupResps)) {
                    for (ItfSysDefRoleUser defRoleUser : groupResps) {
                        toEvalUsers.add(TePrjEmpMdPfm2ToEvalUsers.builder().role(PrjMdConstant.ROLE_PRJ_GROUP_RESP)
                                .user(defRoleUser.getRoleUser()).build());
                    }
                    toEvalUsers = toEvalUsers.stream().distinct().collect(Collectors.toList());
                    idToEvalUsers.put(pfmId, toEvalUsers);
                }

                // 更新该小组下的数据
                List<IDbCondition> uConds = new ArrayList<>();
                List<UpdataData> updates = new ArrayList<>();
                uConds.add(new DC_E(DFN.common__id, pfmId));
                condsAll.add(uConds);
                updates.add(new UpdataData(DFN.PrjEmpMdPfm.toEvalUsers, toEvalUsers));
                updatesAll.add(updates);
            }
            if (CollectionUtils.isNotEmpty(condsAll)) {
                pfmDao.batchUpdate(condsAll, updatesAll, false);
            }
        }
    }

    // 绩效初始化完成后，要自动发送邮件通知项目经理、项目助理、项目组长
    // ym: 202405
    private void sendMailToEvaluators(String bgId, String evalYm, Map<ObjectId, List<ItfSysDefRoleUser>> prjIdToPms,
                                      Map<ObjectId, List<ItfSysDefRoleUser>> groupIdToGroupResp) {
        // 查找项目组长
        List<ObjectId> roleCids = Arrays.asList(PrjMdConstant.ROLE_PRJ_PM.getCid(),
                PrjMdConstant.ROLE_PRJ_ASST.getCid(),
                PrjMdConstant.ROLE_PRJ_GROUP_RESP.getCid());
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.notPfmMd, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.role.dot(DFN.common_cid)), roleCids));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.PrjEmpMdPfm.prjGroup);
        fieldNames.add(DFN.PrjEmpMdPfm.toEvalUsers);
        fieldNames.add(DFN.PrjEmpMdPfm.prj);
        List<TePrjEmpMdPfm> result = pfmDao.findByFieldAndConds(conds, fieldNames);

        PfmMailVo mailVo;
        List<PfmMailVo> groupRespMailVos = new ArrayList<>();
        List<PfmMailVo> pmMailVos = new ArrayList<>();
        List<PfmMailVo> asstMailVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            for (TePrjEmpMdPfm pfm : result) {
                List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
                TeIdNameCn prj = pfm.getPrj();
                if (CollectionUtils.isEmpty(toEvalUsers) || Objects.isNull(prj) || Objects.isNull(prj.getCid())) {
                    continue;
                }
                for (TePrjEmpMdPfm2ToEvalUsers TePrjEmpMdPfm2ToEvalUsers : toEvalUsers) {
                    TeIdNameCn role = TePrjEmpMdPfm2ToEvalUsers.getRole();
                    TeUser user = TePrjEmpMdPfm2ToEvalUsers.getUser();
                    if (Objects.isNull(role) || Objects.isNull(user)) {
                        continue;
                    }
                    // 项目经理
                    if (PrjMdConstant.ROLE_PRJ_PM.getCid().equals(role.getCid())) {
                        mailVo = new PfmMailVo();
                        mailVo.setPrj(prj);
                        mailVo.setPm(user);
                        for (TePrjEmpMdPfm2ToEvalUsers evalUser : toEvalUsers) {
                            if (PrjMdConstant.ROLE_PRJ_GROUP_RESP.getCid().equals(evalUser.getRole().getCid())) {
                                mailVo.setGroupResp(evalUser.getUser());
                                mailVo.setPrjGroup(pfm.getPrjGroup());
                            }
                        }
                        pmMailVos.add(mailVo);
                    }
                    // 项目助理
                    if (PrjMdConstant.ROLE_PRJ_ASST.getCid().equals(role.getCid())) {
                        mailVo = new PfmMailVo();
                        mailVo.setPrj(prj);
                        mailVo.setPmAsst(user);

                        List<ItfSysDefRoleUser> pms = prjIdToPms.get(prj.getCid());
                        if (CollectionUtils.isNotEmpty(pms)) {
                            mailVo.setPm(pms.get(0).getRoleUser());
                        }

                        if (Objects.nonNull(pfm.getPrjGroup()) && Objects.nonNull(pfm.getPrjGroup().getCid())) {
                            List<ItfSysDefRoleUser> groupResps = groupIdToGroupResp.get(pfm.getPrjGroup().getCid());
                            if (CollectionUtils.isNotEmpty(groupResps)) {
                                mailVo.setGroupResp(groupResps.get(0).getRoleUser());
                            }
                        }
                        asstMailVos.add(mailVo);
                    }
                    // 项目组长
                    if (PrjMdConstant.ROLE_PRJ_GROUP_RESP.getCid().equals(role.getCid())) {
                        mailVo = new PfmMailVo();
                        mailVo.setPrj(prj);
                        mailVo.setPrjGroup(pfm.getPrjGroup());
                        mailVo.setGroupResp(user);

                        List<ItfSysDefRoleUser> pms = prjIdToPms.get(prj.getCid());
                        if (CollectionUtils.isNotEmpty(pms)) {
                            mailVo.setPm(pms.get(0).getRoleUser());
                        }

                        groupRespMailVos.add(mailVo);
                    }

                }
            }
        }

        // 项目经理 jobCode -> 数据
        Map<String, List<PfmMailVo>> pmJobCodeToData = pmMailVos.stream()
                .filter(item -> Objects.nonNull(item.getPm()) && Objects.nonNull(item.getPm().getJobCode()))
                .distinct().collect(Collectors.groupingBy(item -> item.getPm().getJobCode()));
        Set<String> pmUserIds = pmJobCodeToData.keySet();
        Set<String> toEvalUserJobCodes = new HashSet<>(pmUserIds);

        // 项目助理 jobCode -> 数据
        Map<String, List<PfmMailVo>> asstJobCodeToData = asstMailVos.stream()
                .filter(item -> Objects.nonNull(item.getPmAsst()) && Objects.nonNull(item.getPmAsst().getJobCode()))
                .collect(Collectors.groupingBy(item -> item.getPmAsst().getJobCode()));
        Set<String> asstUserIds = asstJobCodeToData.keySet();
        toEvalUserJobCodes.addAll(asstUserIds);

        // 项目组长 jobCode -> 数据
        Map<String, List<PfmMailVo>> groupRespJobCodeToData = groupRespMailVos.stream()
                .filter(item -> Objects.nonNull(item.getGroupResp())
                        && Objects.nonNull(item.getGroupResp().getJobCode()))
                .collect(Collectors.groupingBy(item -> item.getGroupResp().getJobCode()));
        Set<String> groupRespUserIds = groupRespJobCodeToData.keySet();
        toEvalUserJobCodes.addAll(groupRespUserIds);

        if (CollectionUtils.isEmpty(toEvalUserJobCodes)) {
            return;
        }

        List<ItfAiEmp> userList = itfAiEmpFeignClient.listUserByEmployeeNumber(new ArrayList<>(toEvalUserJobCodes))
                .getData(true);
        Map<String, String> jobCodeToMail = new HashMap<>();
        Map<String, String> jobCodeToName = new HashMap<>();
        for (ItfAiEmp user : userList) {
            String jobCode = user.getEmployeeNumber();
            String mailBox = user.getEmailAddress();
            String userName = user.getLastName();
            if (StringUtil.isEmpty(jobCode) || StringUtil.isEmpty(mailBox) || StringUtil.isEmpty(userName)) {
                continue;
            }
            jobCodeToMail.put(jobCode, mailBox);
            jobCodeToName.put(jobCode, userName);
        }

        // 标题
        Date evalDate = parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(evalDate);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        String evalMonth = year + "年" + month + "月";
        String mailSubject = evalMonth + "项目成员绩效考评启动通知";
        String closeDayOfEvalYm = this.getCloseDayOfEvalYm(bgId, evalYm);

        calendar.setTime(new Date());
        String initDay = calendar.get(Calendar.YEAR) + "年" + (calendar.get(Calendar.MONTH) + 1) + "月"
                + (calendar.get(Calendar.DATE) + 1) + "日";
        String[] split = closeDayOfEvalYm.split("-");
        String closeDay = split[0] + "年" + split[1] + "月" + split[2] + "日";

        List<ItfMailInfo> mailInfos = new ArrayList<>();
        for (String jobCode : toEvalUserJobCodes) {
            // 是项目经理的数据
            List<PfmMailVo> pmMailVoList = pmJobCodeToData.get(jobCode);
            Map<String, List<PfmMailVo>> prjCodeToPmData = new HashMap<>();
            if (CollectionUtils.isNotEmpty(pmMailVoList)) {
                prjCodeToPmData = pmMailVoList.stream().filter(item -> Objects.nonNull(item.getPrj()))
                        .collect(Collectors.groupingBy(item -> item.getPrj().getCodeName()));
            }
            // 是项目助理的数据
            List<PfmMailVo> asstMailVoList = asstJobCodeToData.get(jobCode);
            Map<String, List<PfmMailVo>> prjCodeToAsstData = new HashMap<>();
            if (CollectionUtils.isNotEmpty(asstMailVoList)) {
                prjCodeToAsstData = asstMailVoList.stream().filter(item -> Objects.nonNull(item.getPrj()))
                        .collect(Collectors.groupingBy(item -> item.getPrj().getCodeName()));
            }
            // 是项目组长的数据
            List<PfmMailVo> groupRespMailVoList = groupRespJobCodeToData.get(jobCode);
            Map<String, List<PfmMailVo>> prjCodeToGpData = new HashMap<>();
            if (CollectionUtils.isNotEmpty(groupRespMailVoList)) {
                prjCodeToGpData = groupRespMailVoList.stream().filter(item -> Objects.nonNull(item.getPrj()))
                        .collect(Collectors.groupingBy(item -> item.getPrj().getCodeName()));
            }
            if (prjCodeToPmData.isEmpty() && prjCodeToAsstData.isEmpty() && prjCodeToGpData.isEmpty()) {
                return;
            }

            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject(mailSubject);
            mailInfo.setSubsystem(DMPSubsystem.RMS.getCode());
            mailInfo.setToList(Collections.singletonList(jobCodeToMail.get(jobCode)));
            // freeMark要展示的数据
            Map<String, Object> mailContentMap = new HashMap<>();
            mailContentMap.put("evalMonth", evalMonth);
            mailContentMap.put("endDate", closeDayOfEvalYm);
            mailContentMap.put("initDay", initDay);
            mailContentMap.put("closeDay", closeDay);

            mailContentMap.put("name", jobCodeToName.get(jobCode));
            mailContentMap.put("prjCodeToPmData", prjCodeToPmData);
            mailContentMap.put("prjCodeToAsstData", prjCodeToAsstData);
            mailContentMap.put("prjCodeToGpData", prjCodeToGpData);
            String mailContent = FreemakerUtil.process(mailContentMap, "mailTemplate_PfmStartNotice.html");
            mailInfo.setContent(mailContent);
            mailInfos.add(mailInfo);

            // //todo 用于测试， 上线去除
            // if (mailInfos.size() == 5) {
            // break;
            // }
        }
        if (CollectionUtils.isNotEmpty(mailInfos)) {
            mailFeignClient.sendMails(mailInfos);
        }

    }

    private String getCloseDayOfEvalYm(String bgId, String evalYm) {
        Date closeMonthOfEvalYm = DateUtil.getDeltaDate(parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT), Calendar.MONTH,
                2);
        String closeMonthOfEvalYmStr = DateUtil.format(closeMonthOfEvalYm, DateUtil.DATE_MONTH_FOTMAT2);
        String pfmCloseTime = getPfmCloseTime(bgId);
        String[] split = pfmCloseTime.split(",");
        String num = split[0];
        List<TeSysCal> workDays = sysCalDao.getWorkDays(closeMonthOfEvalYmStr);
        if (CollectionUtils.isEmpty(workDays)) {
            throw BusinessException.initExc("获取关闭月工作日为空!");
        }
        TeSysCal teSysCal = workDays.get(Integer.parseInt(num) - 1);
        return teSysCal.getDate();
    }

    @Override
    public PageBean<EvalDataVo> listMyToEvalPrj(String ym, Integer pageIndex, Integer pageSize, TeSysUser loginUser) {
        ObjectId userId = loginUser.getId();
        String evalYm = ym.replace("-", "");

        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where(DFN.PrjEmpMdPfm.isValid.n()).is(true)
                .and(DFN.PrjEmpMdPfm.ym.n()).is(evalYm)
                .and(DFN.PrjEmpMdPfm.isLocked.n()).is(false)
                .and(DFN.PrjEmpMdPfm.status.dot(DbFieldName.common_cid).n()).is(PrjMdConstant.TODO.getCid())
                .and(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DbFieldName.common_userId)).n())
                .is(userId);
        MatchOperation match = Aggregation.match(criteria);
        GroupOperation group = Aggregation.group("prj.cid");
        list.add(match);
        list.add(group);
        CountOperation count = Aggregation.count().as("count");
        SkipOperation skip = Aggregation.skip((long) pageIndex * pageSize);
        LimitOperation limit = Aggregation.limit(pageSize);
        list.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        Aggregation dateAgg = Aggregation.newAggregation(list)
                .withOptions(new AggregationOptions.Builder().allowDiskUse(true).build());
        List<Map> dataList = mongoTemplate.aggregate(dateAgg, "prjEmpMdPfm", Map.class).getMappedResults();

        PageBean<EvalDataVo> pageBean = new PageBean<>();
        if (CollectionUtils.isEmpty(dataList)) {
            return pageBean;
        }
        List<Map<String, Integer>> count1 = (List<Map<String, Integer>>) dataList.get(0).get("count");
        if (ObjectUtil.isEmpty(count1)) {
            return pageBean;
        }
        Integer total = count1.get(0).get("count");
        pageBean.setCount(Long.valueOf(total));

        List<Map<String, Object>> data1 = (List<Map<String, Object>>) dataList.get(0).get("data");
        List<ObjectId> prjIds = new ArrayList<>();
        for (Map map : data1) {
            ObjectId prjId = (ObjectId) map.get("_id");
            prjIds.add(prjId);
        }

        List<ItfAiPrj> prjList = prjFeignClient.queryPrjByPrjIds(prjIds).getData(true);
        Map<ObjectId, ItfAiPrj> idToPrj = prjList.stream()
                .filter(item -> Objects.nonNull(item.getDmpPrj()) && Objects.nonNull(item.getDmpPrj().getCid()))
                .collect(Collectors.toMap(item -> item.getDmpPrj().getCid(), s -> s, (v1, v2) -> v2));
        List<String> pmJobCodes = prjList.stream().map(ItfAiPrj::getPmEmployeeNumber).distinct()
                .collect(Collectors.toList());
        List<ItfAiEmp> pmList = itfAiEmpFeignClient.listUserByEmployeeNumber(pmJobCodes).getData(true);
        Map<String, TeUser> jobCodeToUSer = pmList.stream()
                .collect(Collectors.toMap(ItfAiEmp::getEmployeeNumber, ItfAiEmp::getDmpUser, (v1, v2) -> v2));

        List<EvalDataVo> result = new ArrayList<>();
        EvalDataVo resultVo;
        for (ObjectId prjId : prjIds) {
            ItfAiPrj aiPrj = idToPrj.get(prjId);
            if (Objects.isNull(aiPrj)) {
                continue;
            }
            resultVo = new EvalDataVo();
            resultVo.setPrj(aiPrj.getDmpPrj());
            resultVo.setYm(ym);
            String pmEmployeeNumber = aiPrj.getPmEmployeeNumber();
            if (StringUtil.isNotEmpty(pmEmployeeNumber)) {
                resultVo.setPm(jobCodeToUSer.get(pmEmployeeNumber));
            }
            resultVo.setBuId(aiPrj.getSbuId());
            resultVo.setCcId(aiPrj.getCostCenterId());
            result.add(resultVo);
        }
        pageBean.setObjectList(result);
        pageBean.setPageSize(pageSize);
        pageBean.setPageIndex(pageIndex);
        return pageBean;
    }

    @Override
    public List<EvalDataVo> listEvalPrj(String ym, TeSysUser loginUser) {
        ObjectId userId = loginUser.getId();
        String evalYm = ym.replace("-", "");

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(
                new DC_E(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DbFieldName.common_userId)), userId));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        List<TeIdNameCn> prjList = data.stream().map(TePrjEmpMdPfm::getPrj).distinct().collect(Collectors.toList());
        List<EvalDataVo> result = new ArrayList<>();
        EvalDataVo vo;
        for (TeIdNameCn prj : prjList) {
            vo = new EvalDataVo();
            vo.setPrj(prj);
            result.add(vo);
        }
        return result;
    }

    @Override
    public EvalDataVo queryEvalInfo(String ym, String prjCode) {
        String evalYm = ym.replace("-", "");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.PrjEmpMdPfm.prj);
        fieldNames.add(DFN.PrjEmpMdPfm.md);
        fieldNames.add(DFN.PrjEmpMdPfm.pfmValue);
        fieldNames.add(DFN.PrjEmpMdPfm.employeeType);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);

        int employeeNum = 0;
        int employeeMd = 0;
        int employeeGteFourPointFiveNum = 0;
        int employeeGteFourNum = 0;

        int outSourceNum = 0;
        int outSourceMd = 0;
        int outSourceGteFourPointFiveNum = 0;
        int outSourceGteFourNum = 0;

        int traineeNum = 0;
        int traineeMd = 0;
        int traineeGteFourPointFiveNum = 0;
        int traineeGteFourNum = 0;
        for (TePrjEmpMdPfm pfm : data) {
            String employeeType = pfm.getEmployeeType();
            Integer md = pfm.getMd();
            Double pfmValue = pfm.getPfmValue() == null ? 0d : pfm.getPfmValue();
            // 正式员工
            if ("Employee".equals(employeeType)) {
                employeeNum++;
                employeeMd += md;
                if (pfmValue.compareTo(4.5d) >= 0) {
                    employeeGteFourPointFiveNum++;
                }
                if (pfmValue.compareTo(4.0d) >= 0) {
                    employeeGteFourNum++;
                }
            }
            // 外包员工
            if ("Outsource".equals(employeeType) || "Outsource1".equals(employeeType)) {
                outSourceNum++;
                outSourceMd += md;
                if (pfmValue.compareTo(4.5d) >= 0) {
                    outSourceGteFourPointFiveNum++;
                }
                if (pfmValue.compareTo(4.0d) >= 0) {
                    outSourceGteFourNum++;
                }
            }
            // 实习生
            if ("Trainee".equals(employeeType)) {
                traineeNum++;
                traineeMd += md;
                if (pfmValue.compareTo(4.5d) >= 0) {
                    traineeGteFourPointFiveNum++;
                }
                if (pfmValue.compareTo(4.0d) >= 0) {
                    traineeGteFourNum++;
                }
            }
        }

        EvalDataVo resultVo = new EvalDataVo();
        // 正式员工数据
        resultVo.setEmployeeNum(employeeNum);
        resultVo.setEmployeeMd(employeeMd);
        Double employeeGteFourPointFive = employeeNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(employeeGteFourPointFiveNum), employeeNum, 2);
        Double employeeGteFour = employeeNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(employeeGteFourNum), employeeNum, 2);
        resultVo.setEmployeeGteFourPointFive(employeeGteFourPointFive);
        resultVo.setEmployeeGteFour(employeeGteFour);
        // 外包员工数据
        resultVo.setOutSourceNum(outSourceNum);
        resultVo.setOutSourceMd(outSourceMd);
        Double outSourceGteFourPointFive = outSourceNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(outSourceGteFourPointFiveNum), outSourceNum, 2);
        Double outSourceGteFour = outSourceNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(outSourceGteFourNum), outSourceNum, 2);
        resultVo.setOutSourceGteFourPointFive(outSourceGteFourPointFive);
        resultVo.setOutSourceGteFour(outSourceGteFour);
        // 实习生数据
        resultVo.setTraineeNum(traineeNum);
        resultVo.setTraineeMd(traineeMd);
        Double traineeGteFourPointFive = traineeNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(traineeGteFourPointFiveNum), traineeNum, 2);
        Double traineeGteFour = traineeNum == 0 ? 0
                : BigDecimalUtils.divideDouble(Double.valueOf(traineeGteFourNum), traineeNum, 2);
        resultVo.setTraineeGteFourPointFive(traineeGteFourPointFive);
        resultVo.setTraineeGteFour(traineeGteFour);
        return resultVo;
    }

    // 判断是否是考评人(项目经理、项目助理）
    public Boolean isEvaluator(String prjCode, ObjectId userId) {
        if (StringUtil.isEmpty(prjCode) || Objects.isNull(userId)) {
            return false;
        }
        List<ObjectId> roleIds = Arrays.asList(PrjMdConstant.ROLE_PRJ_PM.getCid(),
                PrjMdConstant.ROLE_PRJ_ASST.getCid());
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        org.bson.Document toEvalUsers = new org.bson.Document();
        toEvalUsers.put(DFN.PrjEmpMdPfm.role.dot(DFN.common_cid).n(), new org.bson.Document("$in", roleIds));
        toEvalUsers.put(DFN.PrjEmpMdPfm.user.dot(DbFieldName.common_userId).n(), userId);
        conds.add(new DC_E(DFN.PrjEmpMdPfm.toEvalUsers, new org.bson.Document("$elemMatch", toEvalUsers)));
        long l = pfmDao.countByConds(conds);
        // 如果有，则按项目经理角色处理，否则按项目组长角色处理。
        return l > 0;
    }

    @Override
    public PageBean<EvalDataVo> ListPrjEvalData(EvalDataVo vo, TeSysUser loginUser) {
        PageBean<EvalDataVo> pageBean = new PageBean<>();
        ObjectId userId = loginUser.getId();
        // 年月
        String ym = vo.getYm();
        if (StringUtil.isEmpty(ym)) {
            throw BusinessException.initExc("年月入参不可为空");
        }
        String evalYm = ym.replace("-", "");
        // 项目
        String prjCode = vo.getPrjCode();
        if (StringUtil.isEmpty(prjCode)) {
            throw BusinessException.initExc("项目编码不可为空");
        }
        // 员工类型
        List<String> empTypeList = vo.getEmpTypeList();
        Integer pageIndex = vo.getPageIndex();
        Integer pageSize = vo.getPageSize();
        // 判断是考评人还是项目组长
        Boolean isEvaluator = isEvaluator(prjCode, userId);

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.employeeType, empTypeList));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        // 角色查询
        if (!Objects.isNull(vo.getRoleId())) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.role.dot(DFN.common_cid), vo.getRoleId()));
        }
        boolean fromAdminView = Boolean.TRUE.equals(vo.getFromAdminView());
        if (!isEvaluator && !fromAdminView) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
            conds.add(new DC_E(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DFN.common_userId)), userId));
        }
        // 姓名、工号查询
        if (StringUtil.isNotEmpty(vo.getEmpName()) && StringUtil.isEmpty(vo.getJobCode())) {
            conds.add(new DC_R(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userName), vo.getEmpName()));
        } else if (StringUtil.isNotEmpty(vo.getJobCode()) && StringUtil.isEmpty(vo.getEmpName())) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), vo.getJobCode()));
        } else if (StringUtil.isNotEmpty(vo.getJobCode()) && StringUtil.isNotEmpty(vo.getEmpName())) {
            List<IDbCondition> orCond = new ArrayList<>();
            orCond.add(new DC_L(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userName), vo.getEmpName()));
            orCond.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), vo.getJobCode()));
            conds.add(new DC_OR(orCond));
        }
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);

        // 获取上月评分
        Date date = parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT);
        Date deltaDate = DateUtil.getDeltaDate(date, Calendar.MONTH, -1);
        String provMonth = DateUtil.format(deltaDate, DateUtil.DATE_MONTH_FOTMAT);
        List<IDbCondition> prvMonConds = new ArrayList<>();
        prvMonConds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        prvMonConds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, true));
        prvMonConds.add(new DC_E(DFN.PrjEmpMdPfm.ym, provMonth));
        prvMonConds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        prvMonConds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.IS_CLOSED.getCid()));
        prvMonConds.add(new DC_I<>(DFN.PrjEmpMdPfm.employeeType, empTypeList));
        List<TePrjEmpMdPfm> provData = pfmDao.findByFieldAndConds(prvMonConds, null);

        Map<String, Double> provJobCodePrjCodeToPfmValue = new HashMap<>();
        for (TePrjEmpMdPfm mdPfm : provData) {
            TeUser emp = mdPfm.getEmp();
            TeIdNameCn prj = mdPfm.getPrj();
            if (Objects.isNull(emp) || Objects.isNull(prj)) {
                continue;
            }
            provJobCodePrjCodeToPfmValue.put(emp.getJobCode() + "_" + prj.getCodeName(), mdPfm.getPfmValue());
        }
        // 获取工时内容
        QueryContentVo queryContentVo = new QueryContentVo();
        queryContentVo.setPrjCode(prjCode);
        queryContentVo.setYearMonth(ym);
        List<RmsWorkRptVo> contentData = rmsEmpDateFeignClient.queryPrjFillContent(queryContentVo).getData(true);
        Map<String, String> jobCodePrjCodeToContent = new HashMap<>();
        for (RmsWorkRptVo dataVo : contentData) {
            TeUser emp = dataVo.getUser();
            TeIdNameCn prj = dataVo.getPrj();
            if (Objects.isNull(emp) || Objects.isNull(prj)) {
                continue;
            }
            jobCodePrjCodeToContent.put(emp.getJobCode() + "_" + prj.getCodeName(), dataVo.getDesc());
        }

        List<EvalDataVo> list = new ArrayList<>();
        EvalDataVo resultVo;
        for (TePrjEmpMdPfm pfm : data) {
            resultVo = new EvalDataVo();
            TeIdNameCn status = pfm.getStatus();

            TeUser emp = pfm.getEmp();
            if (Objects.isNull(emp)) {
                continue;
            }
            resultVo.setId(pfm.getId());
            resultVo.setEmpName(emp.getUserName());
            resultVo.setJobCode(emp.getJobCode());
            TeIdNameCn role = pfm.getRole();
            if (Objects.nonNull(role)) {
                resultVo.setRole(role.getName());
            }

            resultVo.setEmpType(pfm.getEmployeeType());
            resultVo.setMd(pfm.getMd());
            resultVo.setOprtInfo(pfm.getOprtInfo());
            resultVo.setIsEvaluator(isEvaluator);
            TeIdNameCn prj = pfm.getPrj();
            String key = emp.getJobCode() + "_" + prj.getCodeName();
            Double provMonthPfmValue = provJobCodePrjCodeToPfmValue.get(key);
            resultVo.setProvMonthPfmValue(provMonthPfmValue);
            resultVo.setDesc(jobCodePrjCodeToContent.get(key));
            if (isEvaluator) {
                // 如果是考评人（项目经理、项目助理）
                // 如果status.cid不为ObjectId("668cfc937dbb511498817e7a")即已关闭，则可以编辑。
                ObjectId statusId = status.getCid();
                resultVo.setCanEdit(Objects.nonNull(statusId) && !statusId.equals(PrjMdConstant.IS_CLOSED.getCid()));
            } else {
                // 如果是项目组长
                resultVo.setCanEdit(true);
                List<TePrjEmpMdPfm2OprtInfo> oprtInfo = pfm.getOprtInfo();
                for (TePrjEmpMdPfm2OprtInfo item : oprtInfo) {
                    if (PrjMdConstant.PM_SCORE.equals(item.getOprtType())) {
                        resultVo.setCanEdit(false);
                    }
                }
            }
            // 获取项目经理、项目组长打分
            List<TePrjEmpMdPfm2OprtInfo> oprtInfo = pfm.getOprtInfo();
            if (CollectionUtils.isNotEmpty(oprtInfo)) {
                List<TePrjEmpMdPfm2OprtInfo> pmOprtHist = new ArrayList<>();
                List<TePrjEmpMdPfm2OprtInfo> pGOprtHist = new ArrayList<>();

                for (TePrjEmpMdPfm2OprtInfo item : oprtInfo) {
                    if (PrjMdConstant.PG_SCORE.equals(item.getOprtType())) {
                        pGOprtHist.add(item);
                    } else if (PrjMdConstant.PM_SCORE.equals(item.getOprtType())
                            || PrjMdConstant.PM_ADJUST.equals(item.getOprtType())) {
                        pmOprtHist.add(item);
                    }
                }
                // 显示的项目经理评分
                if (CollectionUtils.isNotEmpty(pmOprtHist)) {
                    pmOprtHist = pmOprtHist.stream()
                            .sorted(Comparator.comparing(TePrjEmpMdPfm2OprtInfo::getOprtTime).reversed())
                            .collect(Collectors.toList());
                    resultVo.setPmScore(pmOprtHist.get(0));
                } else {
                    TePrjEmpMdPfm2OprtInfo pmScore = new TePrjEmpMdPfm2OprtInfo();
                    pmScore.setNewValue(pfm.getPfmValue());
                    resultVo.setPmScore(pmScore);
                }
                // 显示的项目组长评分
                if (CollectionUtils.isNotEmpty(pGOprtHist)) {
                    pGOprtHist = pGOprtHist.stream()
                            .sorted(Comparator.comparing(TePrjEmpMdPfm2OprtInfo::getOprtTime).reversed())
                            .collect(Collectors.toList());
                    resultVo.setPgScore(pGOprtHist.get(0));
                }
            }
            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
            for (TePrjEmpMdPfm2ToEvalUsers evalUser : toEvalUsers) {
                if (PrjMdConstant.PRJROLE_PM.equals(evalUser.getRole().getCid())) {
                    TeUser user = evalUser.getUser();
                    resultVo.setEvalUser(user.getUserName() + "/" + user.getLoginName());
                    resultVo.setEvalUserId(user.getUserId());
                }
            }
            list.add(resultVo);
        }

        List<EvalDataVo> canEdit = list.stream().filter(EvalDataVo::getCanEdit).collect(Collectors.toList());
        List<EvalDataVo> notCanEdit = list.stream().filter(item -> !item.getCanEdit()).collect(Collectors.toList());
        List<EvalDataVo> result = new ArrayList<>();
        result.addAll(canEdit);
        result.addAll(notCanEdit);
        int count = result.size();
        int endIndex = Math.min((pageIndex + 1) * pageSize, count);
        List<EvalDataVo> pageResult = result.subList(pageIndex * pageSize, endIndex);

        pageBean.setCount((long) count);
        pageBean.setObjectList(pageResult);
        pageBean.setPageIndex(pageIndex);
        pageBean.setPageSize(pageSize);
        return pageBean;
    }

    @Override
    public void updateEvalData(EvalDataVo vo, TeSysUser loginUser) {
        String prjCode = vo.getPrjCode();
        String jobCode = vo.getJobCode();
        String ym = vo.getYm();
        String editDesc = vo.getEditDesc();
        Double newValue = vo.getNewValue();
        Date date = new Date();
        if (StringUtil.isEmpty(ym)) {
            throw BusinessException.initExc("年月入参不可为空");
        }
        ym = ym.replace("-", "");
        if (newValue == null && StringUtil.isEmpty(editDesc)) {
            return;
        }
        // 校验评分
        if (newValue != null && (newValue < 0 || 5 < newValue)) {
            throw BusinessException.initExc("评分不可小于0或大于5.0");
        }

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, ym));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCode));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        // 操作人
        TeUser oprtUser = new TeUser();
        oprtUser.setUserName(loginUser.getUserName());
        oprtUser.setJobCode(loginUser.getJobCode());
        oprtUser.setUserId(loginUser.getId());
        oprtUser.setLoginName(loginUser.getLoginName());

        TePrjEmpMdPfm mdPfm = data.get(0);
        List<TePrjEmpMdPfm2OprtInfo> oprtInfo = mdPfm.getOprtInfo();
        if (CollectionUtils.isEmpty(oprtInfo)) {
            oprtInfo = new ArrayList<>();
        }

        TePrjEmpMdPfm2OprtInfo TePrjEmpMdPfm2OprtInfo = new TePrjEmpMdPfm2OprtInfo();
        ObjectId userId = loginUser.getId();
        Boolean isEvaluator = isEvaluator(prjCode, userId);
        if (isEvaluator) {
            TePrjEmpMdPfm2OprtInfo.setOprtType(PrjMdConstant.PM_SCORE);
        } else {
            TePrjEmpMdPfm2OprtInfo.setOprtType(PrjMdConstant.PG_SCORE);
        }
        TePrjEmpMdPfm2OprtInfo.setOldValue(mdPfm.getPfmValue());
        if (newValue != null) {
            TePrjEmpMdPfm2OprtInfo.setNewValue(newValue);
        }
        if (StringUtil.isNotEmpty(editDesc)) {
            TePrjEmpMdPfm2OprtInfo.setOprtDesc(editDesc);
        }
        TePrjEmpMdPfm2OprtInfo.setOprtUser(oprtUser);
        TePrjEmpMdPfm2OprtInfo.setOprtTime(date);
        oprtInfo.add(TePrjEmpMdPfm2OprtInfo);

        // 更新
        List<IDbCondition> updateConds = new ArrayList<>();
        updateConds.add(new DC_E(DFN.common__id, mdPfm.getId()));
        List<UpdataData> updataDataList = new ArrayList<>();
        if (newValue != null) {
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.pfmValue, newValue));
        }
        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdUser, oprtUser));
        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdTime, date));
        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.oprtInfo, oprtInfo));
        pfmDao.updateByConds(updateConds, updataDataList);
    }

    @Override
    public void updateEvalUserToBeEvaluated(String ym, String prjCode, String jobCode, TeSysUser loginUser) {
        List<ItfAiEmp> empList = itfAiEmpFeignClient.listUserByEmployeeNumber(Collections.singletonList(jobCode))
                .getData(true);
        if (CollectionUtils.isEmpty(empList)) {
            throw BusinessException.initExc("新考评人不存在！");
        }
        TeUser newEvalUser = empList.get(0).getDmpUser();
        TePrjEmpMdPfm2ToEvalUsers addEvalUser = new TePrjEmpMdPfm2ToEvalUsers();
        addEvalUser.setUser(newEvalUser);
        addEvalUser.setRole(PrjMdConstant.ROLE_PRJ_PM);

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.ym, ym.replace("-", "")));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.prj.dot(DFN.common_cn), prjCode));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.PrjEmpMdPfm.toEvalUsers);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);

        updateEvalUser(data, addEvalUser);
    }

    private void updateEvalUser(List<TePrjEmpMdPfm> data, TePrjEmpMdPfm2ToEvalUsers addEvalUser) {
        List<List<IDbCondition>> condAll = new ArrayList<>();
        List<List<UpdataData>> updateAll = new ArrayList<>();
        List<IDbCondition> updateConds;
        List<UpdataData> updateDatas;

        for (TePrjEmpMdPfm pfm : data) {
            updateConds = new ArrayList<>();
            updateConds.add(new DC_E(DbFieldName.common__id, pfm.getId()));
            condAll.add(updateConds);

            updateDatas = new ArrayList<>();
            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
            List<TePrjEmpMdPfm2ToEvalUsers> list = new ArrayList<>();
            list.add(addEvalUser);
            for (TePrjEmpMdPfm2ToEvalUsers evalUsers : toEvalUsers) {
                TeIdNameCn role = evalUsers.getRole();
                if (PrjMdConstant.ROLE_PRJ_PM.getCid().equals(role.getCid())) {
                    continue;
                }
                list.add(evalUsers);
            }
            updateDatas.add(new UpdataData(DbFieldName.PrjEmpMdPfm.toEvalUsers, list));
            updateAll.add(updateDatas);
        }

        if (CollectionUtils.isNotEmpty(condAll)) {
            pfmDao.batchUpdate(condAll, updateAll, false);
        }
    }

    @Override
    public Boolean canCommitted(String ym, String prjCode, TeSysUser loginUser) {
        // 如果是项目经理，则状态是待考评、组长提交时可以进行提交; 如果是组长则只能在状态是待考评时才能进行提交
        String evalYm = ym.replace("-", "");
        PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true).isLocked(false).ym(evalYm)
                .prjCodeList(Collections.singletonList(prjCode)).build();
        List<TePrjEmpMdPfm> data = listMdPfms(query);
        if (CollectionUtils.isEmpty(data)) {
            return false;
        }

        Boolean isEvalator = isEvaluator(prjCode, loginUser.getId());
        TePrjEmpMdPfm pfm = data.get(0);
        TeIdNameCn status = pfm.getStatus();

        if (isEvalator && (PrjMdConstant.TODO.getCid().equals(status.getCid())
                || PrjMdConstant.GROUP_RESP_IS_DONE.getCid().equals(status.getCid()))) {
            return true;
        } else
            return !isEvalator && PrjMdConstant.TODO.getCid().equals(status.getCid());
    }

    @Override
    public void commitEval(String ym, String prjCode, TeSysUser loginUser) {
        ObjectId userId = loginUser.getId();
        String evalYm = ym.replace("-", "");
        String errMsg = checkRatioIsReasonable(evalYm, prjCode);
        if (StringUtil.isNotEmpty(errMsg)) {
            throw BusinessException.initExc(errMsg);
        }

        // 判断是考评人还是项目组长
        Boolean isEvaluator = isEvaluator(prjCode, userId);
        if (isEvaluator) {
            pmCommitEval(prjCode, evalYm, loginUser);
        } else {
            groupRespCommitEval(prjCode, evalYm, loginUser);
        }

    }

    // 项目经理、项目助理提交
    private void pmCommitEval(String prjCode, String evalYm, TeSysUser loginUser) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));

        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.PrjEmpMdPfm.emp);
        fieldNames.add(DFN.PrjEmpMdPfm.pfmValue);
        fieldNames.add(DFN.PrjEmpMdPfm.oprtInfo);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        // 如果有人未打分，则不可以提交
        for (TePrjEmpMdPfm pfm : data) {
            if (pfm.getPfmValue() == null) {
                throw BusinessException.initExc("该项目有员工未进行打分，不可以进行提交");
            }
        }

        Map<String, List<TePrjEmpMdPfm2OprtInfo>> jobCodeToOprtInfo = data.stream()
                .filter(item -> Objects.nonNull(item.getEmp()) && Objects.nonNull(item.getOprtInfo()))
                .collect(Collectors.toMap(item -> item.getEmp().getJobCode(), TePrjEmpMdPfm::getOprtInfo,
                        (v1, v2) -> v2));

        List<IDbCondition> batchUpdateConds;
        TeUser oprtUser = new TeUser();
        oprtUser.setUserName(loginUser.getUserName());
        oprtUser.setJobCode(loginUser.getJobCode());
        oprtUser.setUserId(loginUser.getId());
        oprtUser.setLoginName(loginUser.getLoginName());
        Date date = new Date();
        List<List<IDbCondition>> queryConditions = new ArrayList<>();
        List<List<UpdataData>> updateConditions = new ArrayList<>();
        for (String jobCode : jobCodeToOprtInfo.keySet()) {
            batchUpdateConds = new ArrayList<>(conds);
            batchUpdateConds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCode));
            List<TePrjEmpMdPfm2OprtInfo> oprtInfo = jobCodeToOprtInfo.getOrDefault(jobCode, new ArrayList<>());
            oprtInfo.add(TePrjEmpMdPfm2OprtInfo.builder().oprtType(PrjMdConstant.PM_COMMIT).oldValue(null)
                    .oprtUser(oprtUser).oprtTime(date).build());

            List<UpdataData> updataDataList = new ArrayList<>();
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.status, PrjMdConstant.PM_IS_DONE));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdUser, oprtUser));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdTime, date));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.oprtInfo, oprtInfo));
            queryConditions.add(batchUpdateConds);
            updateConditions.add(updataDataList);
        }
        if (CollectionUtils.isNotEmpty(queryConditions)) {
            pfmDao.batchUpdate(queryConditions, updateConditions, false);
        }

        // 发送邮件给每个员工
        String bgId = loginUser.getBgId();
        sendPmCommitMail(evalYm, prjCode, bgId);

    }

    private void sendPmCommitMail(String evalYm, String prjCode, String bgId) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.common_cn), prjCode));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.PM_IS_DONE.getCid()));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        List<String> jobCodeList = data.stream()
                .filter(item -> Objects.nonNull(item.getEmp()) && Objects.nonNull(item.getEmp().getJobCode()))
                .map(item -> item.getEmp().getJobCode()).distinct().collect(Collectors.toList());
        List<ItfAiEmp> empList = itfAiEmpFeignClient.listUserByEmployeeNumber(jobCodeList).getData(true);
        Map<String, String> jobCodeToMail = empList.stream()
                .collect(Collectors.toMap(ItfAiEmp::getEmployeeNumber, ItfAiEmp::getEmailAddress, (v1, v2) -> v2));

        // 获取绩效关闭时间
        String pfmCloseDay = "";
        String pfmCloseTime = getPfmCloseTime(bgId);
        if (StringUtil.isNotEmpty(pfmCloseTime)) {
            String[] split = pfmCloseTime.split(",");
            String num = split[0];
            Date nextMonthDate = DateUtil.getDeltaDate(new Date(), Calendar.MONTH, 1);
            String formatYm = DateUtil.format(nextMonthDate, DateUtil.DATE_MONTH_FOTMAT2);
            List<TeSysCal> sysCals = sysCalDao.getWorkDays(formatYm);
            TeSysCal sysCal = sysCals.get(Integer.parseInt(num));
            pfmCloseDay = sysCal.getDate();
        }

        String mailContent = "";
        String year = evalYm.substring(0, 4);
        String month = evalYm.substring(4, 6);
        String yearMonth = year + "年" + month + "月";
        List<ItfMailInfo> mailInfoList = new ArrayList<>();
        for (TePrjEmpMdPfm pfm : data) {
            TeUser emp = pfm.getEmp();
            if (Objects.isNull(emp)) {
                continue;
            }
            Double pfmValue = pfm.getPfmValue();
            String pmName = "";
            TeUser pm = pfm.getPm();
            if (Objects.nonNull(pm)) {
                pmName = pm.getUserName();
            }
            String empName = emp.getUserName();
            String jobCode = emp.getJobCode();
            String mailBox = jobCodeToMail.get(jobCode);

            Map<String, Object> root = new HashMap<>();
            root.put("empName", empName);
            root.put("prjCode", prjCode);
            root.put("pmName", pmName);
            root.put("yearMonth", yearMonth);
            root.put("pfmValue", pfmValue);
            root.put("pfmCloseDay", pfmCloseDay);

            mailContent = FreemakerUtil.process(root, "mailTemplate_PmCommitEval.html");

            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject("项目经理提交考评成绩通知");
            mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
            mailInfo.setContent(mailContent);
            mailInfo.setToList(Collections.singletonList(mailBox));
            mailInfoList.add(mailInfo);

            // todo 测试
            if (mailInfoList.size() == 5) {
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)) {
            mailFeignClient.sendMails(mailInfoList);
        }

    }

    // 项目组长提交
    private void groupRespCommitEval(String prjCode, String evalYm, TeSysUser loginUser) {
        ObjectId userId = loginUser.getId();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DFN.common_userId)), userId));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.common__id);
        fieldNames.add(DFN.PrjEmpMdPfm.pfmValue);
        fieldNames.add(DFN.PrjEmpMdPfm.oprtInfo);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        // 如果有人未打分，则不可以提交
        for (TePrjEmpMdPfm pfm : data) {
            if (pfm.getPfmValue() == null) {
                throw BusinessException.initExc("该项目有员工未进行打分，不可以进行提交");
            }
        }

        // 操作人
        TeUser oprtUser = new TeUser();
        oprtUser.setUserName(loginUser.getUserName());
        oprtUser.setJobCode(loginUser.getJobCode());
        oprtUser.setUserId(loginUser.getId());
        oprtUser.setLoginName(loginUser.getLoginName());
        // 操作时间
        Date oprtTime = new Date();
        // 添加的操作记录
        TePrjEmpMdPfm2OprtInfo pfm2oprtInfo = new TePrjEmpMdPfm2OprtInfo();
        pfm2oprtInfo.setOprtType(PrjMdConstant.GORUP_RESP_COMMIT);
        pfm2oprtInfo.setOprtUser(oprtUser);
        pfm2oprtInfo.setOprtTime(oprtTime);

        Map<ObjectId, List<TePrjEmpMdPfm2OprtInfo>> id2OprtInfo = new HashMap<>();
        List<ObjectId> ids = data.stream().map(TePrjEmpMdPfm::getId).collect(Collectors.toList());
        for (TePrjEmpMdPfm pfm : data) {
            ObjectId id = pfm.getId();
            List<TePrjEmpMdPfm2OprtInfo> oprtInfo = pfm.getOprtInfo();
            if (CollectionUtils.isEmpty(oprtInfo)) {
                oprtInfo = new ArrayList<>();
            }
            oprtInfo.add(pfm2oprtInfo);
            id2OprtInfo.put(id, oprtInfo);
        }

        List<List<IDbCondition>> condsAll = new ArrayList<>();
        List<List<UpdataData>> updatesAll = new ArrayList<>();
        List<IDbCondition> updateConds;

        for (ObjectId id : ids) {
            updateConds = new ArrayList<>(conds);
            updateConds.add(new DC_E(DFN.common__id, id));
            condsAll.add(updateConds);

            List<UpdataData> updataDataList = new ArrayList<>();
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.status, PrjMdConstant.GROUP_RESP_IS_DONE));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdUser, oprtUser));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdTime, oprtTime));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.oprtInfo, id2OprtInfo.get(id)));
            updatesAll.add(updataDataList);
        }
        if (CollectionUtils.isNotEmpty(condsAll)) {
            pfmDao.batchUpdate(condsAll, updatesAll, false);
        }
    }

    @Override
    public String checkRatioIsReasonable(String ym, String prjCode) {
        // 1.正式员工、外包和实习生的绩效分值比例分布要求相同，且各自独立计算及控制：即4.5分以上（含）不得大于20%，4分以上（含）不得大于50%；
        // 2.如被考评员工未填写工时内容，该员工的得分只能低于3.5分（不包含3.5分）。

        EvalDataVo dataVo = queryEvalInfo(ym, prjCode);
        Double employeeGteFour = dataVo.getEmployeeGteFour();
        Double employeeGteFourPointFive = dataVo.getEmployeeGteFourPointFive();
        Double outSourceGteFour = dataVo.getOutSourceGteFour();
        Double outSourceGteFourPointFive = dataVo.getOutSourceGteFourPointFive();
        Double traineeGteFour = dataVo.getTraineeGteFour();
        Double traineeGteFourPointFive = dataVo.getTraineeGteFourPointFive();

        if (employeeGteFour > 0.5d) {
            throw BusinessException.initExc("正式员工绩效得分4分以上（含）不得大于50%");
        }
        if (employeeGteFourPointFive > 0.2d) {
            throw BusinessException.initExc("正式员工绩效得分4.5分以上（含）不得大于20%");
        }
        if (outSourceGteFour > 0.5d) {
            throw BusinessException.initExc("外包员工绩效得分4分以上（含）不得大于50%");
        }
        if (outSourceGteFourPointFive > 0.2d) {
            throw BusinessException.initExc("外包员工绩效得分4.5分以上（含）不得大于20%");
        }
        if (traineeGteFour > 0.5d) {
            throw BusinessException.initExc("实习员工绩效得分4分以上（含）不得大于50%");
        }
        if (traineeGteFourPointFive > 0.2d) {
            throw BusinessException.initExc("实习员工绩效得分4.5分以上（含）不得大于20%");
        }

        // 如被考评员工未填写工时内容，该员工的得分只能低于3.5分（不包含3.5分)
        // 获取工时内容
        Date date = parseDate(ym, DateUtil.DATE_MONTH_FOTMAT);
        String formatYm = DateUtil.format(date, DateUtil.DATE_MONTH_FOTMAT2);

        QueryContentVo queryContentVo = new QueryContentVo();
        queryContentVo.setPrjCode(prjCode);
        queryContentVo.setYearMonth(formatYm);
        List<RmsWorkRptVo> contentData = rmsEmpDateFeignClient.queryPrjFillContent(queryContentVo).getData(true);

        List<String> jobCodes = contentData.stream()
                .filter(item -> Objects.nonNull(item.getUser()) && Objects.nonNull(item.getUser().getJobCode())
                        && StringUtil.isEmpty(item.getDesc()))
                .map(item -> item.getUser().getJobCode()).distinct().collect(Collectors.toList());

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, ym));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCodes));
        List<TePrjEmpMdPfm> pfmList = pfmDao.findByFieldAndConds(conds, null);
        for (TePrjEmpMdPfm pfm : pfmList) {
            Double pfmValue = pfm.getPfmValue();
            TeUser emp = pfm.getEmp();
            if (pfmValue >= 3.5) {
                throw BusinessException.initExc(emp.getUserName() + "未填写工时内容, 得分只能低于3.5分（不包含3.5）");
            }
        }

        return "";
    }

    @Override
    public PageBean<EvalDataVo> listCommittedOrCloseEvalData(EvalDataVo vo, TeSysUser loginUser) {
        PageBean<EvalDataVo> pageBean = new PageBean<>();
        String ym = vo.getYm();
        String evalYm = ym.replace("-", "");
        Integer pageIndex = vo.getPageIndex();
        Integer pageSize = vo.getPageSize();
        if (StringUtil.isEmpty(evalYm)) {
            throw BusinessException.initExc("考评年月不可为空！");
        }
        if (pageIndex == null || pageSize == null) {
            throw BusinessException.initExc("分页入参不完整！");
        }
        String buId = vo.getBuId();
        String prjCode = vo.getPrjCode();
        String pmSearchKey = vo.getPmSearchkey();
        boolean queryCommit = vo.getQueryCommit() != null ? vo.getQueryCommit() : false;
        boolean queryClose = vo.getQueryClose() != null ? vo.getQueryClose() : false;
        ObjectId userId = loginUser.getId();

        if (StringUtil.isNotEmpty(pmSearchKey)) {
            ItfAiEmpQuery empQuery = ItfAiEmpQuery.builder().keyword(pmSearchKey).build();
            List<ItfAiEmp> empList = itfAiEmpFeignClient.queryAiEmps(empQuery).getData(true);
            if (CollectionUtils.isEmpty(empList)) {
                return pageBean;
            }
            ItfAiEmp aiEmp = empList.get(0);
            TeUser dmpUser = aiEmp.getDmpUser();
            if (Objects.nonNull(dmpUser)) {
                userId = dmpUser.getUserId();
            }
        }

        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where(DFN.PrjEmpMdPfm.isValid.n()).is(true)
                .and(DFN.PrjEmpMdPfm.ym.n()).is(evalYm)
                .and(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DbFieldName.common_userId)).n())
                .is(userId);
        if (queryCommit) {
            criteria.and(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid).n()).is(PrjMdConstant.PM_IS_DONE.getCid());
        } else if (queryClose) {
            criteria.and(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid).n()).is(PrjMdConstant.IS_CLOSED.getCid());
        }
        MatchOperation match = Aggregation.match(criteria);

        if (StringUtil.isNotEmpty(buId)) {
            criteria.and(DFN.PrjEmpMdPfm.prjBu.n()).is(buId);
        }
        if (StringUtil.isNotEmpty(prjCode)) {
            criteria.and(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName).n()).is(prjCode);
        }
        list.add(match);
        GroupOperation group = Aggregation.group("prj.cid");
        list.add(group);
        CountOperation count = Aggregation.count().as("count");
        SkipOperation skip = Aggregation.skip((long) pageIndex * pageSize);
        LimitOperation limit = Aggregation.limit(pageSize);
        list.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        Aggregation dateAgg = Aggregation.newAggregation(list)
                .withOptions(new AggregationOptions.Builder().allowDiskUse(true).build());
        List<Map> dataList = mongoTemplate.aggregate(dateAgg, "prjEmpMdPfm", Map.class).getMappedResults();

        if (CollectionUtils.isEmpty(dataList)) {
            return pageBean;
        }
        List<Map<String, Integer>> count1 = (List<Map<String, Integer>>) dataList.get(0).get("count");
        if (ObjectUtil.isEmpty(count1)) {
            return pageBean;
        }
        Integer total = count1.get(0).get("count");
        pageBean.setCount(Long.valueOf(total));

        List<Map<String, Object>> data1 = (List<Map<String, Object>>) dataList.get(0).get("data");
        List<ObjectId> prjIds = new ArrayList<>();
        for (Map map : data1) {
            ObjectId prjId = (ObjectId) map.get("_id");
            prjIds.add(prjId);
        }

        List<ItfAiPrj> prjList = prjFeignClient.queryPrjByPrjIds(prjIds).getData(true);
        Map<ObjectId, ItfAiPrj> idToPrj = prjList.stream()
                .filter(item -> Objects.nonNull(item.getDmpPrj()) && Objects.nonNull(item.getDmpPrj().getCid()))
                .collect(Collectors.toMap(item -> item.getDmpPrj().getCid(), s -> s, (v1, v2) -> v2));
        List<String> pmJobCodes = prjList.stream().map(ItfAiPrj::getPmEmployeeNumber).distinct()
                .collect(Collectors.toList());
        List<ItfAiEmp> pmList = itfAiEmpFeignClient.listUserByEmployeeNumber(pmJobCodes).getData(true);
        Map<String, TeUser> jobCodeToUSer = pmList.stream()
                .collect(Collectors.toMap(ItfAiEmp::getEmployeeNumber, ItfAiEmp::getDmpUser, (v1, v2) -> v2));

        List<EvalDataVo> result = new ArrayList<>();
        EvalDataVo resultVo;
        for (ObjectId prjId : prjIds) {
            ItfAiPrj aiPrj = idToPrj.get(prjId);
            if (Objects.isNull(aiPrj)) {
                continue;
            }
            resultVo = new EvalDataVo();
            resultVo.setPrj(aiPrj.getDmpPrj());
            resultVo.setYm(evalYm);
            String pmEmployeeNumber = aiPrj.getPmEmployeeNumber();
            if (StringUtil.isNotEmpty(pmEmployeeNumber)) {
                resultVo.setPm(jobCodeToUSer.get(pmEmployeeNumber));
            }
            resultVo.setBuId(aiPrj.getSbuId());
            resultVo.setCcId(aiPrj.getCostCenterId());
            result.add(resultVo);
        }
        pageBean.setObjectList(result);
        pageBean.setPageSize(pageSize);
        pageBean.setPageIndex(pageIndex);
        return pageBean;
    }

    // @Override
    // public PageBean<EvalDataVo> listCommittedEvalData(EvalDataVo vo, ItfSysUser
    // loginUser) {
    // PageBean<EvalDataVo> pageBean = new PageBean<>();
    // String ym = vo.getYm();
    // ObjectId userId = loginUser.getId();
    // String buId = vo.getBuId();
    // String prjSearchKey = vo.getPrjSearchkey();
    // String pmSearchKey = vo.getPmSearchkey();
    //
    // //获取登陆人是项目经理的项目id
    // List<AggregationOperation> list = new ArrayList<>();
    // Criteria criteria = Criteria.where("isValid").is(true)
    // .and("role.roleId").is(new ObjectId("5a4ae684ba4014a42391fa91"))
    // .and("TePrjEmpMdPfm2ToEvalUsers.userId").is(userId);
    // MatchOperation match = Aggregation.match(criteria);
    // list.add(match);
    // Aggregation aggregation =
    // Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
    // Iterator<Map> cursor = mongoTemplate.aggregate(aggregation, "sysDefRoleUser",
    // Map.class).iterator();
    // List<ObjectId> prjIdList = new ArrayList<>();
    // while (cursor.hasNext()) {
    // Map map = cursor.next();
    // ObjectId prjId = StringUtil.toObjectId(map.get("defId"));
    // prjIdList.add(prjId);
    // }
    //
    // //获取登陆人是项目助理的项目id
    // list.clear();
    // Criteria criteriaResp = Criteria.where("isValid").is(true)
    // .and("defType.defTypeId").is(new ObjectId("5a41ed2bba4014a42391fa35"))
    // .and("role.roleId").is(new ObjectId("5df2fc06c56a424c334704c1"))
    // .and("TePrjEmpMdPfm2ToEvalUsers.userId").is(userId);
    // MatchOperation matchResp = Aggregation.match(criteriaResp);
    // list.add(matchResp);
    // Aggregation aggregationResp =
    // Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
    // Iterator<ItfSysDefRoleUser> cursorResp =
    // mongoTemplate.aggregate(aggregationResp, "sysDefRoleUser",
    // ItfSysDefRoleUser.class).iterator();
    // while (cursorResp.hasNext()) {
    // ItfSysDefRoleUser sysDefRoleUser = cursorResp.next();
    // ItfSysDef2SrcDef srcDef = sysDefRoleUser.getSrcDef();
    // if (Objects.nonNull(srcDef)) {
    // prjIdList.add(srcDef.getSrcDefId());
    // }
    // }
    //
    // List<IDbCondition> conds = new ArrayList<>();
    // conds.add(DFN.PrjEmpMdPfm.isValid.is(true));
    // conds.add(DFN.PrjEmpMdPfm.ym.is(ym));
    // conds.add(DFN.PrjEmpMdPfm.prj.dot(DFN.common_cid).in(prjIdList));
    // Sort sort = Sort.by(Sort.Direction.DESC, "_id");
    // Integer pageIndex = vo.getPageIndex();
    // Integer pageSize = vo.getPageSize();
    // Pager page = new Pager(pageIndex, pageSize);
    // List<TePrjEmpMdPfm> data = pfmDao.findByConds(conds, sort, page);
    // long count = pfmDao.countByConds(conds);
    //
    // List<ItfAiPrj> prjList =
    // prjFeignClient.queryPrjByPrjIds(prjIdList).getData(true);
    // Map<String, ItfAiPrj> prjCodeToPrj = prjList.stream().filter(item ->
    // Objects.nonNull(item.getProjectCode()))
    // .collect(Collectors.toMap(ItfAiPrj::getProjectCode, s -> s, (v1, v2) -> v2));
    //
    //
    // List<EvalDataVo> result = new ArrayList<>();
    // EvalDataVo resultvo;
    // for (TePrjEmpMdPfm pfm : data) {
    // resultvo = new EvalDataVo();
    // resultvo.setYm(pfm.getYm());
    // TeIdNameCn prj = pfm.getPrj();
    // if (Objects.isNull(prj)) {
    // continue;
    // }
    // resultvo.setPrj(prj);
    // ItfAiPrj aiPrj = prjCodeToPrj.get(prj.getCodeName());
    // if (Objects.nonNull(aiPrj)) {
    // resultvo.setBuId(aiPrj.getSbuId());
    // resultvo.setCcId(aiPrj.getCostCenterId());
    // }
    // resultvo.setPm(pfm.getPm());
    // result.add(resultvo);
    // }
    //
    // pageBean.setPageIndex(pageIndex);
    // pageBean.setPageSize(pageSize);
    // pageBean.setCount((int) count);
    // pageBean.setObjectList(result);
    // return pageBean;
    // }

    @Override
    public PageBean<EvalDataVo> listPrjCommittedOrCloseData(EvalDataVo vo, TeSysUser loginUser) {
        PageBean<EvalDataVo> pageBean = new PageBean<>();
        // 年月
        String ym = vo.getYm();
        String formatYm = ym.replace("-", "");
        // 项目
        String prjCode = vo.getPrjCode();
        // 员工类型
        List<String> empTypeList = vo.getEmpTypeList();
        boolean queryCommit = vo.getQueryCommit() != null ? vo.getQueryCommit() : false;
        boolean queryClose = vo.getQueryClose() != null ? vo.getQueryClose() : false;
        Integer pageIndex = vo.getPageIndex();
        Integer pageSize = vo.getPageSize();

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, formatYm));
        if (queryCommit) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.PM_IS_DONE.getCid()));
        } else if (queryClose) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.IS_CLOSED.getCid()));
        }
        // 立项bu查询
        if (StringUtil.isNotEmpty(vo.getBuId())) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.prjBu, vo.getBuId()));
        }
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.employeeType, empTypeList));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        // 角色查询
        if (StringUtil.isNotEmpty(vo.getRole())) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.role.dot(DFN.common_name), vo.getRole()));
        }
        // 姓名、工号查询
        if (StringUtil.isNotEmpty(vo.getEmpName()) && StringUtil.isEmpty(vo.getJobCode())) {
            conds.add(new DC_L(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userName), vo.getEmpName()));
        }
        if (StringUtil.isNotEmpty(vo.getJobCode()) && StringUtil.isEmpty(vo.getEmpName())) {
            conds.add(new DC_L(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), vo.getJobCode()));
        }
        if (StringUtil.isNotEmpty(vo.getJobCode()) && StringUtil.isNotEmpty(vo.getEmpName())) {
            List<IDbCondition> orCond = new ArrayList<>();
            orCond.add(new DC_L(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userName), vo.getEmpName()));
            orCond.add(new DC_L(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), vo.getJobCode()));
            conds.add(new DC_OR(orCond));
        }

        Sort sort = Sort.by(Sort.Direction.ASC, DFN.common__id.n());
        Pager page = new Pager();
        page.setIndex(pageIndex);
        page.setSize(pageSize);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null, sort, page);
        long count = pfmDao.countByConds(conds);

        // 获取上月评分
        Date date = DateUtil.parseDate(formatYm, DateUtil.DATE_MONTH_FOTMAT);
        Date deltaDate = DateUtil.getDeltaDate(date, Calendar.MONTH, -1);
        String provMonth = DateUtil.format(deltaDate, DateUtil.DATE_MONTH_FOTMAT);
        List<IDbCondition> prvMonConds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, provMonth));
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.employeeType, empTypeList));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        List<TePrjEmpMdPfm> provData = pfmDao.findByFieldAndConds(prvMonConds, null);
        Map<String, Double> provJobCodePrjCodeToPfmValue = new HashMap<>();
        for (TePrjEmpMdPfm mdPfm : provData) {
            TeUser emp = mdPfm.getEmp();
            TeIdNameCn prj = mdPfm.getPrj();
            if (Objects.isNull(emp) || Objects.isNull(prj)) {
                continue;
            }
            provJobCodePrjCodeToPfmValue.put(emp.getJobCode() + "_" + prj.getCodeName(), mdPfm.getPfmValue());
        }
        // 获取工时内容
        QueryContentVo queryContentVo = new QueryContentVo();
        queryContentVo.setPrjCode(prjCode);
        queryContentVo.setYearMonth(ym);
        List<RmsWorkRptVo> contentData = rmsEmpDateFeignClient.queryPrjFillContent(queryContentVo).getData(true);
        Map<String, String> jobCodePrjCodeToContent = new HashMap<>();
        for (RmsWorkRptVo dataVo : contentData) {
            TeUser emp = dataVo.getUser();
            TeIdNameCn prj = dataVo.getPrj();
            if (Objects.isNull(emp) || Objects.isNull(prj)) {
                continue;
            }
            jobCodePrjCodeToContent.put(emp.getJobCode() + "_" + prj.getCodeName(), dataVo.getDesc());
        }

        List<EvalDataVo> list = new ArrayList<>();
        EvalDataVo resultVo;
        for (TePrjEmpMdPfm pfm : data) {
            resultVo = new EvalDataVo();
            TeUser emp = pfm.getEmp();
            if (Objects.isNull(emp)) {
                continue;
            }
            resultVo.setEmpName(emp.getUserName());
            resultVo.setJobCode(emp.getJobCode());

            resultVo.setEmpType(pfm.getEmployeeType());
            resultVo.setMd(pfm.getMd());
            resultVo.setOprtInfo(pfm.getOprtInfo());
            TeIdNameCn prj = pfm.getPrj();
            String key = emp.getJobCode() + "_" + prj.getCodeName();
            Double provMonthPfmValue = provJobCodePrjCodeToPfmValue.get(key);
            resultVo.setProvMonthPfmValue(provMonthPfmValue);
            resultVo.setDesc(jobCodePrjCodeToContent.get(key));
            list.add(resultVo);
        }

        pageBean.setCount(count);
        pageBean.setObjectList(list);
        pageBean.setPageIndex(pageIndex);
        pageBean.setPageSize(pageSize);
        return pageBean;
    }

    @Override
    public void updateCommittedEvalData(EvalDataVo vo, TeSysUser loginUser) {
        String ym = vo.getYm();
        String evalYm = ym.replace("-", "");
        String prjCode = vo.getPrjCode();
        String jobCode = vo.getJobCode();
        // 需要更新的数据
        Double newValue = vo.getNewValue();
        if (newValue == null) {
            throw BusinessException.initExc("调整分数不可为空！");
        }
        String desc = vo.getEditDesc();
        if (StringUtil.isEmpty(desc)) {
            throw BusinessException.initExc("调整说明不可为空！");
        }
        if (StringUtil.isEmpty(prjCode) || StringUtil.isEmpty(jobCode)) {
            throw BusinessException.initExc("prjCode或jobCode缺少入参！");
        }

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCode));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.PM_IS_DONE.getCid()));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        TePrjEmpMdPfm mdPfm = data.get(0);
        List<UpdataData> updataDataList = new ArrayList<>();

        TeUser oprtUser = new TeUser();
        oprtUser.setLoginName(loginUser.getLoginName());
        oprtUser.setUserName(loginUser.getUserName());
        oprtUser.setJobCode(loginUser.getJobCode());
        oprtUser.setUserId(loginUser.getId());

        List<TePrjEmpMdPfm2OprtInfo> oprtInfo = mdPfm.getOprtInfo();
        Double pfmValue = mdPfm.getPfmValue();
        if (oprtInfo == null) {
            oprtInfo = new ArrayList<>();
        }
        Date date = new Date();
        oprtInfo.add(TePrjEmpMdPfm2OprtInfo.builder()
                .oprtType(PrjMdConstant.PM_ADJUST)
                .oldValue(pfmValue)
                .newValue(newValue)
                .oprtDesc(desc)
                .oprtUser(oprtUser)
                .oprtTime(date).build());

        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdUser, oprtUser));
        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.lastUpdTime, date));
        updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.oprtInfo, oprtInfo));
        pfmDao.updateByConds(conds, updataDataList);

        // 发送调整邮件通知员工
        sendAdjustMail(prjCode, loginUser.getUserName(), evalYm, mdPfm.getEmp(), loginUser.getBgId(), newValue);
    }

    private void sendAdjustMail(String prjCode, String pmName, String ym, TeUser emp, String bgId, Double newValue) {
        if (StringUtil.isEmpty(ym) || StringUtil.isEmpty(bgId)) {
            return;
        }
        // 获取绩效关闭时间
        String pfmCloseDay = "";
        String pfmCloseTime = getPfmCloseTime(bgId);
        if (StringUtil.isNotEmpty(pfmCloseTime)) {
            String[] split = pfmCloseTime.split(",");
            String num = split[0];
            Date date = DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT);
            String formatYm = DateUtil.format(date, DateUtil.DATE_MONTH_FOTMAT2);
            List<TeSysCal> sysCals = sysCalDao.getWorkDays(formatYm);
            TeSysCal sysCal = sysCals.get(Integer.parseInt(num));
            pfmCloseDay = sysCal.getDate();
        }
        String jobCode = emp.getJobCode();
        String empName = emp.getUserName();
        if (StringUtil.isEmpty(jobCode)) {
            log.info("绩效调整发送邮件获取员工工号为空！");
            return;
        }
        List<ItfAiEmp> users = itfAiEmpFeignClient.listUserByEmployeeNumber(Collections.singletonList(jobCode))
                .getData(true);
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        ItfAiEmp user = users.get(0);
        String mailBox = user.getEmailAddress();

        String year = ym.substring(0, 4);
        String month = ym.substring(4, 6);
        String yearMonth = year + "年" + month + "月";

        Map<String, Object> root = new HashMap<>();
        root.put("empName", empName);
        root.put("prjCode", prjCode);
        root.put("pmName", pmName);
        root.put("yearMonth", yearMonth);
        root.put("newValue", newValue);
        root.put("pfmCloseDay", pfmCloseDay);
        String mailContent = FreemakerUtil.process(root, "mailTemplate_PfmPmAdjustToEmp.html");

        ItfMailInfo mailInfo = new ItfMailInfo();
        mailInfo.setSubject("项目经理调整考评成绩通知");
        mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
        mailInfo.setContent(mailContent);
        mailInfo.setToList(Collections.singletonList(mailBox));
        mailFeignClient.sendMails(Collections.singletonList(mailInfo));
    }

    @Override
    public void copyProvMonPfmValue(String ym, String jobCode, String prjCode, TeSysUser loginUser) {
        // 获取上月年月
        String deltaYm = DateUtil.getDeltaYm(ym, -1);
        String provFormatYm = deltaYm.replace("-", "");
        String formatYm = ym.replace("-", "");

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.ym, Arrays.asList(provFormatYm, formatYm)));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName), prjCode));
        if (StringUtil.isNotEmpty(jobCode)) {
            conds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), jobCode));
        }
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        Map<String, Map<String, List<TePrjEmpMdPfm>>> mon2Key2Data = data.stream()
                .collect(Collectors.groupingBy(TePrjEmpMdPfm::getYm,
                        Collectors.groupingBy(
                                s -> s.getYm() + "_" + s.getEmp().getJobCode() + "_" + s.getPrj().getCodeName())));
        Map<String, List<TePrjEmpMdPfm>> provMonMap = mon2Key2Data.get(provFormatYm);
        Map<String, List<TePrjEmpMdPfm>> thisMonMap = mon2Key2Data.get(formatYm);
        Map<String, Double> thisMonKey2Value = new HashMap<>();
        for (String key : thisMonMap.keySet()) {
            List<TePrjEmpMdPfm> pfmList = thisMonMap.get(key);
            if (CollectionUtils.isEmpty(pfmList)) {
                continue;
            }
            TePrjEmpMdPfm pfm = pfmList.get(0);
            thisMonKey2Value.put(key, pfm.getPfmValue());
        }
        if (provMonMap == null || provMonMap.isEmpty()) {
            throw BusinessException.initExc("获取上月绩效成绩为空！");
        }

        Date date = new Date();
        List<IDbCondition> batchUpdateConds;
        List<UpdataData> updataDataList;
        List<List<UpdataData>> updatesAll = new ArrayList<>();
        List<List<IDbCondition>> condsAll = new ArrayList<>();

        for (String key : provMonMap.keySet()) {
            String[] split = key.split("_");
            String key_jobCode = split[1];
            String key_prjCode = split[2];
            String thisMonKey = formatYm + "_" + key_jobCode + "_" + key_prjCode;

            List<TePrjEmpMdPfm> provMonPfmList = provMonMap.get(key);
            List<TePrjEmpMdPfm> thisMonPfmList = thisMonMap.get(thisMonKey);
            if (CollectionUtils.isEmpty(provMonPfmList) || CollectionUtils.isEmpty(thisMonPfmList)) {
                continue;
            }

            batchUpdateConds = new ArrayList<>();
            batchUpdateConds.add(new DC_E(DFN.common_isValid, true));
            batchUpdateConds.add(new DC_E(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), key_jobCode));
            batchUpdateConds.add(new DC_E(DFN.PrjEmpMdPfm.prj.dot(DbFieldName.PrjEmpMdPfm.codeName), key_prjCode));
            condsAll.add(batchUpdateConds);

            TePrjEmpMdPfm provMonPfm = provMonPfmList.get(0);
            TePrjEmpMdPfm thisMonPfm = thisMonPfmList.get(0);

            TeUser oprtUser = new TeUser();
            oprtUser.setUserId(loginUser.getId());
            oprtUser.setLoginName(loginUser.getLoginName());
            oprtUser.setUserName(loginUser.getUserName());
            oprtUser.setJobCode(loginUser.getJobCode());

            TePrjEmpMdPfm2OprtInfo adjustOprt = new TePrjEmpMdPfm2OprtInfo();
            adjustOprt.setOprtType(PrjMdConstant.PM_SCORE);
            adjustOprt.setOprtUser(oprtUser);
            adjustOprt.setNewValue(provMonPfm.getPfmValue());
            adjustOprt.setOldValue(thisMonKey2Value.get(thisMonKey));
            adjustOprt.setOprtTime(date);

            List<TePrjEmpMdPfm2OprtInfo> updateOprtInfo = new ArrayList<>();
            List<TePrjEmpMdPfm2OprtInfo> thisOprtInfo = thisMonPfm.getOprtInfo();
            if (CollectionUtils.isNotEmpty(thisOprtInfo)) {
                for (TePrjEmpMdPfm2OprtInfo info : thisOprtInfo) {
                    if (!PrjMdConstant.PM_SCORE.equals(info.getOprtType())) {
                        updateOprtInfo.add(info);
                    }

                }
            }
            updateOprtInfo.add(adjustOprt);

            updataDataList = new ArrayList<>();
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.pfmValue, provMonPfm.getPfmValue()));
            updataDataList.add(new UpdataData(DFN.PrjEmpMdPfm.oprtInfo, updateOprtInfo));
            updatesAll.add(updataDataList);
        }

        if (CollectionUtils.isNotEmpty(condsAll)) {
            pfmDao.batchUpdate(condsAll, updatesAll, false);
        }
    }

    @Override
    public Map<String, Object> isPfmAdmin(TeSysUser loginUser) {
        Map<String, Object> result = new HashMap<>();
        result.put("isAdmin", false);

        // 填工时BU
        List<ItfSysDefCnfg> defMdBuList = sysDefCnfgFeignClient
                .queryByCnfgType(PrjMdConstant.RMS_MD_REQUIRED_BU_LIST, null).getData(true);
        List<String> sbuIds = new ArrayList<>();
        List<ObjectId> buIds = new ArrayList<>();
        Map<String, List<TeIdNameCn>> bgId2BuList = new HashMap<>();
        Map<String, List<String>> bgId2BuIds = new HashMap<>();
        for (ItfSysDefCnfg defCnfg : defMdBuList) {
            TeIdNameCn firstDef = defCnfg.getFirstDef();
            if (Objects.isNull(firstDef)) {
                continue;
            }
            sbuIds.add(firstDef.getCodeName());
            buIds.add(firstDef.getCid());
            TeIdNameCn srcDef = defCnfg.getSrcDef();
            if (Objects.isNull(srcDef)) {
                continue;
            }
            bgId2BuList.computeIfAbsent(srcDef.getCodeName(), s -> new ArrayList<>()).add(firstDef);
            bgId2BuIds.computeIfAbsent(srcDef.getCodeName(), s -> new ArrayList<>()).add(firstDef.getCodeName());
        }

        // 判断是否是BG管理员
        String bgId = "";
        // 查找所有bg
        List<ItfDept> bgList = itfDeptFeignClient.listByOrgType(PrjMdConstant.BG).getData(true);
        for (ItfDept dept : bgList) {
            List<TeDeptAdmin> admins = dept.getAdmin();
            if (CollectionUtils.isEmpty(admins)) {
                continue;
            }
            for (TeDeptAdmin admin : admins) {
                Boolean isValid = admin.getIsValid();
                TeUser emp = admin.getEmp();
                if (null == isValid && !isValid.booleanValue() && null == emp) {
                    continue;
                }
                if (loginUser.getJobCode().equals(emp.getJobCode())) {
                    bgId = dept.getOrgId();
                }
            }
        }
        if (StringUtil.isNotEmpty(bgId)) {
            result.put("isAdmin", true);
            result.put("buList", bgId2BuList.get(bgId));
            result.put("buIds", bgId2BuIds.get(bgId));
            return result;
        }

        Set<TeIdNameCn> manageBus = new HashSet<>();
        Set<String> manageBuIds = new HashSet<>();
        // 判断是否是BU绩效接口人
        List<ItfSysDefRoleUser> roleUserList = roleUserFeignClient.queryRoleUserList(
                        ItfRoleUserCommonQuery.builder()
                                .roleIdList(Collections.singletonList(PrjMdConstant.ADMIN.getRoleId()))
                                .srcDefIdList(buIds)
                                .defTypeIdList(Collections.singletonList(PrjMdConstant.SUBSYS_DEF.getCid()))
                                .defIdList(Collections.singletonList(PrjMdConstant.PES_DEF.getCid()))
                                .build())
                .getData(true);

        for (ItfSysDefRoleUser roleUser : roleUserList) {
            TeUser user = roleUser.getRoleUser();
            if (Objects.isNull(user) || !loginUser.getJobCode().equals(user.getJobCode())) {
                continue;
            }

            ItfSysDef2SrcDef srcDef = roleUser.getSrcDef();
            if (Objects.isNull(srcDef)) {
                continue;
            }
            TeIdNameCn bu = new TeIdNameCn();
            bu.setCid(srcDef.getSrcDefId());
            bu.setName(srcDef.getSrcDefName());
            bu.setCodeName(srcDef.getSrcDefCodeName());
            manageBus.add(bu);
            manageBuIds.add(srcDef.getSrcDefCodeName());
        }
        if (CollectionUtils.isNotEmpty(manageBus)) {
            result.put("isAdmin", true);
            result.put("buList", manageBus);
            result.put("buIds", manageBuIds);
            return result;
        }

        // 判断是否是BU部门管理员
        List<String> bgIds = bgList.stream().map(ItfDept::getOrgId).collect(Collectors.toList());
        List<ItfDept> buList = itfDeptFeignClient.queryByOrgParentIds(bgIds).getData(true);
        for (ItfDept bu : buList) {
            if (!sbuIds.contains(bu.getOrgCode())) {
                continue;
            }
            TeUser manager = bu.getManager();
            if (Objects.isNull(manager)) {
                continue;
            }
            if (loginUser.getJobCode().equals(manager.getJobCode())) {
                TeIdNameCn addBu = new TeIdNameCn();
                addBu.setCid(bu.getId());
                addBu.setName(bu.getOrgName());
                addBu.setCodeName(bu.getOrgCode());
                manageBus.add(addBu);
                manageBuIds.add(bu.getOrgCode());
            }
        }
        if (CollectionUtils.isNotEmpty(manageBus)) {
            result.put("isAdmin", true);
            result.put("buList", manageBus);
            result.put("buIds", manageBuIds);
            return result;
        }

        return result;
    }

    @Override
    public void updateEvalUser(String ym, String prjCode, String jobCode, TeSysUser loginUser) {
        List<ItfAiEmp> empList = itfAiEmpFeignClient.listUserByEmployeeNumber(Collections.singletonList(jobCode))
                .getData(true);
        if (CollectionUtils.isEmpty(empList)) {
            throw BusinessException.initExc("新考评人不存在！");
        }
        TeUser newEvalUser = empList.get(0).getDmpUser();
        TePrjEmpMdPfm2ToEvalUsers addEvalUser = new TePrjEmpMdPfm2ToEvalUsers();
        addEvalUser.setUser(newEvalUser);
        addEvalUser.setRole(PrjMdConstant.ROLE_PRJ_PM);

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.ym, ym.replace("-", "")));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.IS_CLOSED.getCid(), true));
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.prj.dot(DFN.common_cn), prjCode));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.PrjEmpMdPfm.toEvalUsers);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);

        updateEvalUser(data, addEvalUser);
    }

    @Override
    public List<ItfAiPrj> listPfmPrjByBuIds(String ym, List<String> buIds) {
        if (StringUtil.isEmpty(ym) || CollectionUtils.isEmpty(buIds)) {
            return Collections.emptyList();
        }
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.PrjEmpMdPfm.ym, ym.replace("-", "")));
        conds.add(new DC_I<>(DbFieldName.PrjEmpMdPfm.prjBu, buIds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.PrjEmpMdPfm.prj);
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<String> prjCodeList = data.stream().filter(s -> Objects.nonNull(s.getPrj()))
                .map(s -> s.getPrj().getCodeName())
                .distinct().collect(Collectors.toList());
        return prjFeignClient.queryAiPrjsByCode(prjCodeList).getData(true);
    }

    // 更新离职的项目经理考评人
    private void updateResignPm(String bgId, Map<String, TeUser> prjCode2OldPm) {
        String evalYm = this.getEvalYm(bgId);
        PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true).ym("202408")// todo 上线恢复
                .statusId(PrjMdConstant.TODO.getCid()).isLocked(false).build();
        List<TePrjEmpMdPfm> toEvalData = listMdPfms(query);
        if (CollectionUtils.isEmpty(toEvalData)) {
            return;
        }
        // 获取待考评数据项目经理
        Set<String> pmJobCodes = new HashSet<>();
        Map<String, String> prjCode2jobCode = new HashMap<>();
        Map<String, List<String>> jobCode2PrjCodes = new HashMap<>();
        for (TePrjEmpMdPfm pfm : toEvalData) {
            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
            TeIdNameCn prj = pfm.getPrj();
            if (CollectionUtils.isEmpty(toEvalUsers) || Objects.isNull(prj)) {
                continue;
            }
            for (TePrjEmpMdPfm2ToEvalUsers evalUser : toEvalUsers) {
                TeIdNameCn role = evalUser.getRole();
                if (PrjMdConstant.ROLE_PRJ_PM.getCid().equals(role.getCid())) {
                    TeUser user = evalUser.getUser();
                    if (Objects.nonNull(user)) {
                        pmJobCodes.add(user.getJobCode());
                        jobCode2PrjCodes.computeIfAbsent(user.getJobCode(), s -> new ArrayList<>())
                                .add(prj.getCodeName());
                        prjCode2jobCode.put(prj.getCodeName(), user.getJobCode());
                    }
                }
            }
        }
        // 获取离职的项目经理
        List<TeSysUser> resignPms = sysUserService.listResignUsers(new ArrayList<>(pmJobCodes));
        if (CollectionUtils.isEmpty(resignPms)) {
            return;
        }
        Map<String, TeUser> jobCode2OldPm = new HashMap<>();
        List<String> resignJobCodes = new ArrayList<>();
        for (TeSysUser resignPm : resignPms) {
            TeUser oldPm = new TeUser();
            oldPm.setUserId(resignPm.getId());
            oldPm.setLoginName(resignPm.getLoginName());
            oldPm.setUserName(resignPm.getUserName());
            oldPm.setJobCode(resignPm.getJobCode());
            jobCode2OldPm.put(resignPm.getJobCode(), oldPm);
            resignJobCodes.add(resignPm.getJobCode());
        }

        Set<String> resignPrjCodes = new HashSet<>();
        for (String jobCode : jobCode2PrjCodes.keySet()) {
            if (!resignJobCodes.contains(jobCode)) {
                continue;
            }
            resignPrjCodes.addAll(jobCode2PrjCodes.get(jobCode));
        }
        for (String prjCode : resignPrjCodes) {
            prjCode2OldPm.put(prjCode, jobCode2OldPm.get(prjCode2jobCode.get(prjCode)));
        }

        // 获取离职项目经理对应的项目信息
        List<ItfAiPrj> resignPrjList = prjFeignClient.queryAiPrjsByCode(new ArrayList<>(resignPrjCodes)).getData(true);
        Map<String, String> prjCode2newPmJobCode = new HashMap<>();
        for (ItfAiPrj prj : resignPrjList) {
            String prjPmJobCode = prj.getPmEmployeeNumber();
            if (resignJobCodes.contains(prjPmJobCode)) {
                // 项目表里还是已离职项目经理
                prjCode2newPmJobCode.put(prj.getProjectCode(), null);
            } else {
                // 项目表里是新项目经理
                prjCode2newPmJobCode.put(prj.getProjectCode(), prjPmJobCode);
            }
        }

        Collection<String> newPmJobCodes = prjCode2newPmJobCode.values();
        List<TeSysUser> newPmList = sysUserService.queryUserByJobCodes(new ArrayList<>(newPmJobCodes));
        Map<String, TeUser> newPmJobCode2User = new HashMap<>();
        for (TeSysUser user : newPmList) {
            TeUser newPm = new TeUser();
            newPm.setJobCode(user.getJobCode());
            newPm.setUserName(user.getUserName());
            newPm.setLoginName(user.getLoginName());
            newPm.setUserId(user.getId());
            newPmJobCode2User.put(user.getJobCode(), newPm);
        }

        // 更新离职的项目经理对应项目的绩效数据
        List<List<IDbCondition>> condsAll = new ArrayList<>();
        List<List<UpdataData>> updatesAll = new ArrayList<>();
        for (TePrjEmpMdPfm pfm : toEvalData) {
            TeIdNameCn prj = pfm.getPrj();
            if (Objects.isNull(prj)) {
                continue;
            }
            String prjCode = prj.getCodeName();
            if (!resignPrjCodes.contains(prjCode)) {
                continue;
            }

            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
            List<TePrjEmpMdPfm2ToEvalUsers> newEvalUsers = new ArrayList<>();
            for (TePrjEmpMdPfm2ToEvalUsers evalUser : toEvalUsers) {
                TeIdNameCn role = evalUser.getRole();
                if (!PrjMdConstant.ROLE_PRJ_PM.getCid().equals(role.getCid())) {
                    newEvalUsers.add(evalUser);
                }
            }
            TePrjEmpMdPfm2ToEvalUsers pmEvalUser = new TePrjEmpMdPfm2ToEvalUsers();
            pmEvalUser.setRole(PrjMdConstant.ROLE_PRJ_PM);
            pmEvalUser.setUser(newPmJobCode2User.get(prjCode));
            newEvalUsers.add(pmEvalUser);

            // 更新
            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DFN.common__id, pfm.getId()));
            List<UpdataData> updataData = new ArrayList<>();
            updataData.add(new UpdataData(DbFieldName.PrjEmpMdPfm.toEvalUsers, newEvalUsers));

            condsAll.add(conds);
            updatesAll.add(updataData);
        }

        if (CollectionUtils.isNotEmpty(condsAll)) {
            pfmDao.batchUpdate(condsAll, updatesAll, false);
        }

    }

    @Override
    public PageBean<EvalDataVo> adminQueryEvalData(EvalDataVo vo, TeSysUser loginUser) {
        String ym = vo.getYm().replace("-", "");
        if (StringUtil.isEmpty(ym)) {
            throw BusinessException.initExc("年月不可为空！");
        }
        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where(DFN.PrjEmpMdPfm.isValid.n()).is(true)
                .and(DFN.PrjEmpMdPfm.ym.n()).is(ym)
                .and(DFN.PrjEmpMdPfm.isLocked.n()).is(false);

        if (StringUtil.isNotEmpty(vo.getPrjCode())) {
            criteria.and(DFN.PrjEmpMdPfm.prj.dot(DFN.PrjEmpMdPfm.codeName).n()).is(vo.getPrjCode());
        }
        if (StringUtil.isNotEmpty(vo.getPrjType())) {
            criteria.and(DFN.PrjEmpMdPfm.prjType.n()).is(vo.getPrjType());
        }
        if (StringUtil.isNotEmpty(vo.getBuId())) {
            criteria.and(DFN.PrjEmpMdPfm.prjBu.n()).is(vo.getBuId());
        } else {
            Map<String, Object> pfmAdmin = isPfmAdmin(loginUser);
            List<String> buIds = (List) pfmAdmin.get("buIds");
            if (CollectionUtils.isEmpty(buIds)) {
                buIds = Collections.emptyList();
            }
            criteria.and(DFN.PrjEmpMdPfm.prjBu.n()).in(buIds);
        }
        if (Objects.nonNull(vo.getStatusId())) {
            criteria.and(DFN.PrjEmpMdPfm.status.dot(DbFieldName.common_cid).n()).is(vo.getStatusId());
        }
        if (StringUtil.isNotEmpty(vo.getJobCode())) {
            criteria.and(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.role.dot(DbFieldName.common_cid)).n())
                    .is(PrjMdConstant.ROLE_PRJ_PM.getCid());
            criteria.and(DFN.PrjEmpMdPfm.toEvalUsers.dot(DFN.PrjEmpMdPfm.user.dot(DbFieldName.common_jobCode)).n())
                    .is(vo.getJobCode());
        }

        MatchOperation match = Aggregation.match(criteria);
        GroupOperation group = Aggregation.group("prj.cid")
                .first("ym").as("ym")
                .first("prj").as("prj")
                .first("pm").as("pm")
                .first("prjBu").as("prjBu")
                .first("prjType").as("prjType")
                .first("subPrjType").as("subPrjType")
                .first("status").as("status")
                .first("toEvalUsers").as("toEvalUsers");

        list.add(match);
        list.add(group);
        CountOperation count = Aggregation.count().as("count");

        Integer pageIndex = vo.getPageIndex();
        Integer pageSize = vo.getPageSize();
        if (pageIndex != null && pageSize != null) {
            SkipOperation skip = Aggregation.skip((long) pageIndex * pageSize);
            LimitOperation limit = Aggregation.limit(pageSize);
            list.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        } else {
            SkipOperation skip = Aggregation.skip(0L);
            list.add(Aggregation.facet(count).as("count").and(skip).as("data"));
        }
        Aggregation dateAgg = Aggregation.newAggregation(list)
                .withOptions(new AggregationOptions.Builder().allowDiskUse(true).build());
        List<PrjEmpMdPfmBo> dataList = mongoTemplate.aggregate(dateAgg, "prjEmpMdPfm", PrjEmpMdPfmBo.class)
                .getMappedResults();

        PageBean<EvalDataVo> pageBean = new PageBean<>();
        if (CollectionUtils.isEmpty(dataList)) {
            return pageBean;
        }
        List<MongoCountBo> count1 = dataList.get(0).getCount();
        if (ObjectUtil.isEmpty(count1)) {
            return pageBean;
        }
        int total = count1.get(0).getCount().intValue();
        pageBean.setCount((long) total);

        List<TePrjEmpMdPfm> data = dataList.get(0).getData();
        Set<String> prjBuIds = new HashSet<>();
        List<EvalDataVo> result = new ArrayList<>();
        EvalDataVo resultVo;
        int index = 0;
        for (TePrjEmpMdPfm pfm : data) {
            resultVo = new EvalDataVo();
            resultVo.setIndex(++index);
            String evalYm = pfm.getYm();
            if (!StringUtil.isEmpty(evalYm)) {
                resultVo.setYm(DateUtil.format(DateUtil.parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT),
                        DateUtil.DATE_MONTH_FOTMAT2));
            }
            TeUser pm = pfm.getPm();
            if (Objects.nonNull(pm)) {
                resultVo.setPmNameAndJc(pm.getUserName() + "/" + pm.getJobCode());
            }
            TeIdNameCn prj = pfm.getPrj();
            if (Objects.nonNull(prj)) {
                resultVo.setPrjCode(prj.getCodeName());
                resultVo.setPrjName(prj.getName());
            }
            TeIdNameCn status = pfm.getStatus();
            if (Objects.nonNull(status)) {
                resultVo.setStatus(status.getName());
            }
            resultVo.setPrjType(pfm.getPrjType());
            resultVo.setSubPrjType(pfm.getSubPrjType());
            resultVo.setBuId(pfm.getPrjBu());

            List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
            if (CollectionUtils.isNotEmpty(toEvalUsers)) {
                for (TePrjEmpMdPfm2ToEvalUsers evalUser : toEvalUsers) {
                    if (PrjMdConstant.ROLE_PRJ_PM.getCid().equals(evalUser.getRole().getCid())) {
                        TeUser user = evalUser.getUser();
                        if (Objects.nonNull(user)) {
                            resultVo.setEvalUser(user.getUserName() + "/" + user.getJobCode());
                        }
                    }
                }
            }

            prjBuIds.add(pfm.getPrjBu());
            result.add(resultVo);
        }
        List<ItfDept> buList = itfDeptFeignClient.queryDataByOrgCode(new ArrayList<>(prjBuIds)).getData(true);
        Map<String, String> buCode2Name = buList.stream()
                .collect(Collectors.toMap(ItfDept::getOrgCode, ItfDept::getOrgName, (v1, v2) -> v2));
        for (EvalDataVo dataVo : result) {
            dataVo.setBuName(buCode2Name.get(dataVo.getBuId()));
        }

        pageBean.setObjectList(result);
        pageBean.setPageSize(pageSize);
        pageBean.setPageIndex(pageIndex);
        return pageBean;
    }

    @Override
    public void exportAdminQueryEvalData(EvalDataVo vo, TeSysUser loginUser, HttpServletResponse response)
            throws IOException {
        PageBean<EvalDataVo> resultVo = adminQueryEvalData(vo, loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("管理员视图.xlsx", "UTF-8"));

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "管理员视图")
                .head(EvalDataVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(resultVo.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public void closePfm(String bgId, String evalYm) {
        // 1.将未关闭的置为已关闭
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, evalYm));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, bgId));
        List<UpdataData> updataDatas = new ArrayList<>();
        updataDatas.add(new UpdataData(DFN.PrjEmpMdPfm.isLocked, true));
        updataDatas.add(new UpdataData(DFN.PrjEmpMdPfm.status, PrjMdConstant.IS_CLOSED));
        pfmDao.updateByConds(conds, updataDatas);
        // 2、计算人员月度绩效
        this.computerEmpYmPfm(bgId, evalYm);
    }

    private List<TePrjEmpYmPfm> listYmMdPfm(String bgId, String ym, List<ObjectId> userIds) {
        List<IDbCondition> conds = new ArrayList<>();
        List<IDbCondition> condOr = new ArrayList<>();
        List<IDbCondition> condOne = new ArrayList<>();
        List<IDbCondition> condTwo = new ArrayList<>();
        condOne.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        condOne.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, true));
        condOne.add(new DC_E(DFN.PrjEmpMdPfm.ym, ym));
        condOne.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, bgId));
        condOne.add(new DC_I<>(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userId), userIds));

        condTwo.add(new DC_E(DFN.PrjEmpMdPfm.notPfmMd, true));
        condTwo.add(new DC_E(DFN.PrjEmpMdPfm.ym, ym));
        condTwo.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, bgId));
        condTwo.add(new DC_I<>(DFN.PrjEmpMdPfm.emp.dot(DFN.common_userId), userIds));

        condOr.add(new DC_AO(condOne));
        condOr.add(new DC_AO(condTwo));
        conds.add(new DC_OR(condOr));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        Map<TeUser, List<TePrjEmpMdPfm>> emp2data = data.stream().collect(Collectors.groupingBy(TePrjEmpMdPfm::getEmp));

        List<TePrjEmpYmPfm> ymPfmList = new ArrayList<>();
        TePrjEmpYmPfm ymPfm;
        for (TeUser emp : emp2data.keySet()) {
            List<TePrjEmpMdPfm> empMdPfms = emp2data.get(emp);
            if (CollectionUtils.isEmpty(empMdPfms)) {
                continue;
            }
            ymPfm = new TePrjEmpYmPfm();
            // 分配项目清单
            Set<TeIdNameCn> prjs = new HashSet<>();
            // 绩效项目清单
            Set<TeIdNameCn> pfmPrjs = new HashSet<>();
            // 评分项目清单
            Set<TeIdNameCn> scorePrjs = new HashSet<>();
            // 分配工时数
            int prjMd = 0;
            // 绩效工时数
            int prjMdPfm = 0;
            // 评分工时数
            int prjMdScore = 0;
            double scoreSum = 0d;
            int mdSum = 0;

            for (TePrjEmpMdPfm mdPfm : empMdPfms) {
                TeIdNameCn prj = mdPfm.getPrj();
                Boolean notPfmMd = mdPfm.getNotPfmMd();
                TeIdNameCn status = mdPfm.getStatus();
                Integer md = mdPfm.getMd();
                Double pfmValue = mdPfm.getPfmValue();
                prjs.add(prj);
                prjMd += md;
                if (!notPfmMd) {
                    pfmPrjs.add(prj);
                    prjMdPfm += md;
                }
                if (Objects.nonNull(status) && PrjMdConstant.PM_IS_DONE.getCid().equals(status.getCid())) {
                    scorePrjs.add(prj);
                    prjMdScore += md;
                    if (pfmValue != null) {
                        scoreSum += pfmValue * md;
                        mdSum += md;
                    }
                }
            }
            ymPfm.setYm(ym);
            ymPfm.setEmp(emp);
            ymPfm.setPrjs(new ArrayList<>(prjs));
            ymPfm.setPfmPrjs(new ArrayList<>(pfmPrjs));
            ymPfm.setScorePrjs(new ArrayList<>(scorePrjs));
            ymPfm.setPrjMd(prjMd);
            ymPfm.setPrjMdPfm(prjMdPfm);
            ymPfm.setPrjMdScore(prjMdScore);
            // 加权平均
            if (mdSum != 0) {
                double score = BigDecimalUtils.divideDouble(scoreSum, mdSum, 2);
                ymPfm.setScore(score);
            }
            ymPfmList.add(ymPfm);
        }
        return ymPfmList;
    }

    private void addStdMd(String ym, List<ObjectId> empIds, List<TePrjEmpYmPfm> data) {
        Date date = DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT);
        String formatYm = DateUtil.format(date, DateUtil.DATE_MONTH_FOTMAT2);
        List<List<ObjectId>> partition = Lists.partition(empIds, 1000);
        List<Map> stdMdList = new ArrayList<>();
        for (List<ObjectId> userIds : partition) {
            List<Map> mapList = rmsEmpDateFeignClient.statUserStdMdByMonth(userIds, Collections.singletonList(formatYm))
                    .getData(true);
            stdMdList.addAll(mapList);
        }

        Map<String, Integer> jobCode2md = new HashMap<>();
        for (Map map : stdMdList) {
            Map emp = (Map) map.get("emp");
            if (Objects.isNull(emp)) {
                continue;
            }
            Integer stdMd = (Integer) map.get("stdMd");
            jobCode2md.put(StringUtil.getNotNullStr(emp.get("jobCode")), stdMd);
        }

        for (TePrjEmpYmPfm ymPfm : data) {
            TeUser emp = ymPfm.getEmp();
            if (Objects.isNull(emp)) {
                continue;
            }
            ymPfm.setStdMd(jobCode2md.get(emp.getJobCode()));
        }
    }

    @Override
    public void rerunEmpYmPfm(String bgId) {
        String provYm = DateUtil.format(DateUtil.getDeltaDate(new Date(), Calendar.MONTH, -1),
                DateUtil.DATE_MONTH_FOTMAT);
        Date lastDayProvMonthDate = DateUtil.getLastDayByMonth(DateUtil.parseDate(provYm, DateUtil.DATE_MONTH_FOTMAT));
        String lastDayProvMonth = DateUtil.format(lastDayProvMonthDate, DateUtil.DATE_FORMAT);
        ItfAiEmpHistQuery empHistQuery = ItfAiEmpHistQuery.builder().timeSheetFlag("Y").currentFlag("Y")
                .bgId(bgId).dates(Collections.singletonList(lastDayProvMonth)).build();
        List<ItfAiEmpHist> empHistList = itfAiEmpHistFeignClient.queryDataByConds(empHistQuery).getData(true);
        List<ObjectId> histEmpIdList = empHistList.stream().filter(s -> Objects.nonNull(s.getDmpUser()))
                .map(s -> s.getDmpUser().getUserId()).collect(Collectors.toList());

        // 将之前的置为无效
        List<IDbCondition> updateConds = new ArrayList<>();
        updateConds.add(new DC_E(DbFieldName.PrjEmpYmPfm.isValid, true));
        updateConds.add(new DC_E(DbFieldName.PrjEmpYmPfm.ym, provYm));
        updateConds.add(new DC_I<>(DbFieldName.PrjEmpYmPfm.emp.dot(DbFieldName.common_userId), histEmpIdList));
        List<UpdataData> updataDataList = new ArrayList<>();
        updataDataList.add(new UpdataData(DbFieldName.PrjEmpYmPfm.isValid, false));
        ymPfmDao.updateByConds(updateConds, updataDataList);

        // 计算月度综合绩效
        this.computerEmpYmPfm(bgId, provYm);
    }

    private void computerEmpYmPfm(String bgId, String evalYm) {
        // 从人员历史表（itfAiEmpHist）获取当前BG下，date为考评月最后一个自然日下 的在职人员 且 是填工时的人员（即currentFlag为Y &
        // timeSheetFlag=Y）
        Date lastDayProvMonthDate = DateUtil.getLastDayByMonth(DateUtil.parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT));
        String lastDayProvMonth = DateUtil.format(lastDayProvMonthDate, DateUtil.DATE_FORMAT);
        ItfAiEmpHistQuery empHistQuery = ItfAiEmpHistQuery.builder().timeSheetFlag("Y").currentFlag("Y")
                .bgId(bgId).dates(Collections.singletonList(lastDayProvMonth)).build();
        List<ItfAiEmpHist> empHistList = itfAiEmpHistFeignClient.queryDataByConds(empHistQuery).getData(true);
        Map<ObjectId, ItfAiEmpHist> histEmpId2Data = empHistList.stream().filter(s -> Objects.nonNull(s.getDmpUser()))
                .collect(Collectors.toMap(s -> s.getDmpUser().getUserId(), s -> s, (v1, v2) -> v2));
        // 取出每个人已分配工时数
        List<ObjectId> histEmpIdList = new ArrayList<>(histEmpId2Data.keySet());
        List<TePrjEmpYmPfm> ymPfmList = this.listYmMdPfm(bgId, evalYm, histEmpIdList);
        this.addStdMd(evalYm, histEmpIdList, ymPfmList);

        // 计算其月度综合绩效得分
        Date now = new Date();
        List<TePrjEmpYmPfm> toSaveData = new ArrayList<>();
        for (TePrjEmpYmPfm ymPfm : ymPfmList) {
            Integer stdMd = ymPfm.getStdMd(); // 标准工时数
            Integer prjMd = ymPfm.getPrjMd(); // 分配工时数
            Integer prjMdPfm = ymPfm.getPrjMdPfm(); // 绩效工时数
            Integer prjMdScore = ymPfm.getPrjMdScore(); // 评分工时数
            if (prjMd < stdMd / 2) {
                ymPfm.setScore(null);
                ymPfm.setDesc("当月已分配项目（包含工程项目其它类、研发项目P0(PRD)/P0类/P1类和售前项目的所有项目）的工时天数未达到标准工作日天数的50%！");
            } else if (prjMdPfm == 0) {
                ymPfm.setScore(null);
                ymPfm.setDesc("参与的项目都不在绩效计算范围内！");
            } else if (prjMdScore == 0) {
                ymPfm.setScore(null);
                ymPfm.setDesc("所有的绩效工时，项目经理均未考评！");
            }
            TeUser emp = ymPfm.getEmp();
            if (Objects.isNull(emp)) {
                continue;
            }
            ItfAiEmpHist itfAiEmpHist = histEmpId2Data.get(emp.getUserId());
            ymPfm.setIsValid(true);
            ymPfm.setBuCode(itfAiEmpHist.getSbuId());
            ymPfm.setCcId(itfAiEmpHist.getCostCenterId());
            ymPfm.setAddTime(now);
            toSaveData.add(ymPfm);

            if (toSaveData.size() == 1000) {
                ymPfmDao.batchSave(toSaveData);
                toSaveData.clear();
            }
        }

        if (CollectionUtils.isNotEmpty(toSaveData)) {
            ymPfmDao.batchSave(toSaveData);
        }
    }

    @Override
    public Boolean canRerunYmPfm(String bgId) {
        Date now = new Date();
        String pfmCloseTime = getPfmCloseTime(bgId);
        if (StringUtil.isEmpty(pfmCloseTime)) {
            throw BusinessException.initExc("绩效关闭时间获取为空");
        }
        String formatYm = DateUtil.format(now, DateUtil.DATE_MONTH_FOTMAT2);
        List<ItfSysCal> workDays = sysCalFeignClient.getWorkDaysByYm(formatYm).getData(true);
        if (CollectionUtils.isEmpty(workDays)) {
            throw BusinessException.initExc("获取工作日为空");
        }
        String[] split = pfmCloseTime.split(",");
        String num = split[0]; // 前推天数
        ItfSysCal itfSysCal = workDays.get(Integer.parseInt(num) - 1);
        if (Objects.isNull(itfSysCal)) {
            throw BusinessException.initExc("绩效关闭日获取错误");
        }
        String closeDay = itfSysCal.getDate();
        String nextMonthFirstDay = DateUtil.getNextMonthFirstDay(now, 1);
        Date nextMonthFirstDate = DateUtil.parseDate(nextMonthFirstDay, DateUtil.DATE_FORMAT);
        Date lastTimeCloseDate = DateUtil
                .getSomeDayLastTimeByDateAndNum(DateUtil.parseDate(closeDay, DateUtil.DATE_FORMAT), 0);
        return (lastTimeCloseDate.before(now) && now.before(nextMonthFirstDate));
    }

    /**
     * 当项目经理提交考评、调整考评成绩后、以及绩效关闭日前一天还未完成考评的员工，均需发邮件通知员工上级经理，同一个上级经理合并发一封邮件。
     */
    @Override
    public void sendManageMail(String bgId) {
        Date nowDay = new Date();
        Date yesterday = DateUtil.getDeltaDate(nowDay, Calendar.DAY_OF_MONTH, -1);
        Date yestFirstTime = DateUtil.getSomeDayFirstTimeByDateAndNum(nowDay, -1);
        Date yestLastTime = DateUtil.getLastDayByDate(yesterday);

        // 昨天提交
        List<TePrjEmpMdPfm> commitYesterday = listByStatusOprtTypeTime(bgId, PrjMdConstant.PM_IS_DONE.getCid(),
                PrjMdConstant.PM_COMMIT, yestFirstTime, yestLastTime);
        // 昨天调整
        List<TePrjEmpMdPfm> adjustYesterday = listByStatusOprtTypeTime(bgId, PrjMdConstant.PM_IS_DONE.getCid(),
                PrjMdConstant.PM_ADJUST, yestFirstTime, yestLastTime);

        List<String> manageJobCode = new ArrayList<>();
        List<String> jobCode1 = commitYesterday.stream().filter(s -> Objects.nonNull(s.getEmpManager()))
                .map(s -> s.getEmpManager().getJobCode())
                .collect(Collectors.toList());
        List<String> jobCode2 = adjustYesterday.stream().filter(s -> Objects.nonNull(s.getEmpManager()))
                .map(s -> s.getEmpManager().getJobCode())
                .collect(Collectors.toList());
        manageJobCode.addAll(jobCode1);
        manageJobCode.addAll(jobCode2);

        // List<ItfSysDef> buList =
        // sysDefFeignClient.listSysDefByDefTypeId(SysDefType.AI_BU.getId()).getData(true);
        // Map<String, String> buIdToName =
        // buList.stream().collect(Collectors.toMap(ItfSysDef::getCodeName,
        // ItfSysDef::getDefName));

        // 判断是否是绩效考评关闭前一天
        Boolean isProvDayOfPfmClose = isProvDayOfPfmClose(bgId);
        Map<String, List<TePrjEmpMdPfm>> todoMap = new HashMap<>();
        if (isProvDayOfPfmClose) {
            // 未提交考评
            Map<String, String> sortMap = new HashMap<>();
            sortMap.put(DbFieldName.PrjEmpMdPfm.emp.dot(DFN.common_userName).n(), Sort.Direction.ASC.name());
            PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true)
                    .isLocked(false).statusId(PrjMdConstant.TODO.getCid()).prjBgId(bgId).sortMap(sortMap).build();
            List<TePrjEmpMdPfm> todoList = listMdPfms(query);
            List<String> jobCode3 = todoList.stream().filter(s -> Objects.nonNull(s.getEmpManager()))
                    .map(s -> s.getEmpManager().getJobCode())
                    .collect(Collectors.toList());
            manageJobCode.addAll(jobCode3);
            todoMap = todoList.stream()
                    .filter(s -> Objects.nonNull(s.getEmp()) && Objects.nonNull(s.getEmpManager())
                            && Objects.nonNull(s.getPrj()))
                    .sorted(Comparator.comparing(s -> s.getEmp().getUserName()))
                    .collect(Collectors.groupingBy(s -> s.getEmpManager().getJobCode() + s.getPrj().getCodeName()));
        }

        if (CollectionUtils.isEmpty(manageJobCode)) {
            return;
        }
        // 去重
        manageJobCode = manageJobCode.stream().distinct().collect(Collectors.toList());
        List<ItfAiEmp> userList = itfAiEmpFeignClient.listUserByEmployeeNumber(manageJobCode).getData(true);
        Map<String, String> jobCodeToMail = userList.stream()
                .filter(item -> Objects.nonNull(item.getEmployeeNumber()) && Objects.nonNull(item.getEmailAddress()))
                .collect(Collectors.toMap(ItfAiEmp::getEmployeeNumber, ItfAiEmp::getEmailAddress, (v1, v2) -> v2));

        Map<String, List<TePrjEmpMdPfm>> adjustYestMap = adjustYesterday.stream()
                .filter(s -> Objects.nonNull(s.getEmp()) && Objects.nonNull(s.getEmpManager())
                        && Objects.nonNull(s.getPrj()))
                .sorted(Comparator.comparing(s -> s.getEmp().getUserName()))
                .collect(Collectors.groupingBy(s -> s.getEmpManager().getJobCode() + s.getPrj().getCodeName()));

        Map<String, List<TePrjEmpMdPfm>> commitYestMap = commitYesterday.stream()
                .filter(s -> Objects.nonNull(s.getEmp()) && Objects.nonNull(s.getEmpManager())
                        && Objects.nonNull(s.getPrj()))
                .sorted(Comparator.comparing(s -> s.getEmp().getUserName()))
                .collect(Collectors.groupingBy(s -> s.getEmpManager().getJobCode() + s.getPrj().getCodeName()));

        // 准备邮件数据
        List<PfmMailVo> todoMailVoList = new ArrayList<>();
        List<PfmMailVo> commitMailVoList = new ArrayList<>();
        List<PfmMailVo> adjustMailVoList = new ArrayList<>();
        List<ItfMailInfo> mailInfoList = new ArrayList<>();
        for (String jobCode : manageJobCode) {
            if (isProvDayOfPfmClose) {
                // 组装未提交的考评数据
                for (String todoKey : todoMap.keySet()) {
                    if (!todoKey.contains(jobCode)) {
                        continue;
                    }
                    List<TePrjEmpMdPfm> pfmList = todoMap.get(todoKey);
                    if (CollectionUtils.isEmpty(pfmList)) {
                        continue;
                    }
                    PfmMailVo mailVo;
                    for (TePrjEmpMdPfm pfm : pfmList) {
                        mailVo = new PfmMailVo();
                        mailVo.setEmp(pfm.getEmp());
                        mailVo.setEmpType(pfm.getEmployeeType());
                        mailVo.setMd(pfmList.size());
                        mailVo.setPrj(pfm.getPrj());
                        mailVo.setPrjType(pfm.getPrjType());
                        mailVo.setPm(pfm.getPm());
                        mailVo.setYm(pfm.getYm());
                        todoMailVoList.add(mailVo);
                    }
                }
            }

            // 组装昨天提交的考评数据
            for (String todoKey : commitYestMap.keySet()) {
                if (!todoKey.contains(jobCode)) {
                    continue;
                }
                List<TePrjEmpMdPfm> pfmList = commitYestMap.get(todoKey);
                if (CollectionUtils.isEmpty(pfmList)) {
                    continue;
                }
                PfmMailVo mailVo;
                for (TePrjEmpMdPfm pfm : pfmList) {
                    mailVo = new PfmMailVo();
                    mailVo.setEmp(pfm.getEmp());
                    mailVo.setEmpType(pfm.getEmployeeType());
                    mailVo.setMd(pfmList.size());
                    mailVo.setPrj(pfm.getPrj());
                    mailVo.setPrjType(pfm.getPrjType());
                    mailVo.setPm(pfm.getPm());
                    mailVo.setYm(pfm.getYm());

                    List<TePrjEmpMdPfm2OprtInfo> oprtInfo = pfm.getOprtInfo();
                    if (CollectionUtils.isNotEmpty(oprtInfo)) {
                        List<Double> newValues = oprtInfo.stream()
                                .filter(s -> PrjMdConstant.PM_COMMIT.equals(s.getOprtType())
                                        && yestFirstTime.compareTo(s.getOprtTime()) < 0
                                        && yestLastTime.compareTo(s.getOprtTime()) > 0)
                                .sorted(Comparator.comparing(TePrjEmpMdPfm2OprtInfo::getOprtTime).reversed())
                                .map(TePrjEmpMdPfm2OprtInfo::getNewValue).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(newValues)) {
                            mailVo.setPfmValue(newValues.get(0));
                        }
                    }

                    commitMailVoList.add(mailVo);
                }
            }

            // 组装昨天调整的考评数据
            for (String todoKey : adjustYestMap.keySet()) {
                if (!todoKey.contains(jobCode)) {
                    continue;
                }
                List<TePrjEmpMdPfm> pfmList = adjustYestMap.get(todoKey);
                if (CollectionUtils.isEmpty(pfmList)) {
                    continue;
                }
                PfmMailVo mailVo;
                for (TePrjEmpMdPfm pfm : pfmList) {
                    mailVo = new PfmMailVo();
                    mailVo.setEmp(pfm.getEmp());
                    mailVo.setEmpType(pfm.getEmployeeType());
                    mailVo.setMd(pfmList.size());
                    mailVo.setPrj(pfm.getPrj());
                    mailVo.setPrjType(pfm.getPrjType());
                    mailVo.setPm(pfm.getPm());
                    mailVo.setYm(pfm.getYm());

                    List<TePrjEmpMdPfm2OprtInfo> oprtInfo = pfm.getOprtInfo();
                    if (CollectionUtils.isNotEmpty(oprtInfo)) {
                        List<Double> newValues = oprtInfo.stream()
                                .filter(s -> PrjMdConstant.PM_ADJUST.equals(s.getOprtType())
                                        && yestFirstTime.compareTo(s.getOprtTime()) < 0
                                        && yestLastTime.compareTo(s.getOprtTime()) > 0)
                                .sorted(Comparator.comparing(TePrjEmpMdPfm2OprtInfo::getOprtTime).reversed())
                                .map(TePrjEmpMdPfm2OprtInfo::getNewValue).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(newValues)) {
                            mailVo.setPfmValue(newValues.get(0));
                        }
                    }

                    adjustMailVoList.add(mailVo);
                }
            }

            Map<String, Object> root = new HashMap<>();
            root.put("todoMailVoList", todoMailVoList);
            root.put("commitMailVoList", commitMailVoList);
            root.put("adjustMailVoList", adjustMailVoList);
            String content = FreemakerUtil.process(root, "mailTemplate_PfmEmpManager.html");

            // 组装邮件
            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject("部门员工项目绩效提醒");
            mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
            mailInfo.setContent(content);
            mailInfo.setToList(Collections.singletonList(jobCodeToMail.get(jobCode)));
            mailInfoList.add(mailInfo);
            // todo 用于测试， 上线去除
            if (mailInfoList.size() == 5) {
                break;
            }
        }

        if (CollectionUtils.isNotEmpty(mailInfoList)) {
            mailFeignClient.sendMails(mailInfoList);
        }

    }

    private List<TePrjEmpMdPfm> listByStatusOprtTypeTime(String bgId, ObjectId statusId, String oprtType,
                                                         Date startTime, Date endTime) {
        return pfmDao.listByStatusAndOprtTypeAndTime(bgId, statusId, oprtType, startTime, endTime);
    }

    private Boolean isProvDayOfPfmClose(String bgId) {
        String pfmCloseTime = getPfmCloseTime(bgId);
        if (StringUtil.isEmpty(pfmCloseTime)) {
            throw BusinessException.initExc("绩效关闭时间获取为空");
        }
        String[] split = pfmCloseTime.split(",");
        String num = split[0]; // 前推天数

        Date date = new Date();
        String nowDay = DateUtil.format(date, DateUtil.DATE_FORMAT);
        String formatYm = DateUtil.format(date, DateUtil.DATE_MONTH_FOTMAT2);
        List<TeSysCal> workDays = sysCalDao.getWorkDays(formatYm);
        TeSysCal itfSysCal = workDays.get(Integer.parseInt(num) - 1);
        Date closeDate = DateUtil.parseDate(itfSysCal.getDate(), DateUtil.DATE_FORMAT);
        // 绩效关闭前一天
        Date provDateOfClose = DateUtil.getDeltaDate(closeDate, Calendar.DAY_OF_MONTH, -1);
        String provDayOfClose = DateUtil.format(provDateOfClose, DateUtil.DATE_FORMAT);
        return nowDay.equals(provDayOfClose);
    }

    @Override
    public List<BuPfmAdmin> listPfmBuAdmin(TeSysUser loginUser) {
        // 查询人员拥有权限的BG
        ItfDept dept = itfDeptFeignClient.queryUserPowerBg(loginUser.getId()).getData(true);
        if (null == dept) {
            throw BusinessException.initExc("当前人员没有BG权限!");
        }

        String bgId = dept.getOrgId();
        String bgName = dept.getOrgName();
        if (StringUtil.isNull(bgId)) {
            throw BusinessException.initExc("当前人员所属BG信息为空!");
        }

        return queryPfmBuAdmin(bgId, bgName);
    }

    private List<BuPfmAdmin> queryPfmBuAdmin(String bgId, String bgName) {
        // 1.查询人员所属bgId下的BU
        List<ItfSysDefCnfg> defMdBuList = sysDefCnfgFeignClient
                .queryByCnfgType(PrjMdConstant.RMS_MD_REQUIRED_BU_LIST, bgId).getData(true);
        if (CollectionUtils.isEmpty(defMdBuList)) {
            throw BusinessException.initExc("当前人员所属BG下BU为空!");
        }
        List<ObjectId> mdBuCidList = new ArrayList<>();
        for (ItfSysDefCnfg cnfg : defMdBuList) {
            TeIdNameCn mdBuDef = cnfg.getFirstDef();
            if (null == mdBuDef || StringUtil.isNull(mdBuDef.getCid()))
                continue;
            mdBuCidList.add(mdBuDef.getCid());
        }
        if (CollectionUtils.isEmpty(mdBuCidList)) {
            throw BusinessException.initExc("当前人员所属BG下BuId为空!");
        }

        // 2.查询BU对接人
        List<ItfSysDefRoleUser> roleUserList = roleUserFeignClient.queryRoleUserList(
                        ItfRoleUserCommonQuery.builder()
                                .roleIdList(Collections.singletonList(PrjMdConstant.ADMIN.getRoleId()))
                                .srcDefIdList(mdBuCidList)
                                .defTypeIdList(Collections.singletonList(PrjMdConstant.SUBSYS_DEF.getCid()))
                                .defIdList(Collections.singletonList(PrjMdConstant.PES_DEF.getCid()))
                                .build())
                .getData(true);

        // 3.查询人员电话号码
        Map<String, String> userPhoneMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(roleUserList)) {

            List<String> jobCodeList = new ArrayList<>();
            for (ItfSysDefRoleUser TePrjEmpMdPfm2ToEvalUsers : roleUserList) {
                TeUser user = TePrjEmpMdPfm2ToEvalUsers.getRoleUser();
                String jobCode = user.getJobCode();
                if (StringUtil.isNull(jobCode))
                    continue;
                jobCodeList.add(jobCode);
            }
            List<TeSysUser> sysUserList = sysUserService.queryUserByJobCodes(jobCodeList);
            // 封装人员电话号码
            if (CollectionUtils.isNotEmpty(sysUserList)) {
                for (TeSysUser user : sysUserList) {
                    userPhoneMap.put(user.getJobCode(), user.getMobilePhone());
                }
            }
        }

        // 封装人员
        Map<String, List<AdminUser>> buUsersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(roleUserList)) {
            for (ItfSysDefRoleUser TePrjEmpMdPfm2ToEvalUsers : roleUserList) {
                ItfSysDef2SrcDef buDef = TePrjEmpMdPfm2ToEvalUsers.getSrcDef();
                if (null == buDef) {
                    continue;
                }
                TeUser buUser = TePrjEmpMdPfm2ToEvalUsers.getRoleUser();
                if (null == buUser) {
                    continue;
                }

                String buId = buDef.getSrcDefCodeName();
                List<AdminUser> buAnUserList = buUsersMap.computeIfAbsent(buId, k -> new ArrayList<>());
                AdminUser addBuUser = new AdminUser();
                BeanUtils.copyProperties(buUser, addBuUser);
                addBuUser.setPhone(userPhoneMap.get(buUser.getJobCode()));
                buAnUserList.add(addBuUser);
            }
        }

        // 4.封装返回数据
        List<BuPfmAdmin> respUserList = new ArrayList<>();
        BuPfmAdmin buPfmResp = null;
        for (ItfSysDefCnfg cnfg : defMdBuList) {

            TeIdNameCn mdBuDef = cnfg.getFirstDef();
            if (null == mdBuDef || StringUtil.isNull(mdBuDef.getCodeName()))
                continue;

            buPfmResp = new BuPfmAdmin();
            buPfmResp.setBgId(bgId);
            buPfmResp.setBgName(bgName);
            buPfmResp.setSbu(mdBuDef);
            buPfmResp.setUserList(buUsersMap.get(mdBuDef.getCodeName()));
            respUserList.add(buPfmResp);
        }
        return respUserList;
    }

    @Override
    public void addBuPfmResp(ObjectId buCid, String buId, String buName, String phone, List<ObjectId> userIdList,
                             TeSysUser loginUser) {
        if (CollectionUtils.isEmpty(userIdList)) {
            throw BusinessException.initExc("入参人员idList为空");
        }

        // 删除现有人员
        this.deleteBuPfmResp(userIdList, buCid);

        // 查询人员信息
        List<ItfAiEmp> aiEmpList = itfAiEmpFeignClient
                .queryAiEmps(ItfAiEmpQuery.builder().userIdList(userIdList).build()).getData(true);
        if (CollectionUtils.isEmpty(aiEmpList)) {
            throw BusinessException.initExc("添加人员实体不存在!");
        }

        List<ItfSysDefRoleUser> dataList = new ArrayList<>();
        ItfSysDefRoleUser TePrjEmpMdPfm2ToEvalUsers = null;
        ItfSysDef2SrcDef srcDef = new ItfSysDef2SrcDef();
        srcDef.setSrcDefId(buCid);
        srcDef.setSrcDefName(buName);
        srcDef.setSrcDefCodeName(buId);

        ItfSysDef2DefType defType = new ItfSysDef2DefType();
        defType.setDefTypeCodeName(PrjMdConstant.SUBSYS_DEF.getCodeName());
        defType.setDefTypeId(PrjMdConstant.SUBSYS_DEF.getCid());
        defType.setDefTypeName(PrjMdConstant.SUBSYS_DEF.getName());

        TeUser addUser = new TeUser();
        addUser.setUserId(loginUser.getId());
        addUser.setJobCode(loginUser.getJobCode());
        addUser.setLoginName(loginUser.getLoginName());
        addUser.setUserName(loginUser.getUserName());

        for (ItfAiEmp emp : aiEmpList) {
            TePrjEmpMdPfm2ToEvalUsers = new ItfSysDefRoleUser();
            TePrjEmpMdPfm2ToEvalUsers.setIsValid(true);
            TePrjEmpMdPfm2ToEvalUsers.setDefType(defType);
            TePrjEmpMdPfm2ToEvalUsers.setDefId(PrjMdConstant.PES_DEF.getCid());
            TePrjEmpMdPfm2ToEvalUsers.setRole(Collections.singletonList(PrjMdConstant.ADMIN));
            TePrjEmpMdPfm2ToEvalUsers.setRoleUser(emp.getDmpUser());
            TePrjEmpMdPfm2ToEvalUsers.setSrcDef(srcDef);
            TePrjEmpMdPfm2ToEvalUsers.setAddUser(addUser);
            TePrjEmpMdPfm2ToEvalUsers.setAddTime(new Date());
            dataList.add(TePrjEmpMdPfm2ToEvalUsers);
        }
        CommonResult<Void> commonResult = roleUserFeignClient.batchSaveRoleUsers(dataList);
        if (!commonResult.isSuccess()) {
            throw BusinessException.initExc("添加BU绩效接口人, 执行失败! >>>" + commonResult.getMessage());
        }

        if (StringUtil.isNotNull(phone)) {
            ObjectId userId = userIdList.get(0);
            String regex = "^\\d{11}$";
            if (!phone.matches(regex)) {
                throw BusinessException.initExc("请填写正确的手机号码!");
            }

            TeSysUser sysUser = sysUserService.findById(userId);
            if (null == sysUser) {
                throw BusinessException.initExc("更新人员不存在!");
            }
            // 更新
            sysUserFeignClient.updateSysUserPhone(userId, phone);
        }
    }

    @Override
    public void deleteBuPfmResp(List<ObjectId> userIdList, ObjectId buCid) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userIdList)) {
            throw BusinessException.initExc("入参人员ids为空!");
        }
        roleUserFeignClient.deletePfmBuAdmin(userIdList, buCid);
    }

    @Override
    public void sendPfmPmMail(String bgId) {
        boolean canSendMail = this.isSendMailDay(bgId);
        if (!canSendMail) {
            return;
        }

        // 该bg项目绩效关闭时间
        String evalYm = getEvalYm(bgId);
        String closeDay = getCloseDayOfEvalYm(bgId, evalYm);

        List<ItfMailInfo> mailInfoList = new ArrayList<>();
        // 从“prjEmpMdPfm”中取出isValid为true下的
        // 且prjBgCode为该BG下的且status.cid为ObjectId("668cfc477dbb511498817a40")即待考评下的所有记录，
        PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true).isLocked(false)
                .prjBgId(bgId).statusId(PrjMdConstant.TODO.getCid()).build();
        List<TePrjEmpMdPfm> data = listMdPfms(query);
        Map<String, List<TePrjEmpMdPfm>> jobCodeToData = data.stream()
                .filter(s -> Objects.nonNull(s.getPm()) && Objects.nonNull(s.getPm().getJobCode()))
                .collect(Collectors.groupingBy(s -> s.getPm().getJobCode()));
        List<String> prjCodeList = data.stream()
                .filter(s -> Objects.nonNull(s.getPrj()) && StringUtil.isNotEmpty(s.getPrj().getCodeName()))
                .map(s -> s.getPrj().getCodeName()).distinct().collect(Collectors.toList());
        List<ItfAiPrj> prjList = prjFeignClient.queryAiPrjsByCode(prjCodeList).getData(true);
        Map<String, String> prjCodeToSubType = new HashMap<>();
        for (ItfAiPrj aiPrj : prjList) {
            if (StringUtil.isEmpty(aiPrj.getProjectType())) {
                continue;
            }
            if ("工程项目".equals(aiPrj.getProjectType())) {
                prjCodeToSubType.put(aiPrj.getProjectCode(), aiPrj.getPdProjectType());
            } else if ("研发项目".equals(aiPrj.getProjectType())) {
                prjCodeToSubType.put(aiPrj.getProjectCode(), aiPrj.getRdProjectType());
            }
        }
        List<ItfAiEmp> userList = itfAiEmpFeignClient.listUserByEmployeeNumber(new ArrayList<>(jobCodeToData.keySet()))
                .getData(true);
        Map<String, ItfAiEmp> jobCodeToEmp = userList.stream()
                .collect(Collectors.toMap(ItfAiEmp::getEmployeeNumber, s -> s, (v1, v2) -> v2));
        for (String jobCode : jobCodeToData.keySet()) {
            ItfAiEmp aiEmp = jobCodeToEmp.get(jobCode);
            if (Objects.isNull(aiEmp)) {
                continue;
            }
            List<TePrjEmpMdPfm> pfmList = jobCodeToData.get(jobCode);
            List<PfmMailVo> mailVoList = new ArrayList<>();
            PfmMailVo mailVo;
            for (TePrjEmpMdPfm pfm : pfmList) {
                mailVo = new PfmMailVo();
                TeIdNameCn prj = pfm.getPrj();
                mailVo.setPrj(prj);
                mailVo.setPrjType(pfm.getPrjType());
                if (Objects.nonNull(prj)) {
                    mailVo.setSubPrjType(prjCodeToSubType.get(prj.getCodeName()));
                }
                mailVo.setPm(pfm.getPm());
                mailVo.setPrjBuId(pfm.getPrjBu());
                mailVo.setYm(pfm.getYm());
                mailVo.setStatus(pfm.getStatus());
                List<TePrjEmpMdPfm2ToEvalUsers> toEvalUsers = pfm.getToEvalUsers();
                for (TePrjEmpMdPfm2ToEvalUsers TePrjEmpMdPfm2ToEvalUsers : toEvalUsers) {
                    if (PrjMdConstant.ROLE_PRJ_PM.getCid().equals(TePrjEmpMdPfm2ToEvalUsers.getRole().getCid())) {
                        mailVo.setEvaluator(TePrjEmpMdPfm2ToEvalUsers.getUser());
                    }
                }
                mailVo.setEvalType("");
                mailVoList.add(mailVo);
            }

            Map<String, Object> root = new HashMap<>();

            root.put("manageName", aiEmp.getLastName());
            root.put("pfmCloseTime", closeDay);
            root.put("dataList", mailVoList);

            String content = FreemakerUtil.process(root, "mailTemplate_PfmPm.html");

            // 组装邮件
            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject("项目经理月度未考评提醒");
            mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
            mailInfo.setContent(content);
            mailInfo.setToList(Collections.singletonList(aiEmp.getEmailAddress()));
            mailInfoList.add(mailInfo);
            // todo 用于测试， 上线去除
            if (mailInfoList.size() == 5) {
                break;
            }
        }

        if (CollectionUtils.isNotEmpty(mailInfoList)) {
            mailFeignClient.sendMails(mailInfoList);
        }
    }

    @Override
    public void sendPfmBuAdminMail(String bgId) {
        boolean canSendMail = this.isSendMailDay(bgId);
        // if (!canSendMail) {
        // return;
        // }

        List<ItfMailInfo> mailInfoList = new ArrayList<>();
        // 从“prjEmpMdPfm”中取出isValid为true下的
        // 且prjBgCode为该BG下的且status.cid为ObjectId("668cfc477dbb511498817a40")即待考评下的所有记录，
        // 按empBuCode分组，项目经理为剔重后的pm清单。
        String evalYm = getEvalYm(bgId);

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.ym, "202408"));// todo 上线恢复
        conds.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, bgId));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);

        // 获取项目经理离职模块展示数据
        Map<String, List<PfmMailVo>> bu2resignPm = this.getResignMailData(bgId);
        if (CollectionUtils.isEmpty(data) && bu2resignPm.size() == 0) {
            return;
        }

        List<ObjectId> pmIdList = data.stream().filter(s -> Objects.nonNull(s.getPm())).map(s -> s.getPm().getUserId())
                .distinct().collect(Collectors.toList());
        List<ItfAiEmp> pmList = itfAiEmpFeignClient.queryDataByDmpUserIds(pmIdList).getData(true);
        // buId -> 该Bu下项目经理
        Map<String, List<ItfAiEmp>> buIdToPmList = pmList.stream().collect(Collectors.groupingBy(ItfAiEmp::getSbuId));

        // 获取填写工时BU
        List<ItfSysDefCnfg> buList = sysDefCnfgFeignClient.queryByCnfgType(PrjMdConstant.RMS_MD_REQUIRED_BU_LIST, bgId)
                .getData(true);
        if (CollectionUtils.isEmpty(buList)) {
            return;
        }
        Map<String, String> buIdToName = buList.stream().filter(s -> Objects.nonNull(s.getFirstDef()))
                .collect(Collectors.toMap(s -> s.getFirstDef().getCodeName(), s -> s.getFirstDef().getName(),
                        (V1, v2) -> v2));
        // 该bg项目绩效关闭时间
        String closeDay = getCloseDayOfEvalYm(bgId, evalYm);

        // 获取BU绩效接口人邮箱
        List<BuPfmAdmin> buPfmAdminList = queryPfmBuAdmin(bgId, "");
        List<ObjectId> adminIds = new ArrayList<>();
        Map<String, List<ObjectId>> buIdToBuAdminIds = new HashMap<>();
        for (BuPfmAdmin buPfmAdmin : buPfmAdminList) {
            List<AdminUser> userList = buPfmAdmin.getUserList();
            TeIdNameCn sbu = buPfmAdmin.getSbu();
            if (CollectionUtils.isEmpty(userList) || Objects.isNull(sbu)) {
                continue;
            }
            List<ObjectId> adminUserIds = userList.stream().map(AdminUser::getUserId).distinct()
                    .collect(Collectors.toList());
            adminIds.addAll(adminUserIds);
            buIdToBuAdminIds.put(sbu.getCodeName(), adminUserIds);
        }
        if (CollectionUtils.isEmpty(adminIds)) {
            return;
        }

        // 获取BG绩效管理员
        List<ItfSysDef> bgList = sysDefFeignClient.listSysDefByDefTypeId(SysDefTypeCodeName.AI_BG.getId())
                .getData(true);
        ObjectId bgObjectId = null;
        List<ObjectId> bgAdminIds = new ArrayList<>();
        for (ItfSysDef bg : bgList) {
            if (bgId.equals(bg.getCodeName())) {
                bgObjectId = bg.getId();
            }
        }
        if (Objects.nonNull(bgObjectId)) {
            List<ItfSysDefRoleUser> pfmBgAdmin = roleUserFeignClient.queryRoleUserList(
                            ItfRoleUserCommonQuery.builder()
                                    .roleIdList(Collections.singletonList(PrjMdConstant.ADMIN.getRoleId()))
                                    .defTypeIdList(Collections.emptyList())
                                    .defIdList(Collections.singletonList(bgObjectId))
                                    .build())
                    .getData(true);
            bgAdminIds = pfmBgAdmin.stream().filter(s -> Objects.nonNull(s.getRoleUser()))
                    .map(s -> s.getRoleUser().getUserId())
                    .collect(Collectors.toList());
        }
        adminIds.addAll(bgAdminIds);

        List<ItfAiEmp> buAdminList = itfAiEmpFeignClient.queryDataByDmpUserIds(adminIds).getData(true);
        Map<ObjectId, String> buAdminToMail = new HashMap<>();

        List<String> bgAdminMail = new ArrayList<>();
        for (ItfAiEmp emp : buAdminList) {
            TeUser dmpUser = emp.getDmpUser();
            String emailAddress = emp.getEmailAddress();
            if (Objects.isNull(dmpUser) || StringUtil.isEmpty(emailAddress)) {
                continue;
            }
            if (bgAdminIds.contains(dmpUser.getUserId())) {
                bgAdminMail.add(emailAddress);
            } else {
                buAdminToMail.put(dmpUser.getUserId(), emailAddress);
            }
        }
        log.info("bgAdminMail:" + bgAdminMail);

        PfmMailVo mailVo;
        for (String buId : buIdToBuAdminIds.keySet()) {
            List<PfmMailVo> unCommitList = new ArrayList<>();
            List<ItfAiEmp> buPmList = buIdToPmList.get(buId);
            if (CollectionUtils.isEmpty(buPmList)) {
                continue;
            }
            List<ObjectId> buPmIds = buPmList.stream().map(s -> s.getDmpUser().getUserId()).distinct()
                    .collect(Collectors.toList());
            List<TePrjEmpMdPfm> filterData = data.stream().filter(s -> buPmIds.contains(s.getPm().getUserId()))
                    .collect(Collectors.toList());
            // 组装该BU数据
            Map<TeUser, List<TePrjEmpMdPfm>> pmToData = filterData.stream()
                    .collect(Collectors.groupingBy(TePrjEmpMdPfm::getPm));
            for (TeUser pm : pmToData.keySet()) {
                List<TePrjEmpMdPfm> pmDataList = pmToData.get(pm);
                List<String> pmPrjCodeList = pmDataList.stream().map(s -> s.getPrj().getCodeName()).distinct()
                        .collect(Collectors.toList());

                mailVo = new PfmMailVo();
                String buName = buIdToName.get(buId);
                mailVo.setEmpBuName(buName);
                mailVo.setPm(pm);
                mailVo.setToEvalPrjNum(pmPrjCodeList.size());
                mailVo.setEvaluator(pm);
                unCommitList.add(mailVo);
            }

            if (CollectionUtils.isEmpty(unCommitList)) {
                continue;
            }
            Map<String, Object> root = new HashMap<>();
            root.put("pfmCloseTime", closeDay);
            root.put("unCommitList", unCommitList);
            root.put("resignData", bu2resignPm.get(buId));

            String content = FreemakerUtil.process(root, "mailTemplate_PfmBuResp.html");

            // 组装邮件
            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject("BU内项目经理未考评提醒");
            mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
            mailInfo.setContent(content);

            List<ObjectId> idList = buIdToBuAdminIds.get(buId);
            List<String> mailList = new ArrayList<>();
            for (ObjectId id : idList) {
                mailList.add(buAdminToMail.get(id));
            }
            mailInfo.setToList(mailList);
            if (CollectionUtils.isNotEmpty(bgAdminMail)) {
                mailInfo.setCcList(bgAdminMail);
            }
            mailInfoList.add(mailInfo);
            // todo 用于测试， 上线去除
            if (mailInfoList.size() == 5) {
                break;
            }
        }

        if (CollectionUtils.isNotEmpty(mailInfoList)) {
            mailFeignClient.sendMails(mailInfoList);
        }
    }

    @Override
    public Map<String, List<PfmMailVo>> getResignMailData(String bgId) {
        String evalYm = getEvalYm(bgId);

        Map<String, TeUser> prjCode2resignPm = new HashMap<>();
        this.updateResignPm(bgId, prjCode2resignPm);
        if (prjCode2resignPm.size() == 0) {
            return new HashMap<>();
        }
        // 查询项目经理人员为null的数据，发送邮件
        List<IDbCondition> resignConds = new ArrayList<>();
        List<ObjectId> statusIds = new ArrayList<>();
        statusIds.add(PrjMdConstant.TODO.getCid());
        statusIds.add(PrjMdConstant.GROUP_RESP_IS_DONE.getCid());
        resignConds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        resignConds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        resignConds.add(new DC_E(DFN.PrjEmpMdPfm.ym, "202408"));// todo 上线恢复
        resignConds.add(new DC_E(DFN.PrjEmpMdPfm.prjBg, bgId));
        resignConds.add(new DC_I<>(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), statusIds));

        Document toEvalUsersConds = new Document();
        toEvalUsersConds.put(DFN.PrjEmpMdPfm.role.dot(DFN.common_cid).n(),
                new Document("$eq", PrjMdConstant.ROLE_PRJ_PM.getCid()));
        toEvalUsersConds.put(DFN.PrjEmpMdPfm.user.n(), new Document("$eq", null));
        resignConds.add(new DC_E(DFN.PrjEmpMdPfm.toEvalUsers, new Document("$elemMatch", toEvalUsersConds)));
        List<TePrjEmpMdPfm> result = pfmDao.findByFieldAndConds(resignConds, null);
        Map<String, List<PfmMailVo>> bu2resignPm = new HashMap<>();
        Map<String, Map<TeIdNameCn, List<TePrjEmpMdPfm>>> bu2prj2data = result.stream()
                .collect(Collectors.groupingBy(TePrjEmpMdPfm::getPrjBu, Collectors.groupingBy(TePrjEmpMdPfm::getPrj)));
        for (String buId : bu2prj2data.keySet()) {
            Map<TeIdNameCn, List<TePrjEmpMdPfm>> prj2data = bu2prj2data.get(buId);
            List<PfmMailVo> prjResignPm = new ArrayList<>();
            for (TeIdNameCn prj : prj2data.keySet()) {
                PfmMailVo resignVo = new PfmMailVo();
                resignVo.setPrj(prj);
                resignVo.setPm(prjCode2resignPm.get(prj.getCodeName()));
                prjResignPm.add(resignVo);
            }
            bu2resignPm.put(buId, prjResignPm);
        }
        return bu2resignPm;
    }

    @Override
    public List<TeUser> listAdminViewPm(EvalDataVo vo) {
        String ym = vo.getYm();
        if (StringUtil.isEmpty(ym)) {
            throw BusinessException.initExc("年月不可为空！");
        }
        List<String> buIds = vo.getBuIds();
        if (CollectionUtils.isEmpty(buIds)) {
            throw BusinessException.initExc("BuId不可为空！");
        }
        PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true).prjBuIdList(buIds).ym(ym.replace("-", ""))
                .build();
        List<TePrjEmpMdPfm> tePrjEmpMdPfms = listMdPfms(query);
        return tePrjEmpMdPfms.stream().map(TePrjEmpMdPfm::getPm).distinct().collect(Collectors.toList());
    }

    @Override
    public void sendPfmEmpMail(String bgId) {
        // 绩效启动后，从第3个工作日开始到绩效关闭日当天，每天发邮件提醒当前考评月无项目绩效成绩的员工，告知其项目经理还未给他提交考评成绩。
        boolean canSendMail = this.isSendMailDay(bgId);
        if (!canSendMail) {
            return;
        }

        List<ItfAiEmp> bgEmpList = itfAiEmpFeignClient.listAiEmpByBgId(bgId, "").getData(true);
        List<String> bgEmpJobCodes = new ArrayList<>();
        Map<String, String> jobCodeToMail = new HashMap<>();
        for (ItfAiEmp aiEmp : bgEmpList) {
            bgEmpJobCodes.add(aiEmp.getEmployeeNumber());
            jobCodeToMail.put(aiEmp.getEmployeeNumber(), aiEmp.getEmailAddress());
        }
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isValid, true));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.isLocked, false));
        conds.add(new DC_E(DFN.PrjEmpMdPfm.status.dot(DFN.common_cid), PrjMdConstant.TODO.getCid()));
        conds.add(new DC_I<>(DFN.PrjEmpMdPfm.emp.dot(DFN.common_jobCode), bgEmpJobCodes));
        List<TePrjEmpMdPfm> data = pfmDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        Map<String, Map<String, List<TePrjEmpMdPfm>>> jobCodeToPrjCodeToData = data.stream()
                .collect(Collectors.groupingBy(s -> s.getEmp().getJobCode(),
                        Collectors.groupingBy(s -> s.getPrj().getCodeName())));
        Map<String, String> jobCodeToName = data.stream()
                .collect(Collectors.toMap(s -> s.getEmp().getJobCode(), s -> s.getEmp().getUserName(), (v1, v2) -> v1));

        List<ItfSysDef> buList = sysDefFeignClient.listSysDefByDefTypeId(SysDefType.AI_BU.getId()).getData(true);
        Map<String, String> buIdToName = buList.stream()
                .collect(Collectors.toMap(ItfSysDef::getCodeName, ItfSysDef::getDefName));

        List<ItfMailInfo> mailInfoList = new ArrayList<>();
        List<PfmMailVo> pfmMailVoList = new ArrayList<>();
        PfmMailVo mailVo;
        for (String jobCode : jobCodeToPrjCodeToData.keySet()) {
            Map<String, List<TePrjEmpMdPfm>> prjCodeToData = jobCodeToPrjCodeToData.get(jobCode);
            for (String prjCode : prjCodeToData.keySet()) {
                mailVo = new PfmMailVo();
                List<TePrjEmpMdPfm> pfmList = prjCodeToData.get(prjCode);
                TePrjEmpMdPfm pfm = pfmList.get(0);
                mailVo.setEmp(pfm.getEmp());
                mailVo.setEmpType(pfm.getEmployeeType());
                mailVo.setEmpBuName(buIdToName.get(pfm.getEmpBu()));
                mailVo.setMd(pfmList.size());
                mailVo.setPrj(pfm.getPrj());
                mailVo.setPrjType(pfm.getPrjType());
                mailVo.setPm(pfm.getPm());
                mailVo.setYm(pfm.getYm());
                mailVo.setEvaluator(pfm.getPm());
                mailVo.setPfmValue(pfm.getPfmValue());
                pfmMailVoList.add(mailVo);
            }

            Map<String, Object> root = new HashMap<>();
            root.put("empName", jobCodeToName.get(jobCode));
            root.put("pfmMailVoList", pfmMailVoList);

            String content = FreemakerUtil.process(root, "mailTemplate_PfmEmp.html");

            // 组装邮件
            ItfMailInfo mailInfo = new ItfMailInfo();
            mailInfo.setSubject("员工无项目绩效成绩提醒");
            mailInfo.setSubsystem(DMPSubsystem.PES.getCode());
            mailInfo.setContent(content);
            mailInfo.setToList(Collections.singletonList(jobCodeToMail.get(jobCode)));
            mailInfoList.add(mailInfo);

            // todo 用于测试， 上线去除
            if (mailInfoList.size() == 5) {
                break;
            }
        }

        if (CollectionUtils.isNotEmpty(mailInfoList)) {
            mailFeignClient.sendMails(mailInfoList);
        }

    }

    private boolean isSendMailDay(String bgId) {
        // 绩效启动后，从第3个工作日开始到绩效关闭日当天，
        List<ItfSysCal> workDays = sysCalFeignClient
                .getWorkDaysByYm(DateUtil.format(new Date(), DateUtil.DATE_MONTH_FOTMAT2))
                .getData(true);
        String pfmCloseTime = this.getPfmCloseTime(bgId);
        String[] split = pfmCloseTime.split(",");
        ItfSysCal pfmCloseDay = workDays.get(Integer.parseInt(split[0]) - 1);
        ItfSysCal itfSysCal = workDays.get(Integer.parseInt(split[0]) + 2);
        // 绩效启动\关闭日期
        Date startDay = DateUtil.parseDate(pfmCloseDay.getDate(), DateUtil.DATE_FORMAT);
        // 绩效启动后第三天
        Date thirdDay = DateUtil.parseDate(itfSysCal.getDate(), DateUtil.DATE_FORMAT);
        Date today = DateUtil.getSomeDayFirstTimeByDateAndNum(new Date(), 0);
        return !(startDay.before(today) && today.before(thirdDay));
    }

    @Override
    public String getEvalYm(String bgId) {
        Date now = new Date();
        String nowDay = DateUtil.format(now, DateUtil.DATE_FORMAT);
        String pfmCloseTime = getPfmCloseTime(bgId);
        String[] split = pfmCloseTime.split(",");
        String num = split[0];

        List<ItfSysCal> workDays = sysCalFeignClient.getWorkDaysByYm(DateUtil.format(now, DateUtil.DATE_MONTH_FOTMAT2))
                .getData(true);
        ItfSysCal itfSysCal = workDays.get(Integer.parseInt(num) - 1);
        String date = itfSysCal.getDate();
        Date evalMonth;
        if (nowDay.compareTo(date) >= 0) {
            evalMonth = DateUtil.getDeltaDate(now, Calendar.MONTH, -1);
        } else {
            evalMonth = DateUtil.getDeltaDate(now, Calendar.MONTH, -2);
        }
        return DateUtil.format(evalMonth, DateUtil.DATE_MONTH_FOTMAT);
    }

    @Override
    public Boolean hasYearPfm() {
        String year = DateUtil.format(new Date(), DateUtil.DATE_YEAR);
        List<IDbCondition> sysUserPfmConds = new ArrayList<>();
        sysUserPfmConds.add(new DC_E(DFN.common_isValid, true));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), PrjMdConstant.AI_EMP_YEAR_PFM.getCid()));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_year, Integer.valueOf(year)));
        return sysUserPfmDao.countByConds(sysUserPfmConds) > 0;
    }

    @Override
    public Boolean hasLockedYearPfm() {
        String year = DateUtil.format(new Date(), DateUtil.DATE_YEAR);
        List<IDbCondition> sysUserPfmConds = new ArrayList<>();
        sysUserPfmConds.add(new DC_E(DFN.common_isValid, true));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_isLocked, true));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), PrjMdConstant.AI_EMP_YEAR_PFM.getCid()));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_year, Integer.valueOf(year)));
        return sysUserPfmDao.countByConds(sysUserPfmConds) > 0;
    }

    @Override
    public void computerYearIntegratedPfm(TeSysUser loginUser) {
        Date now = new Date();
        Integer year = Integer.parseInt(DateUtil.format(now, DateUtil.DATE_YEAR));
        String ym = DateUtil.format(now, DateUtil.DATE_MONTH_FOTMAT);
        // 将本年度所有记录的isValid都置为false后，再进行考评处理。
        this.setPfmNotValidByYear(year);

        List<ItfSysDef> buList = sysDefFeignClient.listSysDefByDefTypeId(PrjConstant.DEF_BU_ID).getData(true);
        Map<String, com.linkus.base.db.mongo.model.TeIdNameCn> buId2bu = new HashMap<>();
        for (ItfSysDef buDef : buList) {
            com.linkus.base.db.mongo.model.TeIdNameCn bu = new com.linkus.base.db.mongo.model.TeIdNameCn();
            bu.setCodeName(buDef.getCodeName());
            bu.setCid(buDef.getId());
            bu.setName(buDef.getDefName());
            buId2bu.put(bu.getCodeName(), bu);
        }

        String provMon = DateUtil.getDeltaYm(now, -1);
        Date lastDayByMonth = DateUtil.getLastDayByMonth(parseDate(provMon, DateUtil.DATE_MONTH_FOTMAT2));
        String lastDayProvMonth = DateUtil.format(lastDayByMonth, DateUtil.DATE_FORMAT);
        List<String> dates = Collections.singletonList(lastDayProvMonth);
        List<String> employeeTypes = Arrays.asList("Employee", "Outsource1", "Trainee");

        ItfAiEmpHistQuery empHistQuery = ItfAiEmpHistQuery.builder()
                .dates(Arrays.asList("2024-09-04")) // todo dates上线需恢复
                .currentFlag(PrjMdConstant.Y)
                .bgId(PrjMdConstant.ASIA_DEF_ORG_ID)
                .employeeTypes(employeeTypes).build();
        List<ItfAiEmpHist> empHists = itfAiEmpHistFeignClient.queryDataByConds(empHistQuery).getData(true);

        if (CollectionUtils.isEmpty(empHists)) {
            throw BusinessException.initExc("获取上个月最后一天的人员历史数据为空！");
        }

        com.linkus.base.db.mongo.model.TeUser addUser = new com.linkus.base.db.mongo.model.TeUser();
        addUser.setUserId(loginUser.getId());
        addUser.setJobCode(loginUser.getJobCode());
        addUser.setUserName(loginUser.getUserName());
        addUser.setLoginName(loginUser.getLoginName());

        List<ObjectId> empIds = empHists.stream().filter(s -> Objects.nonNull(s.getDmpUser()))
                .map(s -> s.getDmpUser().getUserId()).collect(Collectors.toList());
        // 先处理timeSheetFlag为N的，更新人员下的desc为“填工时属性为N（含销售族群）的员工不参与考评！”。
        List<TeSysUserPfm> userYearPfmList = new ArrayList<>();
        List<ObjectId> notPfmEmpIds = new ArrayList<>();
        this.dealNotEvalEmp(empHists, userYearPfmList, notPfmEmpIds, buId2bu, addUser, year, ym, now);

        // 获取AIPM中已有的年度考评分，即从“sysUserPfm”中取出isValid为true下的且type.cid为ObjectId("5f90e4dd4396e16e7e92de80")即PM年度运营绩效下的
        // 且year为该年度下的且isLocked为true下的记录，
        List<IDbCondition> sysUserPfmConds = new ArrayList<>();
        sysUserPfmConds.add(new DC_E(DFN.common_isValid, true));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_isLocked, true));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), PrjMdConstant.AIPM_YEAR_JIXIAO_ID));
        sysUserPfmConds.add(new DC_E(DFN.sysUserPfm_year, year));
        List<TeSysUserPfm> aipmEmps = sysUserPfmDao.findByFieldAndConds(sysUserPfmConds, null);
        List<ObjectId> pmEmpIds = aipmEmps.stream().filter(s -> Objects.nonNull(s.getEmp()))
                .map(s -> s.getEmp().getUserId()).collect(Collectors.toList());
        this.dealPmPfm(aipmEmps, userYearPfmList, year, ym);

        empIds.removeAll(notPfmEmpIds);
        empIds.removeAll(pmEmpIds);

        this.dealOtherEmpYearPfm(empHists, userYearPfmList, empIds, buId2bu, addUser, year, ym, now);

        List<TeSysUserPfm> toSaveData = new ArrayList<>();
        List<String> ccCodeList = userYearPfmList.stream().map(TeSysUserPfm::getCcCode).collect(Collectors.toList());
        List<ItfDept> ccList = itfDeptFeignClient.queryDataByOrgCode(ccCodeList).getData(true);
        Map<String, String> ccCode2Name = ccList.stream()
                .collect(Collectors.toMap(ItfDept::getOrgCode, ItfDept::getOrgName));

        for (TeSysUserPfm empYearPfm : userYearPfmList) {
            empYearPfm.setCcName(ccCode2Name.get(empYearPfm.getCcCode()));
            toSaveData.add(empYearPfm);
            if (toSaveData.size() == 1000) {
                sysUserPfmDao.batchSave(toSaveData);
                toSaveData.clear();
            }
        }

        if (CollectionUtils.isNotEmpty(toSaveData)) {
            sysUserPfmDao.batchSave(toSaveData);
        }
    }

    // 将之前计算的本年度的综合绩效置为无效
    private void setPfmNotValidByYear(Integer year) {
        List<IDbCondition> updateConds = new ArrayList<>();
        updateConds.add(new DC_E(DFN.common_isValid, true));
        updateConds.add(new DC_E(DFN.sysUserPfm_year, year));
        updateConds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), PrjMdConstant.AI_EMP_YEAR_PFM.getCid()));
        List<UpdataData> updataDataList = new ArrayList<>();
        updataDataList.add(new UpdataData(DFN.common_isValid, false));
        sysUserPfmDao.updateByConds(updateConds, updataDataList);
    }

    private void dealNotEvalEmp(List<ItfAiEmpHist> empHists, List<TeSysUserPfm> userPfm, List<ObjectId> notPfmEmpIds,
                                Map<String, com.linkus.base.db.mongo.model.TeIdNameCn> buId2bu,
                                com.linkus.base.db.mongo.model.TeUser addUser,
                                Integer year, String ym, Date now) {
        TeSysUserPfm empYearPfm;
        for (ItfAiEmpHist empHist : empHists) {
            if ("N".equals(empHist.getTimeSheetFlag())) {
                empYearPfm = new TeSysUserPfm();
                empYearPfm.setIsValid(true);
                empYearPfm.setIsLocked(false);
                empYearPfm.setYear(year);
                empYearPfm.setYm(ym);
                empYearPfm.setDesc("填工时属性为N（含销售族群）的员工不参与考评");
                empYearPfm.setAddTime(now);
                empYearPfm.setAddUser(addUser);
                empYearPfm.setType(PrjMdConstant.AI_EMP_YEAR_PFM);
                empYearPfm.setSbu(buId2bu.get(empHist.getSbuId()));
                empYearPfm.setCcCode(empHist.getCostCenterId());
                empYearPfm.setEmployeeType(empHist.getEmployeeType());
                TeUser dmpUser = empHist.getDmpUser();
                if (Objects.isNull(dmpUser)) {
                    continue;
                }
                com.linkus.base.db.mongo.model.TeUser emp = new com.linkus.base.db.mongo.model.TeUser();
                emp.setUserId(dmpUser.getUserId());
                emp.setUserName(dmpUser.getUserName());
                emp.setLoginName(dmpUser.getLoginName());
                emp.setJobCode(dmpUser.getJobCode());
                empYearPfm.setEmp(emp);
                userPfm.add(empYearPfm);
                notPfmEmpIds.add(empHist.getId());
            }
        }
    }

    private void dealOtherEmpYearPfm(List<ItfAiEmpHist> empHists, List<TeSysUserPfm> userYearPfmList,
                                     List<ObjectId> empIds,
                                     Map<String, com.linkus.base.db.mongo.model.TeIdNameCn> buId2bu,
                                     com.linkus.base.db.mongo.model.TeUser addUser, Integer year, String ym, Date now) {

        Calendar calendar = Calendar.getInstance();
        int thisYear = calendar.get(Calendar.YEAR);
        Calendar firstDayOfYear = Calendar.getInstance();
        firstDayOfYear.set(thisYear, Calendar.JANUARY, 1);
        Calendar lastDayOfYear = Calendar.getInstance();
        lastDayOfYear.set(thisYear, Calendar.DECEMBER, 31);
        List<String> monthRange = DateUtil.getMonthRange(firstDayOfYear.getTime(), lastDayOfYear.getTime(),
                DateUtil.DATE_MONTH_FOTMAT);

        Criteria criteria = Criteria.where(DbFieldName.PrjEmpYmPfm.isValid.n()).is(true)
                .and(DbFieldName.PrjEmpYmPfm.ym.n()).in(monthRange)
                .and(DbFieldName.PrjEmpYmPfm.emp.dot(DbFieldName.common_userId).n()).in(empIds);
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("emp.userId")
                        .count().as("pfmMonths")
                        .sum("stdMd").as("stdMds")
                        .sum("prjMd").as("prjMds")
                        .sum("prjMdPfm").as("pfmPrjMds")
                        .avg("score").as("score"));

        List<Map> mapList = mongoTemplate.aggregate(agg, "prjEmpYmPfm", Map.class).getMappedResults();

        Map<ObjectId, Map<String, String>> infoMap = new HashMap<>();
        for (Map map : mapList) {
            ObjectId id = new ObjectId(map.get("_id").toString());
            Map<String, String> itemMap = new HashMap<>();
            itemMap.put("stdMds", StringUtil.getNotNullStr(map.get("stdMds")));
            itemMap.put("prjMds", StringUtil.getNotNullStr(map.get("prjMds")));
            itemMap.put("pfmPrjMds", StringUtil.getNotNullStr(map.get("pfmPrjMds")));
            itemMap.put("pfmMonths", StringUtil.getNotNullStr(map.get("pfmMonths")));
            itemMap.put("score", StringUtil.getNotNullStr(map.get("score")));
            infoMap.put(id, itemMap);
        }

        TeSysUserPfm empYearPfm;
        for (ItfAiEmpHist empHist : empHists) {
            TeUser dmpUser = empHist.getDmpUser();
            if (Objects.isNull(dmpUser) || !empIds.contains(dmpUser.getUserId())) {
                continue;
            }

            String hireDate = empHist.getHireDate();
            if (StringUtil.isEmpty(hireDate)) {
                continue;
            }
            String workMonth = getWorkMonth(hireDate);

            empYearPfm = new TeSysUserPfm();
            empYearPfm.setIsValid(true);
            empYearPfm.setIsLocked(false);
            empYearPfm.setYear(year);
            empYearPfm.setYm(ym);
            empYearPfm.setDesc("填工时属性为N（含销售族群）的员工不参与考评");
            empYearPfm.setAddTime(now);
            empYearPfm.setAddUser(addUser);
            empYearPfm.setType(PrjMdConstant.AI_EMP_YEAR_PFM);
            empYearPfm.setSbu(buId2bu.get(empHist.getSbuId()));
            empYearPfm.setCcCode(empHist.getCostCenterId());
            empYearPfm.setEmployeeType(empHist.getEmployeeType());

            com.linkus.base.db.mongo.model.TeUser emp = new com.linkus.base.db.mongo.model.TeUser();
            emp.setUserId(dmpUser.getUserId());
            emp.setUserName(dmpUser.getUserName());
            emp.setLoginName(dmpUser.getLoginName());
            emp.setJobCode(dmpUser.getJobCode());
            empYearPfm.setEmp(emp);

            List<TeSysUserPfm2InfoItem> infoItems = new ArrayList<>();
            Map<String, String> infoItem = infoMap.get(dmpUser.getUserId());

            if (infoItem == null || infoItem.size() == 0) {
                continue;
            }
            // if (StringUtil.isNotEmpty(infoItem.get("pfmMonths")) &&
            // Integer.parseInt(infoItem.get("pfmMonths")) >= Integer.parseInt(workMonth) /
            // 2) {
            if (StringUtil.isNotEmpty(infoItem.get("pfmMonths"))
                    && Integer.parseInt(infoItem.get("pfmMonths")) >= Integer.parseInt(workMonth) / 20) { // todo
                // 上线恢复为上一行
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.PFM_MONTHS)
                        .value(infoItem.get("pfmMonths")).build());
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.STD_MDS).value(infoItem.get("stdMds"))
                        .build());
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.PRJ_MDS).value(infoItem.get("prjMds"))
                        .build());
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.PFM_PRJ_MDS)
                        .value(infoItem.get("pfmPrjMds")).build());
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.YEAR_ROLE)
                        .value(PrjMdConstant.MD_ROLE_NAME_EMPLOYEE).build());
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.WORK_MONTHS).value(workMonth).build());
                empYearPfm.setScore(
                        StringUtil.isNotEmpty(infoItem.get("score")) ? Double.valueOf(infoItem.get("score")) : null);
            } else {
                infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.YEAR_ROLE)
                        .value(PrjMdConstant.MD_ROLE_NAME_EMPLOYEE).build());
                empYearPfm.setDesc("当年有月度综合绩效的月份数未达到在职月份数的一半");
            }
            empYearPfm.setInfoItems(infoItems);
            userYearPfmList.add(empYearPfm);
        }
    }

    private void dealPmPfm(List<TeSysUserPfm> aipmEmps, List<TeSysUserPfm> userPfm, Integer year, String ym) {
        TeSysUserPfm empYearPfm;
        for (TeSysUserPfm aipmEmp : aipmEmps) {
            empYearPfm = new TeSysUserPfm();
            empYearPfm.setIsValid(true);
            empYearPfm.setIsLocked(false);
            empYearPfm.setYear(year);
            empYearPfm.setYm(ym);
            empYearPfm.setAddTime(aipmEmp.getAddTime());
            empYearPfm.setAddUser(aipmEmp.getAddUser());
            empYearPfm.setType(PrjMdConstant.AI_EMP_YEAR_PFM);
            empYearPfm.setSbu(aipmEmp.getSbu());
            empYearPfm.setCcCode(aipmEmp.getCcCode());
            empYearPfm.setEmployeeType(aipmEmp.getEmployeeType());
            empYearPfm.setEmp(aipmEmp.getEmp());
            empYearPfm.setScore(aipmEmp.getScore());

            List<TeSysUserPfm2InfoItem> infoItems = new ArrayList<>();
            infoItems.add(TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.YEAR_ROLE)
                    .value(PrjMdConstant.MD_ROLE_NAME_PM).build());
            infoItems.add(
                    TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.PFM_MONTHS).value(aipmEmp.getText1()).build());
            infoItems.add(
                    TeSysUserPfm2InfoItem.builder().item(PrjMdConstant.WORK_MONTHS).value(aipmEmp.getText2()).build());
            empYearPfm.setInfoItems(infoItems);

            userPfm.add(empYearPfm);
        }
    }

    // 获取年度在职月份数
    private String getWorkMonth(String hireDate) {
        if (StringUtil.isEmpty(hireDate)) {
            return "0";
        }
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        int lastMonthNum = instance.get(Calendar.MONTH);
        String firstDayThisYear = instance.getWeekYear() + "-01-01";
        if (hireDate.compareTo(firstDayThisYear) < 0) {
            return String.valueOf(lastMonthNum);
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(Objects.requireNonNull(DateUtil.parseDate(hireDate, DateUtil.DATE_FORMAT)));
            int hireMonthNum = calendar.get(Calendar.MONTH) + 1;
            return String.valueOf(lastMonthNum - hireMonthNum + 1);
        }
    }

    @Override
    public void lockYearIntegratedPfm() {
        // “sysUserPfm”中isValid为true下的且type.cid为ObjectId("61654cfeabef7f8c4911748f")下的且year为该年度下的记录下的isLocked都只为true即可。
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int year = calendar.get(Calendar.YEAR);
        List<IDbCondition> updateConds = new ArrayList<>();
        updateConds.add(new DC_E(DFN.common_isValid, true));
        updateConds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), PrjMdConstant.AI_EMP_YEAR_PFM.getCid()));
        updateConds.add(new DC_E(DFN.sysUserPfm_year, year));

        List<UpdataData> updataDataList = new ArrayList<>();
        updataDataList.add(new UpdataData(DFN.sysUserPfm_isLocked, true));
        sysUserPfmDao.updateByConds(updateConds, updataDataList);
    }

    @Override
    public PageBean<MonthPrjPfmVo> monthPrjPfm(PfmQueryVo vo, TeSysUser loginUser) {
        List<Document> list = new ArrayList<>();
        Document match = new Document();
        // 如果是普通员工，仅允许查询本人的绩效数据
        if (vo.getIsEmployee()) {
            match = new Document()
                    .append(DbFieldName.PrjEmpMdPfm.isValid.n(), true)
                    .append(DbFieldName.PrjEmpMdPfm.ym.n(), vo.getYm().replace("-", ""))
                    .append(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(),
                            loginUser.getLoginName());
        } else {
            match = getMonthPrjPfmMatchByParam(vo);
        }
        list.add(new Document("$match", match));

        Document projectFirst = new Document();
        Document filter = new Document().append("input", "$toEvalUsers").append("as", "item")
                .append("cond", new Document("$eq", Arrays.asList("$$item.role.codeName", "pm")));
        projectFirst.append("_id", 1).append("ym", 1)
                .append("empName", "$emp.userName")
                .append("empNt", "$emp.loginName")
                .append("empJobCode", "$emp.jobCode")
                .append("employeeType", "$employeeType")
                .append("evalUser", new Document("$filter", filter))
                .append("pfmValue", "$pfmValue")
                .append("pmName", "$pm.userName")
                .append("pmNt", "$pm.loginName")
                .append("prjCode", "$prj.codeName")
                .append("prjName", "$prj.name")
                .append("md", 1);

        Document projectSec = new Document();
        Document evalUserName = new Document("$arrayElemAt", Arrays.asList("$evalUser.user.userName", 0));
        Document evalUserLoginName = new Document("$arrayElemAt", Arrays.asList("$evalUser.user.loginName", 0));
        Document concat = new Document("$concat", Arrays.asList(evalUserName, "/", evalUserLoginName));
        projectSec.append("_id", 1).append("ym", 1)
                .append("empName", 1)
                .append("empNt", 1)
                .append("empJobCode", 1)
                .append("employeeType", 1)
                .append("evalUser", new Document("$concat", concat))
                .append("pfmValue", 1)
                .append("pmName", 1)
                .append("pmNt", 1)
                .append("prjCode", 1)
                .append("prjName", 1)
                .append("md", 1);
        list.add(new Document("$project", projectFirst));
        list.add(new Document("$project", projectSec));

        Document facet = new Document("$facet", getFacetByParam(vo));
        list.add(facet);

        MongoCursor<Document> cursor = mongoTemplate.getCollection("prjEmpMdPfm").aggregate(list, Document.class)
                .allowDiskUse(true).cursor();
        Document next = cursor.next();
        PageBean<MonthPrjPfmVo> bean = new PageBean<>();
        List<Document> data = (List<Document>) next.get("data");
        if (CollectionUtils.isEmpty(data)) {
            bean.setCount(0L);
            bean.setObjectList(new ArrayList<>());
            return bean;
        }
        ArrayList<MonthPrjPfmVo> resultList = new ArrayList<>();
        int no = 1;
        for (Document datum : data) {
            MonthPrjPfmVo read = mongoConverter.read(MonthPrjPfmVo.class, datum);
            read.setNo(no++);
            String employeeType = read.getEmployeeType();
            switch (employeeType) {
                case "Employee":
                    read.setEmployeeType("正式");
                    break;
                case "Outsource1":
                    read.setEmployeeType("外包");
                    break;
                case "Trainee":
                    read.setEmployeeType("实习");
                    break;
                default:
                    break;
            }
            resultList.add(read);
        }
        Document total = (Document) ((List<?>) next.get("total")).get(0);
        bean.setCount(StringUtil.toLong(total.get("total")));
        bean.setObjectList(resultList);
        return bean;
    }

    private Document getMonthPrjPfmMatchByParam(PfmQueryVo vo) {
        List<String> empBuIds = vo.getEmpBuIds();
        List<String> empCcIds = vo.getEmpCcIds();
        String subordinateType = vo.getSubordinateType();

        if (CollectionUtils.isEmpty(empBuIds) && CollectionUtils.isEmpty(empCcIds)
                && StringUtil.isEmpty(subordinateType)) {
            throw BusinessException.initExc("人员所属BU、人员所示CC、下属类型三者之一必选，才能进行查询!");
        }

        Document match = new Document()
                .append(DbFieldName.PrjEmpMdPfm.isValid.n(), true)
                .append(DbFieldName.PrjEmpMdPfm.ym.n(), vo.getYm().replace("-", ""));

        if (CollectionUtils.isNotEmpty(empBuIds)) {
            match.append(DbFieldName.PrjEmpMdPfm.empBu.n(), new Document("$in", empBuIds));
        }
        if (StringUtil.isNotEmpty(vo.getEmployeeType())) {
            match.append(DbFieldName.PrjEmpMdPfm.employeeType.n(), vo.getEmployeeType());
        }
        if (StringUtil.isNotEmpty(vo.getPrjKey())) {
            org.bson.Document prjCodeDoc = new org.bson.Document();
            prjCodeDoc.append("$regex", vo.getPrjKey()).append("$options", "i");
            org.bson.Document prjNameDoc = new org.bson.Document();
            prjNameDoc.append("$regex", vo.getPrjKey()).append("$options", "i");

            match.append("$or",
                    Arrays.asList(new Document(DbFieldName.PrjEmpMdPfm.prj.dot(DbFieldName.common_cn).n(), prjCodeDoc),
                            new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_name).n(), prjNameDoc)));
        }
        if (StringUtil.isNotEmpty(vo.getPmKey()) && StringUtil.isEmpty(vo.getEmpKey())) {
            org.bson.Document pmJobCodeDoc = new org.bson.Document();
            pmJobCodeDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            org.bson.Document pmNameDoc = new org.bson.Document();
            pmNameDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            org.bson.Document pmLoginNameDoc = new org.bson.Document();
            pmLoginNameDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            match.append("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_jobCode).n(), pmJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_userName).n(), pmNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_loginName).n(), pmLoginNameDoc)));
        }
        if (StringUtil.isNotEmpty(vo.getEmpKey()) && StringUtil.isEmpty(vo.getPmKey())) {
            org.bson.Document empJobCodeDoc = new org.bson.Document();
            empJobCodeDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empNameDoc = new org.bson.Document();
            empNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empLoginNameDoc = new org.bson.Document();
            empLoginNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            match.append("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_jobCode).n(), empJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_userName).n(), empNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(), empLoginNameDoc)));
        }
        if (StringUtil.isNotEmpty(vo.getPmKey()) && StringUtil.isNotEmpty(vo.getEmpKey())) {
            org.bson.Document pmJobCodeDoc = new org.bson.Document();
            pmJobCodeDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            org.bson.Document pmNameDoc = new org.bson.Document();
            pmNameDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            org.bson.Document pmLoginNameDoc = new org.bson.Document();
            pmLoginNameDoc.append("$regex", vo.getPmKey()).append("$options", "i");
            Document pm = new Document("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_jobCode).n(), pmJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_userName).n(), pmNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_loginName).n(), pmLoginNameDoc)));

            // match.append("$or", Arrays.asList(new
            // Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_jobCode).n(),
            // empJobCodeDoc),
            // new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_userName).n(),
            // empNameDoc),
            // new
            // Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_loginName).n(),
            // empLoginNameDoc)));
            // Document pm = new Document("$or", Arrays.asList(new
            // Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_jobCode).n(),
            // vo.getPmKey()),
            // new Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_userName).n(),
            // vo.getPmKey()),
            // new
            // Document(DbFieldName.PrjEmpMdPfm.pm.dot(DbFieldName.common_loginName).n(),
            // vo.getPmKey())));
            // Document emp = new Document("$or", Arrays.asList(new
            // Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_jobCode).n(),
            // vo.getEmpKey()),
            // new
            // Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_userName).n(),
            // vo.getEmpKey()),
            // new
            // Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(),
            // vo.getEmpKey())));
            org.bson.Document empJobCodeDoc = new org.bson.Document();
            empJobCodeDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empNameDoc = new org.bson.Document();
            empNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empLoginNameDoc = new org.bson.Document();
            empLoginNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            Document emp = new Document("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_jobCode).n(), empJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_userName).n(), empNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(), empLoginNameDoc)));
            match.append("$and", Arrays.asList(pm, emp));
        }
        // 下属类型
        if (StringUtil.isNotEmpty(subordinateType)) {
            List<String> ccOrdCodes;
            if (CollectionUtils.isNotEmpty(empCcIds)) {
                ccOrdCodes = new ArrayList<>(empCcIds);
            } else {
                List<DeptMsg> ccList = sysDeptFeignClient.queryBelongToCc(new ArrayList<>()).getData(true);
                ccOrdCodes = ccList.stream().map(DeptMsg::getOrgCode).collect(Collectors.toList());
            }

            List<ItfDept> ccList = itfDeptFeignClient.queryDataByOrgCode(ccOrdCodes).getData(true);
            List<String> childCcOrgCode = new ArrayList<>();
            if (PrjMdConstant.DIRECT_REPORTS.equals(subordinateType)) {
                // 直接下属
                List<String> ccOrgIds = ccList.stream().map(ItfDept::getOrgId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByOrgParentIds(ccOrgIds).getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            } else if (PrjMdConstant.ALL_REPORTS.equals(subordinateType)) {
                // 所有下属
                List<ObjectId> ccIds = ccList.stream().map(ItfDept::getId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByParent2SelfIds(new ArrayList<>(ccIds), false)
                        .getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            }
            match.append(DbFieldName.PrjEmpMdPfm.empCc.n(), new Document("$in", childCcOrgCode));
        } else if (CollectionUtils.isNotEmpty(empCcIds)) {
            match.append(DbFieldName.PrjEmpMdPfm.empCc.n(), new Document("$in", empCcIds));
        }
        return match;
    }

    private Document getFacetByParam(PfmQueryVo vo) {
        Document facetDoc = new Document();
        if (vo.getPageIndex() != null && vo.getPageSize() != null) {
            Document skip = new Document("$skip", vo.getPageIndex() * vo.getPageSize());
            Document limit = new Document("$limit", vo.getPageSize());
            facetDoc.append("total", Collections.singletonList(new Document("$count", "total")))
                    .append("data", Arrays.asList(skip, limit));
        } else {
            Document skip = new Document("$skip", 0);
            facetDoc.append("total", Collections.singletonList(new Document("$count", "total")))
                    .append("data", Collections.singletonList(skip));
        }
        return facetDoc;
    }

    @Override
    public PageBean<MonthIntegratedPfmVo> monthIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser) {
        List<Document> list = new ArrayList<>();
        if (vo.getIsEmployee()) {
            Document match = new Document()
                    .append(DbFieldName.PrjEmpMdPfm.isValid.n(), true)
                    .append(DbFieldName.PrjEmpMdPfm.ym.n(), vo.getYm().replace("-", ""))
                    .append(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(),
                            loginUser.getLoginName());
            list.add(new Document("$match", match));

            Document userLookup = new Document()
                    .append("from", "sysUser")
                    .append("localField", "emp.userId")
                    .append("foreignField", "_id")
                    .append("as", "sysUser");
            list.add(new Document("$lookup", userLookup));
            list.add(new Document("$unwind", "$sysUser"));
        } else {
            list = getMonthIntegratedPfmDocsByParam(vo);
        }
        Document project = new Document()
                .append("_id", 1)
                .append("empName", "$emp.userName")
                .append("empNt", "$emp.loginName")
                .append("empJobCode", "$emp.jobCode")
                .append("employeeType", "$sysUser.employeeType")
                .append("stdMd", 1)
                .append("prjMd", 1)
                .append("prjMdPfm", 1)
                .append("prjMdScore", 1)
                .append("notPrjMdPfm", new Document("$subtract", Arrays.asList("$prjMd", "$prjMdPfm")))
                .append("prjNum", new Document("$size", "$prjs"))
                .append("pfmPrjNum", new Document("$size", "$pfmPrjs"))
                .append("scorePrjNum", new Document("$size", "$scorePrjs"))
                .append("notPfmPrjNum", new Document("$subtract", Arrays.asList("$prjNum", "$pfmPrjNum")))
                .append("score", 1);
        list.add(new Document("$project", project));

        Document facet = new Document("$facet", getFacetByParam(vo));
        list.add(facet);

        MongoCursor<Document> cursor = mongoTemplate.getCollection("prjEmpYmPfm").aggregate(list, Document.class)
                .allowDiskUse(true).cursor();
        Document next = cursor.next();
        PageBean<MonthIntegratedPfmVo> bean = new PageBean<>();
        List<Document> data = (List<Document>) next.get("data");
        if (CollectionUtils.isEmpty(data)) {
            bean.setCount(0L);
            bean.setObjectList(new ArrayList<>());
            return bean;
        }
        ArrayList<MonthIntegratedPfmVo> resultList = new ArrayList<>();
        int no = 1;
        for (Document datum : data) {
            MonthIntegratedPfmVo read = mongoConverter.read(MonthIntegratedPfmVo.class, datum);
            read.setNo(no++);
            String employeeType = read.getEmployeeType();
            switch (employeeType) {
                case "Employee":
                    read.setEmployeeType("正式");
                    break;
                case "Outsource1":
                    read.setEmployeeType("外包");
                    break;
                case "Trainee":
                    read.setEmployeeType("实习");
                    break;
                default:
                    break;
            }
            resultList.add(read);
        }
        Document total = (Document) ((List<?>) next.get("total")).get(0);
        bean.setCount(StringUtil.toLong(total.get("total")));
        bean.setObjectList(resultList);
        return bean;
    }

    private List<Document> getMonthIntegratedPfmDocsByParam(PfmQueryVo vo) {
        List<String> empBuIds = vo.getEmpBuIds();
        List<String> empCcIds = vo.getEmpCcIds();
        String subordinateType = vo.getSubordinateType();

        if (CollectionUtils.isEmpty(empBuIds) && CollectionUtils.isEmpty(empCcIds)
                && StringUtil.isEmpty(subordinateType)) {
            throw BusinessException.initExc("人员所属BU、人员所示CC、下属类型三者之一必选，才能进行查询!");
        }

        List<Document> list = new ArrayList<>();
        Document match = new Document()
                .append(DbFieldName.PrjEmpYmPfm.isValid.n(), true)
                .append(DbFieldName.PrjEmpYmPfm.ym.n(), vo.getYm().replace("-", ""));

        if (CollectionUtils.isNotEmpty(empBuIds)) {
            match.append(DbFieldName.PrjEmpYmPfm.buCode.n(), new Document("$in", empBuIds));
        }
        if (StringUtil.isNotEmpty(vo.getEmpKey())) {
            org.bson.Document empJobCodeDoc = new org.bson.Document();
            empJobCodeDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empNameDoc = new org.bson.Document();
            empNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empLoginNameDoc = new org.bson.Document();
            empLoginNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            match.append("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_jobCode).n(), empJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_userName).n(), empNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(), empLoginNameDoc)));
        }
        // 下属类型
        if (StringUtil.isNotEmpty(subordinateType)) {
            List<String> ccOrdCodes;
            if (CollectionUtils.isNotEmpty(empCcIds)) {
                ccOrdCodes = new ArrayList<>(empCcIds);
            } else {
                List<DeptMsg> ccList = sysDeptFeignClient.queryBelongToCc(new ArrayList<>()).getData(true);
                ccOrdCodes = ccList.stream().map(DeptMsg::getOrgCode).collect(Collectors.toList());
            }

            List<ItfDept> ccList = itfDeptFeignClient.queryDataByOrgCode(ccOrdCodes).getData(true);
            List<String> childCcOrgCode = new ArrayList<>();
            if (PrjMdConstant.DIRECT_REPORTS.equals(subordinateType)) {
                // 直接下属
                List<String> ccOrgIds = ccList.stream().map(ItfDept::getOrgId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByOrgParentIds(ccOrgIds).getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            } else if (PrjMdConstant.ALL_REPORTS.equals(subordinateType)) {
                // 所有下属
                List<ObjectId> ccIds = ccList.stream().map(ItfDept::getId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByParent2SelfIds(new ArrayList<>(ccIds), false)
                        .getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            }
            match.append(DbFieldName.PrjEmpYmPfm.ccId.n(), new Document("$in", childCcOrgCode));
        } else if (CollectionUtils.isNotEmpty(empCcIds)) {
            match.append(DbFieldName.PrjEmpYmPfm.ccId.n(), new Document("$in", empCcIds));
        }
        list.add(new Document("$match", match));

        Document userLookup = new Document()
                .append("from", "sysUser")
                .append("localField", "emp.userId")
                .append("foreignField", "_id")
                .append("as", "sysUser");
        list.add(new Document("$lookup", userLookup));
        list.add(new Document("$unwind", "$sysUser"));

        if (StringUtil.isNotEmpty(vo.getEmployeeType())) {
            list.add(new Document("$match", new Document().append("sysUser.employeeType", vo.getEmployeeType())));
        }

        return list;
    }

    @Override
    public PageBean<YearIntegratedPfmVo> yearIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser) {
        List<Document> list = new ArrayList<>();
        Document match = new Document();
        if (vo.getIsEmployee()) {
            match = new Document()
                    .append(DbFieldName.sysUserPfm_isValid.n(), true)
                    .append(DbFieldName.sysUserPfm_isLocked.n(), true)
                    .append(DbFieldName.sysUserPfm_year.n(), vo.getYear())
                    .append(DbFieldName.sysUserPfm_type.dot(DbFieldName.sysUserPfm_cid).n(),
                            PrjMdConstant.AI_EMP_YEAR_PFM.getCid())
                    .append(DbFieldName.sysUserPfm_emp.dot(DbFieldName.sysUserPfm_loginName).n(),
                            loginUser.getLoginName());
        } else {
            match = getYearIntegratedPfmMatchByParam(vo);
        }
        list.add(new Document("$match", match));

        // 年度角色
        Document roleFilter = new Document().append("input", "$infoItems").append("as", "info")
                .append("cond", new Document("$eq",
                        Arrays.asList("$$info.item.codeName", PrjMdConstant.YEAR_ROLE.getCodeName())));
        // 有绩效月份数
        Document pfmMonthFilter = new Document().append("input", "$infoItems").append("as", "info")
                .append("cond", new Document("$eq",
                        Arrays.asList("$$info.item.codeName", PrjMdConstant.PFM_MONTHS.getCodeName())));
        // 在职月份数
        Document workMonthFilter = new Document().append("input", "$infoItems").append("as", "info")
                .append("cond", new Document("$eq",
                        Arrays.asList("$$info.item.codeName", PrjMdConstant.WORK_MONTHS.getCodeName())));
        // 工时合计天数
        Document prjMdsFilter = new Document().append("input", "$infoItems").append("as", "info")
                .append("cond", new Document("$eq",
                        Arrays.asList("$$info.item.codeName", PrjMdConstant.PRJ_MDS.getCodeName())));
        Document projectFirst = new Document()
                .append("_id", 1)
                .append("empName", "$emp.userName")
                .append("empNt", "$emp.loginName")
                .append("empJobCode", "$emp.jobCode")
                .append("employeeType", 1)
                .append("year", 1)
                .append("role", new Document("$filter", roleFilter))
                .append("buName", "$sbu.name")
                .append("ccName", 1)
                .append("workMonth", new Document("$filter", workMonthFilter))
                .append("pfmMonth", new Document("$filter", pfmMonthFilter))
                .append("score", 1)
                .append("desc", 1)
                .append("prjMds", new Document("$filter", prjMdsFilter));
        list.add(new Document("$project", projectFirst));

        Document projectSec = new Document()
                .append("_id", 1)
                .append("empName", 1)
                .append("empNt", 1)
                .append("empJobCode", 1)
                .append("employeeType", 1)
                .append("year", 1)
                .append("role", new Document("$arrayElemAt", Arrays.asList("$role.value", 0)))
                .append("buName", 1)
                .append("ccName", 1)
                .append("workMonth", new Document("$arrayElemAt", Arrays.asList("$workMonth.value", 0)))
                .append("pfmMonth", new Document("$arrayElemAt", Arrays.asList("$pfmMonth.value", 0)))
                .append("score", 1)
                .append("desc", 1)
                .append("prjMds", new Document("$arrayElemAt", Arrays.asList("$prjMds.value", 0)));
        list.add(new Document("$project", projectSec));

        Document facet = new Document("$facet", getFacetByParam(vo));
        list.add(facet);

        MongoCursor<Document> cursor = mongoTemplate.getCollection("sysUserPfm").aggregate(list, Document.class)
                .allowDiskUse(true).cursor();
        Document next = cursor.next();
        PageBean<YearIntegratedPfmVo> bean = new PageBean<>();
        List<Document> data = (List<Document>) next.get("data");
        if (CollectionUtils.isEmpty(data)) {
            bean.setCount(0L);
            bean.setObjectList(new ArrayList<>());
            return bean;
        }
        ArrayList<YearIntegratedPfmVo> resultList = new ArrayList<>();
        int no = 1;
        for (Document datum : data) {
            YearIntegratedPfmVo read = mongoConverter.read(YearIntegratedPfmVo.class, datum);
            read.setNo(no++);
            String employeeType = read.getEmployeeType();
            if (StringUtil.isNotEmpty(employeeType)) {
                switch (employeeType) {
                    case "Employee":
                        read.setEmployeeType("正式");
                        break;
                    case "Outsource1":
                        read.setEmployeeType("外包");
                        break;
                    case "Trainee":
                        read.setEmployeeType("实习");
                        break;
                    default:
                        break;
                }
            }
            resultList.add(read);
        }
        Document total = (Document) ((List<?>) next.get("total")).get(0);
        bean.setCount(StringUtil.toLong(total.get("total")));
        bean.setObjectList(resultList);
        return bean;
    }

    private Document getYearIntegratedPfmMatchByParam(PfmQueryVo vo) {
        List<String> empBuIds = vo.getEmpBuIds();
        List<String> empCcIds = vo.getEmpCcIds();
        String subordinateType = vo.getSubordinateType();

        if (CollectionUtils.isEmpty(empBuIds) && CollectionUtils.isEmpty(empCcIds)
                && StringUtil.isEmpty(subordinateType)) {
            throw BusinessException.initExc("人员所属BU、人员所示CC、下属类型三者之一必选，才能进行查询!");
        }

        Document match = new Document()
                .append(DbFieldName.sysUserPfm_isValid.n(), true)
                .append(DbFieldName.sysUserPfm_isLocked.n(), true)
                .append(DbFieldName.sysUserPfm_year.n(), vo.getYear())
                .append(DbFieldName.sysUserPfm_type.dot(DbFieldName.sysUserPfm_cid).n(),
                        PrjMdConstant.AI_EMP_YEAR_PFM.getCid());

        if (CollectionUtils.isNotEmpty(empBuIds)) {
            match.append(DbFieldName.sysUserPfm_sbu.dot(DbFieldName.common_cn).n(), new Document("$in", empBuIds));
        }
        if (StringUtil.isNotEmpty(vo.getEmployeeType())) {
            match.append(DbFieldName.sysUserPfm_employeeType.n(), vo.getEmployeeType());
        }
        if (StringUtil.isNotEmpty(vo.getEmpKey())) {
            org.bson.Document empJobCodeDoc = new org.bson.Document();
            empJobCodeDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empNameDoc = new org.bson.Document();
            empNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            org.bson.Document empLoginNameDoc = new org.bson.Document();
            empLoginNameDoc.append("$regex", vo.getEmpKey()).append("$options", "i");
            match.append("$or", Arrays.asList(
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_jobCode).n(), empJobCodeDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_userName).n(), empNameDoc),
                    new Document(DbFieldName.PrjEmpMdPfm.emp.dot(DbFieldName.common_loginName).n(), empLoginNameDoc)));
        }
        // 下属类型
        if (StringUtil.isNotEmpty(subordinateType)) {
            List<String> ccOrdCodes;
            if (CollectionUtils.isNotEmpty(empCcIds)) {
                ccOrdCodes = new ArrayList<>(empCcIds);
            } else {
                List<DeptMsg> ccList = sysDeptFeignClient.queryBelongToCc(new ArrayList<>()).getData(true);
                ccOrdCodes = ccList.stream().map(DeptMsg::getOrgCode).collect(Collectors.toList());
            }

            List<ItfDept> ccList = itfDeptFeignClient.queryDataByOrgCode(ccOrdCodes).getData(true);
            List<String> childCcOrgCode = new ArrayList<>();
            if (PrjMdConstant.DIRECT_REPORTS.equals(subordinateType)) {
                // 直接下属
                List<String> ccOrgIds = ccList.stream().map(ItfDept::getOrgId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByOrgParentIds(ccOrgIds).getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            } else if (PrjMdConstant.ALL_REPORTS.equals(subordinateType)) {
                // 所有下属
                List<ObjectId> ccIds = ccList.stream().map(ItfDept::getId).collect(Collectors.toList());
                List<ItfDept> childCc = itfDeptFeignClient.queryByParent2SelfIds(new ArrayList<>(ccIds), false)
                        .getData(true);
                childCcOrgCode = childCc.stream().map(ItfDept::getOrgCode).filter(StringUtil::isNotEmpty)
                        .collect(Collectors.toList());
            }
            match.append(DbFieldName.sysUserPfm_ccCode.n(), new Document("$in", childCcOrgCode));
        } else if (CollectionUtils.isNotEmpty(empCcIds)) {
            match.append(DbFieldName.sysUserPfm_ccCode.n(), new Document("$in", empCcIds));
        }
        return match;
    }

    @Override
    public void exportMonthPrjPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException {
        PageBean<MonthPrjPfmVo> resultVo = monthPrjPfm(vo, loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("月度项目绩效.xlsx", "UTF-8"));

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "月度项目绩效")
                .head(MonthPrjPfmVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(resultVo.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public void exportMonthIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response)
            throws IOException {
        PageBean<MonthIntegratedPfmVo> resultVo = monthIntegratedPfm(vo, loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("月度综合绩效.xlsx", "UTF-8"));

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "月度综合绩效")
                .head(MonthIntegratedPfmVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(resultVo.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public void exportYearIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response)
            throws IOException {
        PageBean<YearIntegratedPfmVo> resultVo = yearIntegratedPfm(vo, loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("年度综合绩效.xlsx", "UTF-8"));

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "年度综合绩效")
                .head(YearIntegratedPfmVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(resultVo.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public List<ItfDept> listPfmPermissionBu(TeSysUser loginUser) {
        List<ItfDept> buList = new ArrayList<>();
        // 获取填工时Bu清单
        List<String> buIdList = queryBuList();
        if (CollectionUtils.isEmpty(buIdList)) {
            throw BusinessException.initExc("获取填工时Bu为空!");
        }
        // 如果是BG绩效管理员，则是该BG下的所有Bu
        ItfRoleUserCommonQuery bgQuery = ItfRoleUserCommonQuery.builder()
                .roleIdList(Collections.singletonList(PrjMdConstant.ADMIN.getRoleId()))
                .defTypeIdList(Collections.singletonList(SysDefTypeCodeName.AI_BG.getId()))
                .build();
        List<ItfSysDefRoleUser> pfmBgAdmin = roleUserFeignClient.queryRoleUserList(bgQuery).getData(true);
        List<ObjectId> bgIds = new ArrayList<>();
        for (ItfSysDefRoleUser sysDefRoleUser : pfmBgAdmin) {
            TeUser roleUser = sysDefRoleUser.getRoleUser();
            if (roleUser != null && StringUtil.isNotEmpty(roleUser.getJobCode())
                    && roleUser.getJobCode().equals(loginUser.getJobCode())) {
                bgIds.add(sysDefRoleUser.getDefId());
            }
        }
        if (CollectionUtils.isNotEmpty(bgIds)) {
            List<ItfSysDef> bgDefList = sysDefFeignClient.querySysDefsByIds(bgIds).getData(true);
            List<String> bgCodeNames = bgDefList.stream().map(ItfSysDef::getCodeName).distinct()
                    .collect(Collectors.toList());
            List<ItfDept> bgDepts = itfDeptFeignClient.queryDataByOrgCode(bgCodeNames).getData(true);
            List<ObjectId> bgDeptIds = bgDepts.stream().map(ItfDept::getId).collect(Collectors.toList());
            buList = itfDeptFeignClient.queryByParent2SelfIds(bgDeptIds, true).getData(true);
            return buList.stream().filter(s -> buIdList.contains(s.getOrgCode())).collect(Collectors.toList());
        }

        // 如果不是，判断是否是Bu绩效接口人,如果是则返回对应Bu
        ItfRoleUserCommonQuery pfmBuAdminQuery = ItfRoleUserCommonQuery.builder()
                .roleIdList(Collections.singletonList(PrjMdConstant.ADMIN.getRoleId()))
                .defTypeIdList(Collections.singletonList(PrjMdConstant.SUBSYS_DEF.getCid()))
                .defIdList(Collections.singletonList(PrjMdConstant.PES_DEF.getCid()))
                .build();
        List<ItfSysDefRoleUser> roleUserList = roleUserFeignClient.queryRoleUserList(pfmBuAdminQuery).getData(true);
        if (CollectionUtils.isEmpty(roleUserList)) {
            return buList;
        }
        Set<String> orgCodes = new HashSet<>();
        for (ItfSysDefRoleUser sysDefRoleUser : roleUserList) {
            TeUser roleUser = sysDefRoleUser.getRoleUser();
            if (roleUser.getJobCode().equals(loginUser.getJobCode())) {
                orgCodes.add(sysDefRoleUser.getSrcDef().getSrcDefCodeName());
            }
        }
        if (CollectionUtils.isEmpty(orgCodes)) {
            return buList;
        }
        return itfDeptFeignClient.queryDataByOrgCode(new ArrayList<>(orgCodes)).getData(true);
    }

    @Override
    public CommonResult<Void> initPfmByPrjCode(String bgId, String prjCode) {
        // 先判断当前月的项目考评是否未关闭
        String evalYm = getEvalYm(bgId);
        String closeDay = getCloseDayOfEvalYm(bgId, evalYm);
        Date closeDate = parseDate(closeDay, DateUtil.DATE_FORMAT);
        Date today = new Date();
        if (today.after(closeDate)) {
            return CommonResult.fail("当前考评月项目绩效已关闭");
        }
        evalYm = "202408"; // todo 上线恢复

        // 再判断已选项目是否存在当前考评月的项目绩效数据
        PrjEmpMdPfmQuery query = PrjEmpMdPfmQuery.builder().isValid(true).ym(evalYm)
                .prjCodeList(Collections.singletonList(prjCode)).build();
        List<TePrjEmpMdPfm> mdPfmList = listMdPfms(query);
        if (CollectionUtils.isNotEmpty(mdPfmList)) {
            return CommonResult.fail("该项目【" + prjCode + "】已初始化，请勿重复操作");
        }
        // 再判断该项目是否属于考评范围内的项目
        List<ItfAiPrj> prjList = prjFeignClient.queryAiPrjsByCode(Collections.singletonList(prjCode)).getData(true);
        if (CollectionUtils.isEmpty(prjList)) {
            return CommonResult.fail("获取项目信息失败!");
        }
        ItfAiPrj aiPrj = prjList.get(0);
        String projectType = aiPrj.getProjectType();
        String subPrjType = "";
        if (PrjMdConstant.AI_PRJ_TYPE_ENGINEERING.equals(projectType)) {
            subPrjType = aiPrj.getPdProjectType();
        } else if (PrjMdConstant.AI_PRJ_TYPE_RD.equals(projectType)) {
            subPrjType = aiPrj.getRdProjectType();
        }

        List<String> pdList = Arrays.asList("建设类", "结算类", "维护类");
        List<String> rdList = Arrays.asList("P0(PRD)", "P1类");

        if (!(PrjMdConstant.AI_PRJ_TYPE_ENGINEERING.equals(projectType) && pdList.contains(subPrjType))
                && !(PrjMdConstant.AI_PRJ_TYPE_RD.equals(projectType) && rdList.contains(subPrjType))) {
            return CommonResult.fail("该项目的项目类型是" + projectType + "，项目子类型是" + subPrjType + "，不在项目绩效考评范围内");
        }

        // 初始化该项目绩效数据
        String eval_Ym = DateUtil.format(DateUtil.parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT),
                DateUtil.DATE_MONTH_FOTMAT2);
        List<RmsEmpDate> rmsEmpDateList = rmsEmpDateFeignClient
                .listPfmData(bgId, eval_Ym, new ArrayList<>(), Collections.singletonList(prjCode)).getData(true);
        if (CollectionUtils.isEmpty(rmsEmpDateList)) {
            return CommonResult.fail("获取该项目考评月工时数据为空!");
        }
        List<ObjectId> empIds = rmsEmpDateList.stream().map(s -> s.getEmp().getUserId()).distinct()
                .collect(Collectors.toList());
        ItfAiEmpHistQuery histQuery = ItfAiEmpHistQuery.builder()
                // .dates(Collections.singletonList(lastDayOfEvalMonthStr))
                .dates(Collections.singletonList("2024-08-30")) // todo 上线恢复上一行
                .currentFlag("Y").timeSheetFlag("Y")
                .dmpUserIds(empIds)
                .build();
        List<ItfAiEmpHist> empHists = itfAiEmpHistFeignClient.queryDataByConds(histQuery).getData(true);
        if (CollectionUtils.isEmpty(empHists)) {
            return CommonResult.fail("获取考评月(" + evalYm + ")人员历史数据为空!");
        }
        Map<ObjectId, ItfAiEmpHist> userIdToEmpHist = empHists.stream()
                .filter(item -> item.getDmpUser() != null && item.getDmpUser().getUserId() != null)
                .collect(Collectors.toMap(item -> item.getDmpUser().getUserId(), Function.identity(), (v1, v2) -> v2));
        List<ObjectId> userIds = new ArrayList<>(userIdToEmpHist.keySet());
        rmsEmpDateList = rmsEmpDateList.stream().filter(s -> userIds.contains(s.getEmp().getUserId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rmsEmpDateList)) {
            return CommonResult.fail("获取该项目考评月最后一天在职员工工时数据为空!");
        }

        // 初始化基础信息
        this.addBasicInfos(rmsEmpDateList, userIdToEmpHist, evalYm);
        // 初始化考评人信息
        List<ObjectId> prjIdList = prjList.stream().filter(s -> Objects.nonNull(s.getDmpPrj()))
                .map(s -> s.getDmpPrj().getCid()).collect(Collectors.toList());
        this.addToEvalUsers(rmsEmpDateList, prjIdList, evalYm, bgId, false);
        return CommonResult.success();
    }

    // 查询填工时Bu清单
    private List<String> queryBuList() {
        List<ItfSysDefCnfg> cnfgs = sysDefCnfgFeignClient.queryByCnfgType(PrjMdConstant.RMS_MD_REQUIRED_BU_LIST, null)
                .getData(true);
        if (com.alibaba.excel.util.CollectionUtils.isEmpty(cnfgs)) {
            throw BusinessException.initExc("填工时BU清单为空，请联系管理员!");
        }
        List<String> buList = new ArrayList<>();
        for (ItfSysDefCnfg sy : cnfgs) {
            TeIdNameCn firstDef = sy.getFirstDef();
            if (firstDef != null) {
                buList.add(firstDef.getCodeName());
            }
        }
        if (com.alibaba.excel.util.CollectionUtils.isEmpty(buList)) {
            throw BusinessException.initExc("填工时BU清单为空，请联系管理员!");
        }
        return buList;
    }

}
