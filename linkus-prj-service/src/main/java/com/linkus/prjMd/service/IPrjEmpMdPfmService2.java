package com.linkus.prjMd.service;

import com.linkus.common.model.TeUser;
import com.linkus.common.util.PageBean;
import com.linkus.common.web.CommonResult;
import com.linkus.itf.api.model.ItfAiPrj;
import com.linkus.itf.api.model.ItfDept;
import com.linkus.prj.api.model.BuPfmAdmin;
import com.linkus.prjMd.model.TePrjEmpMdPfm;
import com.linkus.prjMd.vo.*;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IPrjEmpMdPfmService2 {

    /**
     *通用查询
     */
    List<TePrjEmpMdPfm> listMdPfms(PrjEmpMdPfmQuery query);

    /**
     * 获取绩效关闭时间
     * 返回示例："6,23:59:59"
     */
    String getPfmCloseTime(String bgId);

    /**
     * 更新绩效关闭时间
     */
    String updatePfmCloseTime(String bgId, String num);

    /**
     * 项目绩效初始化
     * ym: yyyyMM
     */
    void initPfmData(String bgId, String ym);

    /**
     * 判断是否是考评人（项目经理、项目助理）
     */
    Boolean isEvaluator(String prjCode, ObjectId userId);

    /**
     * 查询时考评月份下，当前登录人员是考评人的 ”待考评“ 的项目清单，按一个项目一条记录显示。
     */
    PageBean<EvalDataVo> listMyToEvalPrj(String ym, Integer pageIndex, Integer pageSize, TeSysUser loginUser);

    /**
     * 获取考评项目
     */
    List<EvalDataVo> listEvalPrj(String ym, TeSysUser loginUser);

    /**
     * 统计显示当前考评项目，考评月份下，正式、外包员工的人员总数、工时人天数、以及实时显示不同分值区间的人员百分比。
     */
    EvalDataVo queryEvalInfo(String ym, String prjCode);

    /**
     * 考评页面，查询某项目某月份考评数据
     */
    PageBean ListPrjEvalData(EvalDataVo vo, TeSysUser loginUser);

    /**
     * 修改项目考评数据
     */
    void updateEvalData(EvalDataVo vo, TeSysUser loginUser);

    /**
     * 修改当前项目待考评数据的考评人
     */
    void updateEvalUserToBeEvaluated(String ym, String prjCode, String jobCode, TeSysUser loginUser);

    /**
     * 是否可以提交
     */
    Boolean canCommitted(String ym, String prjCode, TeSysUser loginUser);

    /**
     * 提交考评操作记录历史信息
     */
    void commitEval(String ym, String prjCode, TeSysUser loginUser);

    /**
     * 校验分布比例是否合理
     */
    String checkRatioIsReasonable(String ym, String prjCode);

    /**
     * 修改已提交页面项目考评数据 -调整
     */
    void updateCommittedEvalData(EvalDataVo vo, TeSysUser loginUser);

    /**
     * 查询当前登录人员是考评人（项目经理或项目助理）的 PM已提交 的项目清单，支持按考评月筛选 - 已提交页面 、已关闭页面
     */
    PageBean listCommittedOrCloseEvalData(EvalDataVo vo, TeSysUser loginUser);

    /**
     * 调整页面- 查询项目已提交数据
     */
    PageBean listPrjCommittedOrCloseData(EvalDataVo vo, TeSysUser loginUser);

    /**
     * 发给员工上级经理的邮件提醒
     */
    void sendManageMail(String bgId);

    /**
     * 查询BU绩效接口人
     */
    List<BuPfmAdmin> listPfmBuAdmin(TeSysUser loginUser);

    /**
     * 添加BU绩效接口人
     */
    void addBuPfmResp(ObjectId buCid, String buId, String buName, String phone, List<ObjectId> userIdList, TeSysUser loginUser);

    /**
     * 删除BU绩效接口人
     */
    void deleteBuPfmResp(List<ObjectId> userIdList, ObjectId buId);

    /**
     * 每月绩效启动后，在绩效关闭（含关闭当天）前，每天都需要发邮件提醒给项目经理还未提交考评的项目清单、发邮件提醒BU绩效接口人其管理的BU内还未提交考评的项目信息。
     */
    void sendPfmPmMail(String bgId);

    void sendPfmBuAdminMail(String bgId);

    void sendPfmEmpMail(String bgId);

    /**
     * 复制上月绩效评分记录
     */
    void copyProvMonPfmValue(String ym, String jobCode, String prjCode, TeSysUser loginUser);

    PageBean<EvalDataVo> adminQueryEvalData(EvalDataVo vo, TeSysUser loginUser);

    void exportAdminQueryEvalData(EvalDataVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException;

    /**
     * 绩效关闭
     */
    void closePfm(String bgId, String ym);


    void rerunEmpYmPfm(String bgId);

    Boolean canRerunYmPfm(String bgId);

    /**
     * 确人登陆人权限：BG管理员、BU绩效接口人、BU级部门负责人
     */
    Map<String, Object> isPfmAdmin(TeSysUser loginUser);

    void updateEvalUser(String ym, String prjCode, String jobCode, TeSysUser loginUser);

    List<ItfAiPrj> listPfmPrjByBuIds(String ym, List<String> buIds);

    /**
     * 获取当前考评月
     */
    String getEvalYm(String bgId);

    Map<String, List<PfmMailVo>> getResignMailData(String bgId);

    List<TeUser> listAdminViewPm(EvalDataVo vo);

    Boolean hasYearPfm();

    Boolean hasLockedYearPfm();

    void computerYearIntegratedPfm(TeSysUser loginUser);

    void lockYearIntegratedPfm();

    PageBean<MonthPrjPfmVo> monthPrjPfm(PfmQueryVo vo, TeSysUser loginUser);

    PageBean<MonthIntegratedPfmVo> monthIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser);

    PageBean<YearIntegratedPfmVo> yearIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser);

    void exportMonthPrjPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException;

    void exportMonthIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException;

    void exportYearIntegratedPfm(PfmQueryVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException;

    List<ItfDept> listPfmPermissionBu(TeSysUser loginUser);

    CommonResult<Void> initPfmByPrjCode(String bgId, String prjCode);
}
