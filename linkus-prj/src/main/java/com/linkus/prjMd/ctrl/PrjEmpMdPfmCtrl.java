package com.linkus.prjMd.ctrl;

import com.linkus.base.response.CommonController;
import com.linkus.base.util.DateUtil;
import com.linkus.common.exception.BaseException;
import com.linkus.common.model.TeUser;
import com.linkus.common.util.PageBean;
import com.linkus.common.util.StringUtil;
import com.linkus.common.web.BusinessException;
import com.linkus.common.web.CommonResult;
import com.linkus.itf.api.model.ItfAiPrj;
import com.linkus.itf.api.model.ItfDept;
import com.linkus.prj.api.model.BuPfmAdmin;
import com.linkus.prjMd.model.TePrjEmpMdPfm;
import com.linkus.prjMd.service.IPrjEmpMdPfmService2;
import com.linkus.prjMd.vo.*;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 新版绩效
 */
@RestController
@RequestMapping("/prjEmpMdPfmCtrl")
public class PrjEmpMdPfmCtrl extends CommonController {


    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IPrjEmpMdPfmService2 prjEmpPfmService;


    /**
     * 获取绩效打分关闭时间
     */
    @PostMapping("/getPfmCloseTime")
    public CommonResult<String> getPfmCloseTime(@RequestParam("bgId") String bgId) {
        String pfmCloseTime = prjEmpPfmService.getPfmCloseTime(bgId);
        return CommonResult.success(pfmCloseTime);
    }

    /**
     * 获取当前考评月
     */
    @PostMapping("/getEvalYm")
    public CommonResult<String> getEvalYm(@RequestParam("bgId") String bgId) {
        String evalYm = prjEmpPfmService.getEvalYm(bgId);
        return CommonResult.success(DateUtil.formatDate2Str(DateUtil.parseDate(evalYm, DateUtil.DATE_MONTH_FOTMAT), DateUtil.DATE_MONTH_FOTMAT2));
    }

    /**
     * 更新绩效打分时间
     */
    @PostMapping("/updatePfmCloseTime")
    public CommonResult<String> updatePfmCloseTime(@RequestParam("bgId") String bgId, @RequestParam("forwardDays") String forwardDays) {
        return CommonResult.success(prjEmpPfmService.updatePfmCloseTime(bgId, forwardDays));
    }

    /**
     * 获取该bg该年月下绩效数据个数
     * ym:yyyy-MM
     */
    @PostMapping("/getCountByPrjBgAndYm")
    public CommonResult<Integer> getCountByPrjBgAndYm(@RequestParam("bgId") String bgId, @RequestParam("ym") String ym) {
        PrjEmpMdPfmQuery pfmQuery = PrjEmpMdPfmQuery.builder().isValid(true).prjBgId(bgId)
                .ym(ym.replace("-", "")).build();
        List<TePrjEmpMdPfm> teRmsEmpPfms = prjEmpPfmService.listMdPfms(pfmQuery);
        return CommonResult.success(teRmsEmpPfms.size());
    }


    @PostMapping("/init")
    public CommonResult<Void> init(@RequestParam("bgId") String bgId, @RequestParam("ym") String ym) {
        prjEmpPfmService.initPfmData(bgId, ym.replace("-", ""));
        return CommonResult.success();
    }


    /**
     * 判断登陆人是否是考评人即项目经理、项目助理
     */
    @PostMapping("/isEvaluator")
    public CommonResult<Boolean> isEvaluator(@RequestParam String prjCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.isEvaluator(prjCode, loginUser.getId()));
    }

    /**
     * 查询月份下，当前登录人员是考评人的 ”待考评“ 的项目清单，按一个项目一条记录显示。
     */
    @PostMapping("/listMyToEvalPrj")
    public CommonResult<PageBean<EvalDataVo>> listMyToEvalPrj(@RequestParam String ym, @RequestParam Integer pageIndex,
                                                              @RequestParam Integer pageSize) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        PageBean<EvalDataVo> data = prjEmpPfmService.listMyToEvalPrj(ym, pageIndex, pageSize, loginUser);
        return CommonResult.success(data);
    }

    /**
     * 获取考评项目
     */
    @PostMapping("/listEvalPrj")
    public CommonResult<List<EvalDataVo>> listEvalPrj(@RequestParam String ym) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        List<EvalDataVo> data = prjEmpPfmService.listEvalPrj(ym, loginUser);
        return CommonResult.success(data);
    }

    /**
     * 统计显示当前考评项目，考评月份下，正式、外包员工的人员总数、工时人天数、以及实时显示不同分值区间的人员百分比。
     */
    @PostMapping("/queryEvalInfo")
    public CommonResult<EvalDataVo> queryEvalInfo(@RequestParam String ym, @RequestParam String prjCode) {
        EvalDataVo result = prjEmpPfmService.queryEvalInfo(ym, prjCode);
        return CommonResult.success(result);
    }

    /**
     * 考评页面，查询某项目某月份考评数据 - 待考评
     */
    @PostMapping("/ListPrjEvalData")
    public CommonResult<Object> ListPrjEvalData(@RequestBody EvalDataVo vo) {
        if (StringUtil.isNull(vo.getYm())) {
            throw BusinessException.initExc("当前月份为空");
        }
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        PageBean pageBean = prjEmpPfmService.ListPrjEvalData(vo, loginUser);
        return CommonResult.success(pageBean);
    }

    /**
     * 考评页面 - 修改项目考评数据   --打分
     */
    @PostMapping("/updateEvalData")
    public CommonResult<Void> updateEvalData(@RequestBody EvalDataVo vo) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.updateEvalData(vo, loginUser);
        return CommonResult.success();
    }

    /**
     * 考评页面 - 修改当前项目待考评数据中的考评人
     */
    @PostMapping("/updateEvalUserToBeEvaluated")
    public CommonResult<Void> updateEvalUserToBeEvaluated(@RequestParam(value = "ym") String ym,
                                                          @RequestParam(value = "prjCode") String prjCode,
                                                          @RequestParam(value = "jobCode") String jobCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.updateEvalUserToBeEvaluated(ym, prjCode, jobCode, loginUser);
        return CommonResult.success();
    }

    /**
     * 考评页面 - 提交考评
     */
    @PostMapping("/commitEval")
    public CommonResult<Void> commitEval(@RequestParam String ym, @RequestParam String prjCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.commitEval(ym, prjCode, loginUser);
        return CommonResult.success();
    }

    /**
     * 考评页面 - 该项目项目经理或组长是否可以提交考评
     */
    @PostMapping("/canCommitted")
    public CommonResult<Boolean> canCommitted(@RequestParam String ym, @RequestParam String prjCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.canCommitted(ym, prjCode, loginUser));
    }

    /**
     * 查询项目维度考评数据 - 已提交、已关闭
     */
    @PostMapping("/listCommittedOrCloseEvalData")
    public CommonResult<PageBean> listCommittedEvalData(@RequestBody EvalDataVo vo) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.listCommittedOrCloseEvalData(vo, loginUser));
    }

    /**
     * 修改已提交页面项目考评数据 --调整
     */
    @PostMapping("/updateCommittedEvalData")
    public CommonResult<Void> updateCommittedEvalData(@RequestBody EvalDataVo vo) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.updateCommittedEvalData(vo, loginUser);
        return CommonResult.success();
    }

//    /**
//     * 调整页面、已关闭某项目的查看页面-查询
//     */
//    @PostMapping("/listPrjCommittedOrCloseData")
//    public CommonResult<PageBean> listPrjCommittedOrCloseData(@RequestBody EvalDataVo vo) {
//        TeSysUser loginUser = geTeSysUser();
//        PageBean pageBean = prjEmpPfmService.listPrjCommittedOrCloseData(vo, loginUser);
//        return CommonResult.success(pageBean);
//    }


    /**
     * BU绩效接口人 -查询
     */
    @PostMapping("/listBuPfmResp")
    public CommonResult<List<BuPfmAdmin>> listBuPfmResp() {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.listPfmBuAdmin(loginUser));
    }

    /**
     * BU绩效接口人 -添加
     */
    @PostMapping("/addBuPfmResp")
    public CommonResult<Void> addBuPfmResp(@RequestParam(value = "buCid") ObjectId buCid,
                                           @RequestParam(value = "buId") String buId,
                                           @RequestParam(value = "buName") String buName,
                                           @RequestParam(value = "phone", required = false) String phone,
                                           @RequestParam(value = "userIdList") List<ObjectId> userIdList) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.addBuPfmResp(buCid, buId, buName, phone, userIdList, loginUser);
        return CommonResult.success();
    }

    /**
     * BU绩效接口人 -删除
     */
    @PostMapping("/deleteBuPfmResp")
    public CommonResult<Void> deleteBuPfmResp(@RequestParam(value = "userIdList") List<ObjectId> userIdList,
                                              @RequestParam(value = "buCid") ObjectId buCid) {
        prjEmpPfmService.deleteBuPfmResp(userIdList, buCid);
        return CommonResult.success();
    }

    /**
     * 复制上月绩效评分记录
     */
    @PostMapping("/copyProvMonPfmValue")
    public CommonResult<Void> copyProvMonPfmValue(@RequestParam(value = "ym") String ym,
                                                  @RequestParam(value = "prjCode") String prjCode,
                                                  @RequestParam(value = "jobCode", required = false) String jobCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.copyProvMonPfmValue(ym, jobCode, prjCode, loginUser);
        return CommonResult.success();
    }

    /**
     * 关闭  //todo 测试，上线去掉
     */
    @PostMapping("/closePfm")
    public CommonResult<Void> closePfm(@RequestParam(value = "bgId") String bgId, @RequestParam(value = "ym") String ym) {
        prjEmpPfmService.closePfm(bgId, ym);
        return CommonResult.success();
    }

    /**
     * 测试  //todo 测试，上线去掉
     */
    @PostMapping("/test")
    public CommonResult<Void> test(@RequestParam(value = "bgId") String bgId,
                                   @RequestParam(value = "ym", required = false) String ym) {
//        prjEmpPfmService.sendPfmEmpMail(bgId);

        prjEmpPfmService.sendPfmBuAdminMail(bgId);
//        prjEmpPfmService.getResignMailData(bgId);

        return CommonResult.success();
    }

    /**
     * 月度综合绩效是否可重算
     */
    @PostMapping("/canRerunYmPfm")
    public CommonResult<Boolean> canRerunYmPfm(@RequestParam(value = "bgId") String bgId) {
        return CommonResult.success(prjEmpPfmService.canRerunYmPfm(bgId));
    }

    /**
     * 月度综合绩效-手动重算
     */
    @PostMapping("/rerunYmPfm")
    public CommonResult<Void> rerunYmPfm(@RequestParam(value = "bgId") String bgId) {
        prjEmpPfmService.rerunEmpYmPfm(bgId);
        return CommonResult.success();
    }

    @PostMapping("/isPfmAdmin")
    public CommonResult<Map<String, Object>> isPfmAdmin() {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw BusinessException.initExc("当前登录人为空");
        }
        return CommonResult.success(prjEmpPfmService.isPfmAdmin(loginUser));
    }

    /**
     * 管理员视图-查询
     */
    @PostMapping("/adminQueryEvalData")
    public CommonResult<PageBean<EvalDataVo>> adminQueryEvalData(@RequestBody EvalDataVo vo) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw BusinessException.initExc("当前登录人为空");
        }
        return CommonResult.success(prjEmpPfmService.adminQueryEvalData(vo, loginUser));
    }

    /**
     * 管理员视图-导出
     */
    @PostMapping("/exportAdminQueryEvalData")
    public CommonResult<Void> exportAdminQueryEvalData(@RequestBody EvalDataVo vo) throws IOException {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        HttpServletResponse response = getResponse();
        prjEmpPfmService.exportAdminQueryEvalData(vo, loginUser, response);
        return CommonResult.success();
    }

    /**
     * 管理员视图-修改考评人
     */
    @PostMapping("/updateEvalUser")
    public CommonResult<Void> updateEvalUser(@RequestParam(value = "ym") String ym,
                                             @RequestParam(value = "prjCode") String prjCode,
                                             @RequestParam(value = "jobCode") String jobCode) {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.updateEvalUser(ym, prjCode, jobCode, loginUser);
        return CommonResult.success();
    }


    /**
     * 管理员视图-获取项目经理
     */
    @PostMapping("/listAdminViewPm")
    public CommonResult<List<TeUser>> queryAdminViewPm(@RequestBody EvalDataVo vo) {
        return CommonResult.success(prjEmpPfmService.listAdminViewPm(vo));
    }

    @PostMapping("/listPfmPrjByBuIds")
    public CommonResult<List<ItfAiPrj>> listPfmPrjByBuIds(@RequestBody EvalDataVo vo) {
        return CommonResult.success(prjEmpPfmService.listPfmPrjByBuIds(vo.getYm(), vo.getBuIds()));
    }

    /**
     * 是否有当年年度综合绩效
     */
    @PostMapping("/hasYearPfm")
    public CommonResult<Boolean> hasYearPfm() {
        return CommonResult.success(prjEmpPfmService.hasYearPfm());
    }

    /**
     * 是否有已锁定的当年年度综合绩效
     */
    @PostMapping("/hasLockedYearPfm")
    public CommonResult<Boolean> hasLockedYearPfm() {
        return CommonResult.success(prjEmpPfmService.hasLockedYearPfm());
    }

    /**
     * 年度综合绩效-计算
     */
    @PostMapping("/computerYearIntegratedPfm")
    public CommonResult<Void> computerYearIntegratedPfm() {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        prjEmpPfmService.computerYearIntegratedPfm(loginUser);
        return CommonResult.success();
    }

    /**
     * 年度综合绩效-锁定
     */
    @PostMapping("/lockYearIntegratedPfm")
    public CommonResult<Void> lockYearIntegratedPfm() {
        prjEmpPfmService.lockYearIntegratedPfm();
        return CommonResult.success();
    }

    /**
     * 获取有权限的BU
     */
    @PostMapping("/listPfmPermissionBu")
    public CommonResult<List<ItfDept>> listPfmPermissionBu() {
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.listPfmPermissionBu(loginUser));
    }


//    /**
//     * 获取有权限的CC
//     */
//    @PostMapping("/listPfmPermissionCc")
//    public CommonResult<Void> listPfmPermissionCc() {
//        TeSysUser loginUser = geTeSysUser();
//        if (null == loginUser) {
//            throw new BaseException("当前登录人为空!");
//        }
//        return CommonResult.success();
//    }

    /**
     * 月度项目绩效
     */
    @PostMapping("/monthPrjPfm")
    public CommonResult<PageBean<MonthPrjPfmVo>> monthPrjPfm(@RequestBody PfmQueryVo vo) {
        String ym = vo.getYm();
        Assert.hasLength(ym, "年月不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.monthPrjPfm(vo, loginUser));
    }

    /**
     * 月度综合绩效
     */
    @PostMapping("/monthIntegratedPfm")
    public CommonResult<PageBean<MonthIntegratedPfmVo>> monthIntegratedPfm(@RequestBody PfmQueryVo vo) {
        String ym = vo.getYm();
        Assert.hasLength(ym, "年月不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.monthIntegratedPfm(vo, loginUser));
    }

    /**
     * 年度综合绩效
     */
    @PostMapping("/yearIntegratedPfm")
    public CommonResult<PageBean<YearIntegratedPfmVo>> yearIntegratedPfm(@RequestBody PfmQueryVo vo) {
        Integer year = vo.getYear();
        Assert.notNull(year, "年份不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        return CommonResult.success(prjEmpPfmService.yearIntegratedPfm(vo, loginUser));
    }


    /**
     * 月度项目绩效-导出
     */
    @PostMapping("/exportMonthPrjPfm")
    public CommonResult<Void> exportMonthPrjPfm(@RequestBody PfmQueryVo vo) throws IOException {
        String ym = vo.getYm();
        Assert.hasLength(ym, "年月不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        HttpServletResponse response = getResponse();
        prjEmpPfmService.exportMonthPrjPfm(vo, loginUser, response);
        return CommonResult.success();
    }

    /**
     * 月度综合绩效-导出
     */
    @PostMapping("/exportMonthIntegratedPfm")
    public CommonResult<Void> exportMonthIntegratedPfm(@RequestBody PfmQueryVo vo) throws IOException {
        String ym = vo.getYm();
        Assert.hasLength(ym, "年月不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        HttpServletResponse response = getResponse();
        prjEmpPfmService.exportMonthIntegratedPfm(vo, loginUser, response);
        return CommonResult.success();
    }

    /**
     * 年度综合绩效-导出
     */
    @PostMapping("/exportYearIntegratedPfm")
    public CommonResult<Void> exportYearIntegratedPfm(@RequestBody PfmQueryVo vo) throws IOException {
        Integer ym = vo.getYear();
        Assert.notNull(ym, "年月不可为空!");
        TeSysUser loginUser = geTeSysUser();
        if (null == loginUser) {
            throw new BaseException("当前登录人为空!");
        }
        HttpServletResponse response = getResponse();
        prjEmpPfmService.exportYearIntegratedPfm(vo, loginUser, response);
        return CommonResult.success();
    }

    /**
     * 单个项目的绩效初始化
     */
    @PostMapping("/initPfmByPrjCode")
    public CommonResult<Void> initPfmByPrjCode(@RequestParam String bgId, @RequestParam String prjCode) {
        return prjEmpPfmService.initPfmByPrjCode(bgId, prjCode);
    }


    /**
     * 获取当前用户登录信息
     */
    private TeSysUser geTeSysUser() {
        return sysUserService.queryByLoginName(this.getLoginName());
    }
}
