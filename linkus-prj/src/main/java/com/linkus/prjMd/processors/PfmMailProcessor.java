package com.linkus.prjMd.processors;


import com.linkus.itf.api.client.ItfDeptFeignClient;
import com.linkus.itf.api.model.ItfDept;
import com.linkus.prjMd.constant.PrjMdConstant;
import com.linkus.prjMd.service.IPrjEmpMdPfmService2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 绩效系统邮件
 */

@Slf4j
@Component
public class PfmMailProcessor implements BasicProcessor {

    @Autowired
    private ItfDeptFeignClient deptFeignClient;

    @Autowired
    private IPrjEmpMdPfmService2 pfmService;


    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger omsLogger = context.getOmsLogger();

        //查找所有bg
        List<ItfDept> bgList = deptFeignClient.listByOrgType(PrjMdConstant.BG).getData(true);
        if (CollectionUtils.isEmpty(bgList)) {
            return new ProcessResult(false, "查询BG为空！");
        }
        List<String> bgIds = bgList.stream().map(ItfDept::getOrgCode).collect(Collectors.toList());

        try {
            for (String bgId : bgIds) {
                /**
                 * 发给 员工上级经理 的邮件
                 */
                pfmService.sendManageMail(bgId);

                /**
                 * 发给 项目经理 的邮件
                 */
                pfmService.sendPfmPmMail(bgId);

                /**
                 * 发给 BU绩效接口人 的邮件
                 */
                pfmService.sendPfmBuAdminMail(bgId);

                /**
                 * 发给员工本人
                 */
                pfmService.sendPfmEmpMail(bgId);


            }
        } catch (Exception ex) {
            omsLogger.error("exception", ex);
            return new ProcessResult(false, "exception:" + ex.getMessage());
        }
        return new ProcessResult(true);
    }


}
