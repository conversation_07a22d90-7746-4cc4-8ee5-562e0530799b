package com.linkus.prjMd.processors;


import com.linkus.base.util.DateUtil;
import com.linkus.common.util.StringUtil;
import com.linkus.itf.api.client.ItfDeptFeignClient;
import com.linkus.prjMd.model.TePrjEmpMdPfm;
import com.linkus.prjMd.service.IPrjEmpMdPfmService;
import com.linkus.prjMd.vo.PrjEmpMdPfmQuery;
import com.linkus.sys.api.client.SysCalFeignClient;
import com.linkus.sys.api.model.ItfSysCal;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.service.ISysDefService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.Date;
import java.util.List;

@Component
public class PfmInitProcessor implements BasicProcessor {

    @Autowired
    private ItfDeptFeignClient deptFeignClient;

    @Autowired
    private SysCalFeignClient sysCalFeignClient;

    @Autowired
    private IPrjEmpMdPfmService pfmService;

    @Autowired
    private ISysDefService sysDefService;


    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger log = context.getOmsLogger();
        try {
            Date now = new Date();
            String ym = DateUtil.formatDate2Str(now, DateUtil.DATE_MONTH_FOTMAT);
            String today = DateUtil.formatDate2Str(now, DateUtil.DATE_FORMAT);
            List<ItfSysCal> workDays = sysCalFeignClient.getWorkDaysByYm(ym).getData();
            if (CollectionUtils.isEmpty(workDays)) {
                String errorMsg = "未获取本月工作日日历信息";
                log.warn(errorMsg);
                return new ProcessResult(false, errorMsg);
            }

            // 绩效考评开始日期
            List<SysDef> defs = sysDefService.getSysDefsByDefType(SysDefTypeCodeName.RMS_PFM_START_TIME);
            if (CollectionUtils.isEmpty(defs)) {
                String errorMsg = "未获取绩效考评BG开始日期信息";
                log.warn(errorMsg);
                return new ProcessResult(false, errorMsg);
            }

            for (SysDef def : defs) {
                // 获取BG信息
                SysDef srcDef = def.getSrcDef();
                String bgId = srcDef.getCodeName();
                log.info("开始处理BG:{}的绩效考评信息", bgId);
                Integer th = StringUtil.toInteger(def.getValue());
                if (th == null || th < 1 || th > workDays.size()) {
                    log.error("BG:{}配置绩效考评开始日期错误", bgId);
                }
                ItfSysCal startDateCal = workDays.get(th - 1);
                String startDate = startDateCal.getDate();
                log.info("BG：{}绩效考评开始日期：{}", bgId, startDate);

                // 绩效考评开始日期和当前日期比较，如果大于当前日期，则不处理，否则判断是否已经初始化，没有初始化则需要进行初始化
                if (startDate.compareTo(today) > 0) {
                    log.info("BG：{}绩效考评开始日期大于当前日期，无须处理", bgId);
                    continue;
                }

                // 是否已经初始化
                if (isInited(bgId, ym)) {
                    log.info("BG：{}绩效考评已经初始化完成，无须处理");
                    continue;
                }

                // 初始化绩效考评
                pfmService.initEmpMdPfmData(bgId, ym);
            }
            return new ProcessResult(true, "success");
        } catch (Exception e) {
            return new ProcessResult(false, e.getMessage());
        }
    }

    private boolean isInited(String bgId, String ym) {
        PrjEmpMdPfmQuery pfmQuery = PrjEmpMdPfmQuery.builder().isValid(true).prjBgId(bgId).ym(ym).build();
        List<TePrjEmpMdPfm> rmsEmpMdPfms = pfmService.listEmpMdPfms(pfmQuery);
        return CollectionUtils.isNotEmpty(rmsEmpMdPfms);
    }


}
