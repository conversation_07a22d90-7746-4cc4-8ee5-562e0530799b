package com.linkus.prj.ctrl;

import com.linkus.base.aop.DataMasking;
import com.linkus.base.aop.LogAnnotation;
import com.linkus.base.aop.OperationTypeEnum;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.StringUtil;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.prj.model.TePrjEmpPfm;
import com.linkus.prj.model.TePrjInfo;
import com.linkus.prj.model.vo.PrjBonusAnalyseVo;
import com.linkus.prj.model.vo.PrjBonusDurationVo;
import com.linkus.prj.model.vo.PrjBonusRoleReportVo;
import com.linkus.prj.model.vo.PrjEmpPfmApplyVo;
import com.linkus.prj.model.vo.PrjEmpPfmVo;
import com.linkus.prj.service.IPrjBonusService;
import com.linkus.prj.service.IPrjInfoService;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/prjBonusCtrl")
public class PrjBonusCtrl extends CommonController {
    @Autowired
    private IPrjBonusService prjBonusService;
    @Autowired
    private IPrjInfoService prjInfoService;
    @Autowired
    private ISysUserService sysUserService;

    @PostMapping("/initPrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "启动绩效评分", operation = OperationTypeEnum.CREATE)
    public CommonResult initPrjBonus(@RequestParam ObjectId prjId, @RequestParam String startYm, @RequestParam String endYm, @RequestParam String site) {
        TeSysUser loginUser = getLoginUser();
        prjBonusService.initPrjBonus(prjId,startYm, endYm, site,loginUser);
        return CommonResult.success();
    }

    @PostMapping("/listPrjEmpPfmApplyByPrjId.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "查询项目人员考评", operation = OperationTypeEnum.QUERY)
    public CommonResult listPrjEmpPfmApplyByPrjId(@RequestParam ObjectId prjId) {
        List<PrjEmpPfmApplyVo> pfmApplies = prjBonusService.listPrjEmpPfmApplyByPrjId(prjId);
        return CommonResult.success(pfmApplies);
    }

    @PostMapping("/queryPrjBonus.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "查询项目人员绩效", operation = OperationTypeEnum.QUERY)
    public CommonResult queryPrjBonus(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId,
                                      @RequestParam(required = false) boolean all,
                                      @RequestParam(required = false) String emp,
                                      @RequestParam(required = false) String dept,
                                      @RequestParam(required = false) String title,
                                      @RequestParam(required = false) String group,
                                      @RequestParam(required = false) Boolean employeeType,
                                      @RequestParam(required = false) Boolean isPfmEmp) {
        TeSysUser loginUser = getLoginUser();
        List<PrjEmpPfmVo> tePrjEmpPfms = prjBonusService.queryPrjBonus(prjId, pfmApplyId,all,loginUser,emp,dept,title,group,employeeType,isPfmEmp);
        return CommonResult.success(tePrjEmpPfms);
    }

    @PostMapping("/updatePrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "编辑项目人员绩效", operation = OperationTypeEnum.UPDATE)
    public CommonResult updatePrjBonus(@RequestBody TePrjEmpPfm empPfm) {
        prjBonusService.updatePrjBonus(empPfm);
        return CommonResult.success();
    }

    @PostMapping("/bacthUpdatePrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "批量编辑项目人员绩效", operation = OperationTypeEnum.UPDATE)
    public CommonResult bacthUpdatePrjBonus(@RequestBody List<TePrjEmpPfm> empPfms) {
        prjBonusService.bacthUpdatePrjBonus(empPfms);
        return CommonResult.success();
    }

    @PostMapping("/updatePrjEmpPfmApply.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "编辑计算基本信息", operation = OperationTypeEnum.UPDATE)
    public CommonResult updatePrjEmpPfmApply(@RequestParam ObjectId pfmApplyId,
                                             @RequestParam(required = false) Double prePrjMm,
                                             @RequestParam(required = false) Double bonusExtra) {
        prjBonusService.updatePrjEmpPfmApply(pfmApplyId,prePrjMm,bonusExtra);
        return CommonResult.success();
    }

    @GetMapping("/setPrjEmpPfmApplyScoreAndTrip.action")
    public CommonResult setPrjEmpPfmApplyScoreAndTrip(@RequestParam ObjectId prjId,
                                                      @RequestParam ObjectId pfmApplyId,
                                             @RequestParam(required = false) ObjectId userId) {
        prjBonusService.setPrjEmpPfmApplyScoreAndTrip(prjId,pfmApplyId,userId);
        return CommonResult.success();
    }

    @PostMapping("/insertPrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "新增项目人员绩效", operation = OperationTypeEnum.CREATE)
    public CommonResult insertPrjBonus(@RequestBody List<TePrjEmpPfm> empPfms) {
        TeSysUser loginUser = getLoginUser();
        List<TePrjEmpPfm> prjEmpPfm = prjBonusService.insertPrjBonus(empPfms, loginUser);
        return CommonResult.success(prjEmpPfm);
    }

    @PostMapping("/deletePrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "删除项目人员绩效", operation = OperationTypeEnum.DELETE)
    public CommonResult deletePrjBonus(@RequestParam List<ObjectId> prjEmpPfmIds) {
        prjBonusService.deletePrjBonus(prjEmpPfmIds);
        return CommonResult.success();
    }

    @PostMapping("/submitPrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "审批通过", operation = OperationTypeEnum.UPDATE)
    public CommonResult submitPrjBonus(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId
            ,@RequestParam(required = false)String desc,@RequestParam(required = false)int passBranch) {
        TeSysUser loginUser = getLoginUser();
        prjBonusService.submitPrjBonus(prjId,pfmApplyId,desc,passBranch,loginUser);
        return CommonResult.success();
    }

    @PostMapping("/rejectPrjBonus.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "审批不通过", operation = OperationTypeEnum.UPDATE)
    public CommonResult rejectPrjBonus(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId
            ,@RequestParam(required = false)String desc,@RequestParam(required = false)int passBranch
            ) {
        TeSysUser loginUser = getLoginUser();
        prjBonusService.rejectPrjBonus(prjId,pfmApplyId,desc,passBranch,loginUser);
        return CommonResult.success();
    }

    @PostMapping("/prjBonusDuration.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "结果分析奖金发放", operation = OperationTypeEnum.QUERY)
    public CommonResult prjBonusDuration(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId,boolean actual) {
        List<PrjBonusDurationVo> list = prjBonusService.prjBonusDuration(prjId, pfmApplyId, actual);
        return CommonResult.success(list);
    }

    @PostMapping("/prjBonusAnalyse.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "结果分析奖金分析", operation = OperationTypeEnum.QUERY)
    public CommonResult prjBonusAnalyse(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId) {
        List<PrjBonusAnalyseVo> list = prjBonusService.prjBonusAnalyse(prjId, pfmApplyId);
        return CommonResult.success(list);
    }

    @PostMapping("/prjBonusRoleReport.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "奖金分布情况报表", operation = OperationTypeEnum.QUERY)
    public CommonResult prjBonusRoleReport(@RequestParam ObjectId prjId,@RequestParam ObjectId pfmApplyId,boolean group) {
        List<PrjBonusRoleReportVo> list = prjBonusService.prjBonusRoleReport(prjId, pfmApplyId,group);
        return CommonResult.success(list);
    }

    @GetMapping("/exportPrjBonus.action")
    @DataMasking
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "导出项目人员绩效", operation = OperationTypeEnum.EXPORT)
    public CommonResult exportPrjBonus(@RequestParam ObjectId prjId, @RequestParam ObjectId pfmApplyId,
                                       @RequestParam Integer index, HttpServletResponse response,
                                      @RequestParam(required = false) boolean all,
                                      @RequestParam(required = false) String emp,
                                      @RequestParam(required = false) String dept,
                                      @RequestParam(required = false) String title,
                                      @RequestParam(required = false) String group,
                                      @RequestParam(required = false) Boolean employeeType,
                                      @RequestParam(required = false) Boolean isPfmEmp) {
        TeSysUser loginUser = getLoginUser();
        TePrjInfo info = prjInfoService.getTePrjInfoById(prjId.toHexString());
        List<Map<String, Object>> maps = prjBonusService.exportPrjBonus(prjId, pfmApplyId, all, loginUser, emp, dept, title, group,employeeType,isPfmEmp);
        String[] columnNames = new String[0];
        String[] keys = new String[0];
        if (index == 1){
            columnNames = new String[]{"序", "是否参与激励","人员","人员类型","归属部门", "岗位"
                    ,"归属组", "打分人","工时时长(天)", "增补工时(天)", "增补说明"};
            keys = new String[]{"no","isPfmEmp", "emp","employeeType", "dept","title", "group", "checkPsn","mdStd", "mdAdjust"
                    , "mdAdjustDesc"};
        }
        if (index == 2){
            columnNames = new String[]{"序", "是否参与激励","人员","人员类型", "归属部门", "岗位","岗位系数","归属组",
                    "工作量","工作质量", "积极性","沟通能力","吃苦耐劳","基础得分",
                    "工时时长(天)", "增补工时(天)", "增补说明",
                    "项目时长系数","累计差旅天数","累计差旅次数","平均差旅时长系数","个人绩效分","项目管理系数"};
            keys = new String[]{"no","isPfmEmp", "emp","employeeType", "dept","title","titleRatio", "group",
                    "workEffortScore","workQualityScore","initiativeScore","comSkillScore","hardWorkScore","baseScore",
                    "mdStd", "mdAdjust", "mdAdjustDesc",
                    "mdRatio","tripDays","tripTimes","tripRatio","empPfmRatio","prjMgtRatio"};
        }
        if (index == 3){
            columnNames = new String[]{"序", "是否参与激励","人员","人员类型", "归属部门", "岗位","岗位系数","归属组",
                    "计划发放金额","工作量","工作质量", "积极性","沟通能力","吃苦耐劳","基础得分",
                    "工时时长(天)", "增补工时(天)", "增补说明",
                    "项目时长系数","累计差旅天数","累计差旅次数","平均差旅时长系数","个人绩效分","项目管理系数"};
            keys = new String[]{"no","isPfmEmp", "emp","employeeType", "dept","title","titleRatio", "group",
                    "bonusDue","workEffortScore","workQualityScore","initiativeScore","comSkillScore","hardWorkScore",
                    "baseScore", "mdStd", "mdAdjust", "mdAdjustDesc",
                    "mdRatio","tripDays","tripTimes","tripRatio","empPfmRatio","prjMgtRatio"};
        }
        if (index == 4){
            columnNames = new String[]{"序", "是否参与激励","人员","人员类型", "归属部门", "岗位","岗位系数","归属组",
                    "计划发放金额(元)","实际发放金额(元)","调整说明","实际计划偏差率","偏差率","打分人",
                    "工作量","工作质量", "积极性","沟通能力","吃苦耐劳","基础得分",
                    "工时时长(天)", "增补工时(天)", "增补说明",
                    "项目时长系数","累计差旅天数","累计差旅次数","平均差旅时长系数","个人绩效分","项目管理系数"};
            keys = new String[]{"no","isPfmEmp", "emp","employeeType", "dept","title","titleRatio", "group",
                    "bonusDue","bonusActual","bonusAdjustDesc","actualPlanDeviationRate","deviation","checkPsn",
                    "workEffortScore","workQualityScore","initiativeScore","comSkillScore","hardWorkScore",
                    "baseScore", "mdStd", "mdAdjust", "mdAdjustDesc",
                    "mdRatio","tripDays","tripTimes","tripRatio","empPfmRatio","prjMgtRatio"};
        }
        ByteArrayOutputStream os = ExcelUtils.getResultMap(maps, columnNames, keys);
        byte[] content = os.toByteArray();
        String fileName = "项目绩效评分"+"_"+info.getPrjCode()+"_"+info.getPrjName();
        ExcelUtils.exportFileUtil(response, content, fileName);
        return CommonResult.success();
    }

    @PostMapping("/update.action")
    @LogAnnotation(subsystem = DMPSubsystem.PMS, module = "更新", operation = OperationTypeEnum.UPDATE)
    public CommonResult update(@RequestParam ObjectId pfmApplyId, @RequestParam String startYm
            , @RequestParam String endYm, String site) {
        TeSysUser loginUser = getLoginUser();
        prjBonusService.update(pfmApplyId,startYm,endYm,site,loginUser);
        return CommonResult.success();
    }

    private TeSysUser getLoginUser(){
        String loginName = getLoginName();
        if (StringUtil.isNull(loginName)){
            throw BusinessException.initExc("当前登陆人为空");
        }
        TeSysUser loginUser = sysUserService.queryByLoginName(loginName);
        if (loginUser == null){
            throw BusinessException.initExc("当前登陆人为空");
        }
        return loginUser;
    }
}
