@defaultColor:#3883e5;
@mainColor:#3883e5;
@mainDeepColor:#4c67a7;
@mainLightColor:#3abcff;
@mainDeepColorRgb8:rgba(76,103,167,0.8);
@mainColorRgba8:rgba(56,131,229,0.8);
@mainColorRgba7:rgba(56,131,229,0.7);
@mainColorRgba6:rgba(56,131,229,0.6);
@mainColorRgba5:rgba(56,131,229,0.5);
@mainColorRgba4:rgba(56,131,229,0.4);
@mainColorRgba3:rgba(56,131,229,0.3);
@mainColorRgba2:rgba(56,131,229,0.2);
@mainColorRgba1:rgba(56,131,229,0.1);


.navigation_warp{
  height: calc(100% - 80px);
  padding: 16px 24px;
  background-color: #F5F8FB;
  border: 1px solid rgb(56 131 229);
  position: fixed;
  left: 60px;
  overflow: auto;
  top: 68px;
  z-index: 999;
  font-size: 14px;
  border-radius: 13px;
  box-shadow: 0 2px 12px 0 rgb(56 131 229 / 20%), 0 2px 12px 0 rgb(56 131 229 / 20%);
  &::-webkit-scrollbar {
    width: 0;
  }
  .tip_arrow{
    display: block;
    width: 0;
    height: 0;
    position: fixed;
    border-color: transparent;
    border-style: solid;
    top: calc(30vh - 42px);
    border-width: 0 8px 8px;
    border-bottom-color: #3883e5;
    left: 56px;
    margin-left: -8px;
    z-index: 999;
    transform:rotate(-90deg);
    &:after{
      content: " ";
      top: 1px;
      border-width: 0 8px 8px;
      margin-left: -8px;
      border-color: transparent;
      border-bottom-color: #F5F8FB;
      display: block;
      width: 0;
      height: 0;
      position: absolute;
      border-style: solid;
    }
  }

  >ul{
    >li{
      display: flex;
      >span{
        font-weight: bold;
        color: #262626;
      }
      .line_group{
        margin: 7px ;
        >li{
          display: flex;
          height: 28px;
          >span{
            width: 24px;
            &:nth-child(2){
              margin-left: -2px;
            }
          }
          >em{
            font-style: normal;
            margin-left: 8px;
            margin-top: 18px;
            height: 16px;
            cursor: pointer;
            &:hover{
              color: @defaultColor;
            }
            &.no_url{
              color: #999999;
              cursor: default;
              &:hover{
                color: #999999;
              }
            }
          }
          &.active{
            >em{
              color: @defaultColor;
            }
          }
        }
        >.first{
          >span{
            &:first-child{
              border-top: 2px solid @defaultColor;
              border-right: 2px solid @defaultColor;
              border-radius:0 12px 0 0;
            }
            &:nth-child(2){
              border-bottom: 2px solid @defaultColor;
              position: relative;
              &:after{
                content: '';
                position: absolute;
                left: -2px;
                bottom: -4px;
                width: 6px;
                height: 6px;
                background: #ffffff;
                border: 1px solid @defaultColor;
                border-radius: 50%;
                z-index: 2;
              }
            }
          }
          &.active{
            >span{
              &:nth-child(2){
                &:after{
                  background: #ffff7a;
                }
              }
            }
          }
          &.two_d{
            height: 14px;
            >span{
              &:nth-child(2){
                border-bottom: none;
                &:after{
                  content: '';
                  position: absolute;
                  left: -2px;
                  bottom: -4px;
                  width: 6px;
                  height: 6px;
                  background: transparent;
                  border: none;
                  border-radius: 50%;
                }
              }
            }
          }
        }
        >.last{
          >span{
            &:nth-child(2){
              border-left: 2px solid @defaultColor;
              border-bottom: 2px solid @defaultColor;
              border-radius:0 0 0 12px;
            }
          }
          &.two_d{
            height: 14px;
            >em{
              margin-top: 6px;
            }
          }
        }
        >.data-line{
          >span{
            &:nth-child(2){
              border-bottom: 2px solid @defaultColor;
              border-left: 2px solid @defaultColor;
              position: relative;
              &:after{
                content: '';
                position: absolute;
                left: -4px;
                bottom: -4px;
                width: 6px;
                height: 6px;
                background: #ffffff;
                border: 1px solid @defaultColor;
                border-radius: 50%;
                z-index: 4;
              }
            }
          }
          &.active{
            >span{
              &:nth-child(2){
                &:after{
                  background: #ffff7a;
                }
              }
            }
          }
        }
        .line_group{
          margin-top: 26px;
        }
      }
    }
  }
}


.full_in_tag_warp{
  .full_in_tag_cen{
    padding: 9px 16px;
    font-size: 14px;
    >p{
      color: #555555;
      margin-bottom: 24px;
    }
    .full_method{
      display: flex;
      margin-bottom: 32px;
      >label{
        width: 60px;
        position: relative;
        color: #262626;
        font-weight: 700;
        &:after{
          content: '';
          position: absolute;
          top: 18px;
          left: 0;
          width: 47px;
          height: 3px;
          opacity: 0.8;
          background: #3883e5;
        }
        &.full_table_label{
          padding-top: 6px;
          &:after{
            top: 24px;
          }
        }
      }
      >div{
        color: #555555;
        width: calc(100% -  60px);
        font-size: 12px;
        >div{
          color: #555555;
          &:first-child{
            font-size: 14px;
          }
          &:not(:last-child){
            margin-bottom: 12px;
          }
          &.bg_ye{
            background: rgba(255,153,0,0.10);
            border-radius: 4px;
            padding: 8px 12px;
            span{
              color: #262626;
            }
          }
          &.full_img{
            display: flex;
            padding-left: 12px;
            img{
              width: 100px;
              height: 100px;
            }
          }
          &.full_table_fliter{
            display: flex;
            align-items: center;
            margin-right: 14px;
            >div{
              margin-left: auto;
            }
            >span{
              color: #999999;
              padding: 0 4px;
            }
          }
        }
        .group_select{
          ul{
            display: flex;
            flex-wrap: wrap;
            li{
              margin-bottom: 10px;
              margin-right: 10px;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #ffffff;
              font-size: 12px;
              text-align: center;
              width: 102px;
              cursor: pointer;
              text-overflow: ellipsis;
              white-space: normal;
              min-height: 42px;
              border-width: 1px;
              border-style: solid;
              border-color: rgb(222, 221, 221);
              border-image: initial;
              border-radius: 3px;
              padding: 5px 10px;
              outline: 0;

              i{
                position: absolute;
                right: -8px;
                top: -8px;
                width: 16px;
                height: 16px;
                font-size: 18px;
                border-radius: 50%;
                background: #fff;
                display: none;
              }
              &.active{
                color: @defaultColor;
                border: 1px solid @defaultColor;
                i{
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }
  }
  .tag_btn{
    text-align: right;
  }
  .ivu-modal-footer{
    border-top: none;
  }
}

.turn_down_info{
  background-color: #FFF9E6;
  border: 1px solid #FFD77A;
  color: #555;
  line-height: 32px;
  padding: 0 16px;
  font-size: 12px;
  border-radius: 4px;
  span{
    color:#f90;
    padding: 0 12px 0 4px;
  }
}


//干系人库
.stakeholder_library_warp{

}

/*项目基准*/
.prj_ben_warp{
  &.header_fix{
    .prjHeader.header_menu{
      position: fixed !important;
      top:0;
    }
    .prj_ben_con{
      padding-top: 76px;
      height: 100%;
      overflow: auto;
    }
  }

  .prj_ben_con{
    min-height: calc(100% - 60px);
    background: #f8f8f9;
    padding: 16px;
    display: flex;
    position: relative;
    .prj_b_title{
      height: 22px;
      line-height: 22px;
      padding-left: 16px;
      border-left: 3px solid @defaultColor;
      position: relative;
      color: #262626;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      .prj_b_title_right{
        margin-left: auto;
        .ivu-btn-text{
          font-size: 12px;
        }
      }
      .scope_view_btn{
        color: #A5ACC4;
        font-size: 12px;
        margin-left: 8px;
        cursor:pointer;
      }
      &:after{
        content: '';
        position: absolute;
        left: -7px;
        top: -4px;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-right-color: #ffffff;
      }
      &:before{
        content: '';
        position: absolute;
        left: -7px;
        bottom: -4px;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-right-color: #ffffff;
      }
    }
    .prj_ben_left{
      width: calc((19/24)*100% - 16px);
      margin-right: 16px;
      height: 100%;
      .prj_b_l_top{
        background: #ffffff;
        border-radius: 4px 4px 0 0;
        box-shadow: 0 2px 12px 0 rgba(144,144,144,0.20);
        margin-bottom: 12px;
        .ben_top_filter{
          padding: 12px 16px;
          display: flex;
          align-items: center;
          *{
            font-size: 12px;
          }
          >div{
            display: flex;
            align-items: center;
            &.t_col_1{
              width: 400px;
              >div{
                display: flex !important;
                align-items: center;
                width: calc(100% - 2em - 12px);
                >div.flow-window{
                  width: calc(100% - 88px) !important;
                  display: flex !important;
                  align-items: center;
                  .ivu-input{
                    border: 1px solid #dcdee2;
                  }
                }
              }
            }
            &.t_col_3{
              cursor: pointer;
              i{
                color: #a5acc4;
                margin-right: 4px;
              }
              color: #555555;
              margin-left: auto;
            }
            >label{
              color: #666666;
              padding-right: 12px;
            }
          }
        }
        .ben_top_tab{
          padding: 0 16px;
          border-top: 1px solid #f1f5f9;
          ul{
            display: flex;
            li{
              padding: 19px 16px;
              cursor: pointer;
              &.active{
                border-bottom: 2px solid @defaultColor;
                color:@defaultColor;
              }
            }
          }
        }
      }
      .prj_b_l_bot{
        min-height: calc(100% - 130px);
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(144,144,144,0.2);
        padding: 12px 0;
        &.flex{
          display: flex;
          align-items: center;
          justify-content: center;
          .l_tip{
            text-align: center;
            font-size: 20px;
            color: #bbbec4;
          }
        }


        .b_base_info{
          .user-fuzzy {
            .ivu-input {
              border: 1px solid #DBE3EB;
              color: #262626;
            }
          }
          .member-list-box.drop-select{
            width: 420px !important;
          }
          >div{
            margin-bottom: 24px;
          }
          .cost_basis{
            .prj_b_title{
              display: flex;
              align-items: center;
              .cost_basis_top{
                margin-left: auto;
                height: auto;
                margin-bottom: 0;
                border-bottom: none;
              }
            }
          }
          .benchmark_info{
            .ivu-table-header{
              th{
                background-color: #f5f8fb;
              }
              thead{
                tr:first-child{
                  th{
                    text-align: center;
                    &:not(:last-child){
                      border-right: 1px solid #dbe3eb !important;
                    }
                  }
                }
                tr:nth-child(2){
                  th:nth-child(3n){
                    border-right: 1px solid #dbe3eb !important;
                  }
                  th:last-child{
                    border-right: none !important;
                  }
                }
              }
            }
            .ivu-table-body{
              .ivu-table-tbody{
                td:nth-child(3n){
                  border-right: 1px solid #dbe3eb !important;
                }
                td:last-child{
                  border-right: none !important;
                }
              }
            }
          }
          .prj_b_b_filter.super_base_line_filter{
            padding-top: 16px;
            .fol_item{
              height: auto;
              align-items: flex-start;
            }
          }
          .prj_b_b_filter{
            display: flex;
            align-items: center;
            padding: 0 16px;
            flex-wrap: wrap;
            &.col_3{
              .fol_item{
                width: calc(100% / 3);
              }
            }
            &.col_2{
              .fol_item{
                width: 50%;
              }
            }
            &.col_1{
              .fol_item{
                width: 100%;
              }
            }
            .fol_item{
              display: flex;
              height: 32px;
              align-items: center;
              margin-bottom: 4px;
              .ivu-cascader .ivu-select-dropdown{
                overflow-x:hidden !important;
              }
              .span_ellipsis{
                display: flex;
                >span{
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  max-width: calc(100% - 10px);
                }
              }
              .height100{
                height: 100%;
                display: flex;
                align-items: center;
              }
              .edit_frame{
                height: 100%;
                align-items: center;
                border-radius: 4px;
                cursor:pointer;
                border: 1px solid #DBE3EB;
                padding: 0 7px;
                &:hover{
                  border: 1px solid @defaultColor;
                }
                >span{
                  width: 100%;
                }
              }

              *{
                font-size: 12px;
              }
              &.pdL20{
                padding-left: 20px;
              }
              label{
                width: 6em;
                color: #666666;

              }
              .txt_break{
                white-space: pre-wrap;
              }
              >div{
                padding-left: 24px;
                color: #262626;
                width: calc(100% - 6em);
                >div{
                  width: 100%;
                }
                >em{
                  color: @defaultColor;
                }
              }
            }
          }
          .new_table_warp{
            padding:0 16px;
            border-top: 1px solid #f1f5f9;
            &.no_border{
              border: none;
            }
            .scope_view_table_warp{
              height: auto;
              overflow: hidden;
              &::-webkit-scrollbar{
                width:0;
              }
              &.scopeClose{
                height: 440px;
              }
            }
          }
          .cost_basic_emp_month_avg {
            padding: 8px 16px 0 16px;
            .cost_basic_emp_month_avg_title {
              margin-top: 4px;
              margin-bottom: 12px;
              display: inline-block;
              padding-left: 8px;
              font-size: 12px;
            }
          }
        }
        .target_datum_info{
          padding:0 16px;
          .prj_b_cond_wrap {
            padding-left: 7px;
            color: #999999;
          }
          th{
            border-bottom: 1px solid #dbe3eb !important;
          }

          .prj_b_b_filter{
            display: flex;
            align-items: center;
            padding: 0 8px;
            flex-wrap: wrap;
            padding-top: 8px;
            &.col_3{
              .fol_item{
                width: calc(100% / 3);
              }
            }
            &.col_2{
              .fol_item{
                width: 50%;
              }
            }
            &.col_1{
              .fol_item{
                width: 100%;
              }
            }
            .fol_item{
              display: flex;
              height: 32px;
              align-items: center;
              margin-bottom: 4px;
              .ivu-cascader .ivu-select-dropdown{
                overflow-x:hidden !important;
              }
              .span_ellipsis{
                display: flex;
                >span{
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  max-width: calc(100% - 10px);
                }
              }
              .height100{
                height: 100%;
                display: flex;
                align-items: center;
              }
              .edit_frame{
                height: 100%;
                align-items: center;
                border-radius: 4px;
                cursor:pointer;
                border: 1px solid #DBE3EB;
                padding: 0 7px;
                &:hover{
                  border: 1px solid @defaultColor;
                }
                >span{
                  width: 100%;
                }
              }

              *{
                font-size: 12px;
              }
              &.pdL20{
                padding-left: 20px;
              }
              label{
                width: 6em;
                color: #666666;

              }
              .txt_break{
                white-space: pre-wrap;
              }
              >div{
                padding-left: 24px;
                color: #262626;
                width: calc(100% - 6em);
                >div{
                  width: 100%;
                }
                >em{
                  color: @defaultColor;
                }
              }
            }
          }

        }
        .range_datum_info,.speed_of_progress_datum_info,.cost_basis{
          .prj_b_btn{
            display: flex;
            justify-content: flex-end;
            padding:0 16px;
            line-height: 38px;
            .prj-button{
              margin-left: 12px;
              cursor: pointer;
              color: rgb(153, 153, 153);
              font-size: 12px;
              display: flex;
              align-items: center;
              i{
                margin-right: 4px;
              }
              &:hover{
                color: @defaultColor;
              }
            }
          }
        }
        .cost_basis{
          padding:0 16px;
          .cost_basis_top{
            padding: 0 12px;
            height: 38px;
            border-bottom: 1px solid #dbe3eb;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            width: calc(100% - 160px);
            >div{
              display: flex;
              width: 100%;
              align-items: center;
              >span{
                width: 46px;
                cursor: pointer;
                color: #999999;
                &.active{
                  font-size: 16px;
                  color: #262626;
                  font-weight: 500;
                }
              }
              >i{
                cursor: pointer;
              }
              > i.no_active {
                color: #c5c8ce;
                cursor: no-drop;
              }
              >div{
                display: flex;
                width: calc(100% - 78px);
                align-items: center;
                position: relative;
                overflow: hidden;
                height: 24px;

                >ul{
                  display: flex;
                  align-items: center;
                  position: absolute;
                  left: 0px;
                  li{
                    color: #999999;
                    font-size: 14px;
                    margin-right: 24px;
                    line-height: 22px;
                    cursor: pointer;
                    &.active{
                      font-size: 16px;
                      color: #262626;
                      font-weight: 500;
                    }
                  }
                }
              }
            }
            .prj_b_btn{
              margin-left: auto;
            }
          }

          .prj_b_b_filter{
            display: flex;
            align-items: center;
            padding: 0 8px;
            flex-wrap: wrap;
            padding-top: 8px;
            &.col_3{
              .fol_item{
                width: calc(100% / 3);
              }
            }
            &.col_2{
              .fol_item{
                width: 50%;
              }
            }
            &.col_1{
              .fol_item{
                width: 100%;
              }
            }
            .fol_item{
              display: flex;
              height: 32px;
              align-items: center;
              margin-bottom: 4px;
              .ivu-cascader .ivu-select-dropdown{
                overflow-x:hidden !important;
              }
              .span_ellipsis{
                display: flex;
                >span{
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  max-width: calc(100% - 10px);
                }
              }
              .height100{
                height: 100%;
                display: flex;
                align-items: center;
              }
              .edit_frame{
                height: 100%;
                align-items: center;
                border-radius: 4px;
                cursor:pointer;
                border: 1px solid #DBE3EB;
                padding: 0 7px;
                &:hover{
                  border: 1px solid @defaultColor;
                }
                >span{
                  width: 100%;
                }
              }

              *{
                font-size: 12px;
              }
              &.pdL20{
                padding-left: 20px;
              }
              label{
                width: 10em;
                color: #666666;

              }
              .txt_break{
                white-space: pre-wrap;
              }
              >div{
                padding-left: 24px;
                color: #262626;
                width: calc(100% - 10em);
                >div{
                  width: 100%;
                }
                >em{
                  color: @defaultColor;
                }
              }
            }
          }
        }
      }
      .result_info{
        .result_info_list{
          padding: 0 16px;
          .result_info_item{
            margin-bottom: 24px;
            &.col3{
              display: flex;
              .result_info_item_col{
                width: calc(100% / 3);
                &:not(:last-child){
                  margin-right: 8px;
                }
              }
            }
            .result_info_item_col{
              .result_info_item_col_title{
                height: 26px;
                line-height: 22px;
                padding-left: 16px;
                position: relative;
                color: #262626;
                font-size: 14px;
                font-weight: 500;
                border-bottom: 1px solid #e8eaec;
                &:before{
                  content: '';
                  position: absolute;
                  width: 6px;
                  height: 6px;
                  background: #3883e5;
                  border-radius: 50%;
                  left: 0;
                  top: calc(50% - 3px);
                }
              }
            }
          }
        }
      }
    }
    .prj_ben_right{
      width: calc((5/24)*100%);
      .approve_warp{
        background: #ffffff;
        box-shadow: 0 2px 12px 0 rgba(144,144,144,0.2);
        padding: 12px 16px;
        transition: 0.5s;
        &.no_show{
          height: 0;
          overflow: hidden;
          padding: 0 16px;
        }
        .approve_btn{
          margin-bottom:24px;
          >div{
            width: 100%;
          }
          .one_btn{
            .ivu-btn{
              width: 100%;
              font-size: 12px;
            }
          }
          .two_btn{
            display: flex;
            .ivu-btn{
              width: calc(50% - 6px);
              font-size: 12px;
              &:first-child{
                margin-right: 12px;
              }
            }
          }
        }
        >ul{
          border-left: 1px solid #dbe3eb;
          margin-left: 4px;
          li{
            display: flex;
            height: 20px;
            align-items: center;
            padding-left: 24px;
            position: relative;
            color: #555555;
            &:not(:last-child){
              margin-bottom: 32px;
            }
            i{
              position: absolute;
              left: -4px;

            }
            &.pass{
              color: #65CE7A;
              i{
                width: 8px;
                height: 8px;
                background: #ffffff;
                border: 2px solid #65ce7a;
                border-radius: 50%;
              }
            }
            &.current{
              color:@defaultColor;
              i{
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: @defaultColor;
                box-shadow: 0 0 4px @mainColorRgba8;
                left: -5px;
              }
            }
          }
        }
      }
      .his_btn{
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.9;
        background: #ffffff;
        border: 1px solid #dbe3eb;
        border-radius: 4px;
        color: #555555;
        font-size: 12px;
        cursor: pointer;
        margin: 16px 0;
      }
      .approve_his_desc{
        margin-bottom: 16px;
        padding: 12px 16px;
        background: #ffffff;
        border: 1px dashed #ff9900;
        border-radius: 4px;
        color: #555555;
        font-size: 12px;
        >span{
          color:@defaultColor;

        }
      }
      .approve_his_warp{
        overflow: hidden;
        .approve_his{
          padding: 12px 0;
          background: #ffffff;
          border-radius: 4px;
          box-shadow: 0 2px 12px 0 rgba(144,144,144,0.20);

          &::-webkit-scrollbar{
            width:0;
          }
          .prj_b_title{
            display: flex;
            align-items: center;
            padding-right: 18px;
            i{
              color: #a5acc4;
              margin-left: auto;
              cursor: pointer;
            }
          }
          .approve_his_step{
            padding: 16px;
            overflow-y: auto;
            height: calc(100% - 52px);
            &::-webkit-scrollbar{
              width:0;
            }
          }
        }
      }
    }
    .prj_b_cond_wrap {
      display: flex;
      padding-left: 24px;
      font-size: 12px;
      .prj_b_cond {
        width: 33.33%;
        display: inline-block;
        .prj_b_cond_label {
          display: inline-block;
          width: 6em;
          line-height: 32px;
        }
        .prj_b_cond_component {
          margin-left: -4px;
          width: calc(100% - 6em - 12px);
          display: inline-block;
          vertical-align: middle;
          .ivu-input {
            font-size: 12px;
          }
        }
      }
    }
    .b_base_info {
      .prj_b_cond_tips {
        display: flex;
        font-size: 12px;
        justify-content: start;
        padding-left: 16px;
        padding-bottom: 8px;
        color: #999999;
      }
    }

    .target_datum_info {
      .prj_b_cond_tips {
        display: flex;
        font-size: 12px;
        justify-content: start;
        padding-left: 8px;
        padding-bottom: 8px;
        color: #999999;
      }
    }

  }
}


.new_table{
  .ivu-table-stripe {
    .ivu-table-body tr:nth-child(2n - 1) td,
    .ivu-table-fixed-body tr:nth-child(2n - 1) td{
      background-color: #f5f8fb;
    }
    .ivu-table-body tr:nth-child(2n) td,
    .ivu-table-fixed-body tr:nth-child(2n) td{
      background-color: #ffffff;
    }
  }
  .ivu-table-row:hover td{
    background-color:#d5e8fc !important;
  }


  .ivu-table-header thead th,
  .ivu-table-fixed-header thead th{
    background: #ffffff;
  }
  .ivu-table th{
    color: #999999;
  }
  .ivu-table td, .ivu-table th{
    height: 40px;
    padding: 0;
    border-right-color:transparent;
    border-bottom-color:transparent;
    &:last-child{
      border-right: none;
    }
    *{
      font-size: 12px;
    }
    .ivu-table-cell{
      padding: 0 8px;
      width: 100%;
      .ivu-select-selected-value{
        font-size: 12px;
      }
      .ivu-input,
      .ivu-input-number,
      .ivu-input-number-input{
        border-color: transparent;
        background-color:transparent;
      }
      .ivu-input-number-focused,
      .ivu-input:focus,
      .ivu-input-number:focus,
      .ivu-input:hover,
      .ivu-input-number:hover{
        box-shadow: none;
        border-color:#dbe3eb;
        background-color: #ffffff;

      }
      .ivu-input-number-handler-wrap{
        display: none;
      }
    }
  }
  .ivu-table,.ivu-table-fixed,.ivu-table-fixed-right{
    &:after,&:before{
      width: 0;
      height: 0;
    }
    table{
      border: none;
    }
  }
}

.new_form.ivu-form{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  &.ivu-form-inline{
    .ivu-form-item{
      width: calc(50% - 10px);
    }
  }
  *:not(i){
    font-size: 12px;
    color: #262626;
  }
  .ivu-form-item-label{
    color: #666666;
  }
}
.his_tab .ivu-steps.ivu-steps-small .ivu-steps-head-inner>.ivu-steps-icon.ivu-icon{
  font-size: 18px;
}
.his_tab  .ivu-steps-icon.ivu-icon.ivu-icon-ios-checkmark{
  color: #ffffff !important;
}

.his_tab .ivu-steps-status-process .ivu-steps-head-inner span{
  color: #74d286 !important;
}


.his_tab .ivu-steps-status-finish .ivu-steps-head-inner .ivu-icon-ios-close-circle{
  color: red !important;
}

.his_tab .ivu-steps-status-wait .ivu-steps-head-inner span{
  color: #ccc !important;
}


.tab_warp{
  box-shadow: 0 4px 18px 0 rgba(198,213,229,0.40);
  background: #ffffff;
  .tab_t_info{
    padding: 12px 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f5f8fb;
    position: relative;
    >.col_item{
      display: flex;
      align-items: center;
      width: 33%;
      *{
        font-size: 12px;
      }
      &.pdL20{
        padding-left: 20px;
      }
      .ivu-input{
        font-size: 12px;
        border: 1px solid #dcdee2;
      }
      >.col_con{
        width: calc(100% - 6em);
        display: flex;
        &.fuzz_suer{
          >div{
            width: 100%;
            position: relative;
          }
        }
      }
      >label{
        width: 6em;
        font-size: 12px;
      }
      .ivu-checkbox-wrapper{
        font-size: 12px;
      }
    }
    >.col_item.pd_l24 {
      padding-left: 24px;
    }
  }
  .tab_t_tab{
    padding: 12px 16px 0;
    display: flex;
    .tab_t_btn{
      margin-left: auto;
      .ivu-btn{
        font-size: 12px;
      }
    }
    ul{
      display: flex;
      align-items: center;
      li{
        padding: 0 16px;
        line-height:34px;
        color: #555555;
        height: 46px;
        cursor: pointer;
        &.active{
          color:#3883E5;
          border-bottom: 2px solid #3883E5;
        }
      }
    }
    .tab_btn{
      margin-left: auto;
    }
  }
}
.prj_grad_warp{
  height: calc(100% - 60px);
  .prj_grad_bot{
    height: calc(100% - 58px);
    background: #f5f8fb;
    padding: 16px;
    .prj_grad_bot_con{
      box-shadow: 0 4px 18px 0 rgba(198,213,229,0.40);
      background: #ffffff;
      height: 100%;
      overflow-y:auto;
      .prj_grad_bot_page{
        padding: 0 16px;
      }
    }
  }
}
.prj_role_warp{
  height: calc(100% - 60px);
  .prj_role_bot{
    height: calc(100% - 115px);
    background: #f5f8fb;
    padding: 16px;
    .prj_role_bot_con{
      box-shadow: 0 4px 18px 0 rgba(198,213,229,0.40);
      background: #ffffff;
      height: 100%;
      overflow-y:auto;
      padding:10px 16px;
      &.prov{
        padding: 0;
      }
      .ivu-table-cell{
        *{
          font-size: 12px;
        }
      }
      .list_box{
        .title{
          color: #c8cddc;
          margin: 12px 0;
          display: flex;
          align-items: center;
          > span {
            color: #3883e5;
            padding-left: 8px;
            border-left: 2px solid #3883e5;
          }
          .title_right{
            margin-left: auto;
            span{
              width: 36px;
              height: 36px;
              line-height: 36px;
              background-color: rgba(200, 205, 220, 0.2);
              color: #c8cddc;
              text-align: center;
              border-radius: 50%;
              font-size: 16px;
              vertical-align: top;
              margin-left: 12px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              &.active{
                background-color: #3883e5;
                color: #ffffff;
              }
            }



          }
        }
      }

    }
  }
}

.person_list{
  display: flex;
  flex-wrap: wrap;
  padding:4px 0 32px;
  &:not(:last-child){
    border-bottom: 1px dashed #dddee1;
  }
  > li,.ivu-poptip{
    padding: 14px 12px;
    box-shadow: 0 1px 12px 0 rgba(56,131,229,0.16);
    width: 185px;
    margin:0 12px 16px 0;
    position: relative;
  }
  > li > i.icon-minusSign{
    width: 24px;
    height: 24px;
    background: #e65d4e;
    border-radius: 50%;
    color: #ffffff;
    position: absolute;
    top: -12px;
    right: -12px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
  }
  .add_info{
    text-align: center;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0 1px 12px 0 rgba(0,0,0,0);
    border: 1px dashed rgba(56,131,229,0.80);
    color: #3883e5;
    cursor: pointer;
    padding: 0;
    &:hover{
      background-color:rgba(56,131,229,0.05);
    }
    .add_warp {
      padding: 22px;
      height: 100%;
      span{
        line-height: 24px;
      }
      i.icon-jia{
        font-size: 24px;
      }
    }
  }
  .ivu-poptip-rel{
    width: 100%;
    height: 100%;
  }

  img{
    width: 48px;
    height: 48px;
    display: inline-block;
  }
  > li{
    >div{
      &:last-child{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #999999;
        margin-top: 4px;
        span{
          color: #555555;
        }
      }
      .user{
        display: inline-block;
        width: calc(100% - 52px);
        vertical-align: top;
        padding-left: 13px;
        position: relative;
        i{
          position: absolute;
          right: 0;
          top: 0;
          width: 18px;
          height: 18px;
          text-align: center;
          border-radius: 50%;
          display: inline-block;
          font-size: 10px;
          &.icon-member{
            background: rgba(56,131,229,0.10);
            color: #3883e5;
          }
        }
      }
      .login_name{
        line-height: 16px;
        font-size: 14px;
        color: #999999;
        margin-bottom: 10px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: calc(100% - 18px);
      }
      .user_name{
        font-size: 18px;
        font-weight: 700;
        color: #262626;
      }
    }
  }
}
.add_info_pop{
  z-index: 100;
  .ivu-poptip-body{
    padding: 0;
    .user_list{
      > div:first-child{
        padding: 12px 16px;
      }
      ul{
        max-height: 150px;
        overflow-y: auto;
        line-height: 30px;
        li{
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding: 4px 16px;
          color: #999999;
          &:hover,&.active{
            background: #f8f8f8;
            cursor: pointer;
          }
          span{
            color: #555555;
          }
        }
      }
    }
  }
}

.prj_basic_res_group{
  margin-top: 12px;
  li{
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    *{
      font-size: 12px;
    }
    .res_title{
      width: 10em;
      margin-right: 12px;
      display: flex;
      align-items: center;
      >span{
        padding: 0 8px;
        height: 24px;
        line-height: 24px;
        background: #f5f8fb;
        border: 1px solid #e7eef5;
        border-radius: 3px;
        white-space: nowrap;
        max-width: 10em;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #555555;
        display: inline-block;
      }
    }
    .res_user_l{
      margin-right: 34px;
    }
    .res_user{
      width: calc((100% - 10em - 12px)/2 - 17px);
      display: flex;
      align-items: center;
      >span{
        width: 32px;
        color: #666666;
      }
      >div{
        width: calc(100% - 32px);
      }
    }

  }
}

.ivu-cascader-rel:hover{
  .ivu-input {
    border-color: rgba(56, 131, 229, 0.8) !important;
  }
}

.buoy_operate_warp{
  position: fixed;
  right: 280px;
  bottom: 40px;
  i.iconfont {
    width: 48px;
    height: 48px;
    background: rgba(165, 172, 196, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a5acc4;
    font-size: 22px;
    cursor: pointer;
  }
}

.notional_pooling{
  .prj_value_title{
    display: flex;
    height: 22px;
    line-height: 22px;
    padding-left: 16px;
    border-left: 3px solid #3883e5;
    position: relative;
    color: #262626;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
    .actionWarp {
      margin-left: auto;
      margin-right: 16px;
      color: #999999;
      &:hover{
        color: @defaultColor;
      }
      span {
        color: #999999;
        cursor: pointer;
        font-size: 12px;
        display: inline-flex;
        &:hover{
          color: @defaultColor;
        }
      }
      &.disable {
        &:hover{
          color: #999999;
        }
        span {
          cursor: default;
          &:hover{
            color:#999999;
          }
        }
      }
    }
    &:after{
      content: '';
      position: absolute;
      left: -7px;
      top: -4px;
      width: 0;
      height: 0;
      border: 4px solid transparent;
      border-right-color: #ffffff;
    }
    &:before{
      content: '';
      position: absolute;
      left: -7px;
      bottom: -4px;
      width: 0;
      height: 0;
      border: 4px solid transparent;
      border-right-color: #ffffff;
    }
  }
  .prj_b_title{
    height: 22px;
    line-height: 22px;
    padding-left: 16px;
    border-left: 3px solid @defaultColor;
    position: relative;
    color: #262626;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    .scope_view_btn{
      color: #A5ACC4;
      font-size: 12px;
      margin-left: 8px;
      cursor:pointer;
    }
    &:after{
      content: '';
      position: absolute;
      left: -7px;
      top: -4px;
      width: 0;
      height: 0;
      border: 4px solid transparent;
      border-right-color: #ffffff;
    }
    &:before{
      content: '';
      position: absolute;
      left: -7px;
      bottom: -4px;
      width: 0;
      height: 0;
      border: 4px solid transparent;
      border-right-color: #ffffff;
    }
  }
  .responsibility_group{
    padding-top: 16px;
  }
}

.base_line_info{
  position: fixed;
  z-index: 999;
  padding: 8px 12px;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0,0,0,20%);
  font-size: 12px;
  line-height: 22px;
  .base_line_info_m{
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  li{
    display: flex;
    span{
      color: #999999;
      width: 140px;
    }
  }
}

.ellipsis_tooltip_two{
  .ivu-tooltip{
    width: 100%;
    .ivu-tooltip-rel{
      width: 100%;
      .ellipsis_two{
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        word-break: break-all;
        width: 100%;
      }
    }
  }
}

.cost_basis_top_edit{
  display: flex;
  align-items: center;
  .cost{
    padding: 0 12px;
    height: 38px;
    display: flex;
    align-items: center;
    width: calc(100% - 168px);
    >div{
      display: flex;
      width: 100%;
      align-items: center;
      >span{
        width: 46px;
        cursor: pointer;
        color: #999999;
        &.active{
          font-size: 16px;
          color: #262626;
          font-weight: 500;
        }
      }
      >i{
        cursor: pointer;
        &.no_active{
          color: #c5c8ce;
          cursor: no-drop;
        }
      }
      >div{
        display: flex;
        width: calc(100% - 78px);
        align-items: center;
        position: relative;
        overflow: hidden;
        height: 24px;
        >ul{
          display: flex;
          align-items: center;
          position: absolute;
          left: 0px;
          li{
            color: #999999;
            font-size: 14px;
            margin-right: 22px;
            line-height: 22px;
            cursor: pointer;
            &.active{
              font-size: 16px;
              color: #262626;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  .prj_b_btn{
    margin-left: auto;
  }
}
.costBasisItem_view{
  display: flex;
  align-items: center;
  margin-left: auto;
  overflow: hidden;
  li{
    color: #999999;
    font-size: 14px;
    margin-right: 24px;
    line-height: 22px;
    cursor: pointer;
    &.active{
      font-size: 16px;
      color: #262626;
      font-weight: 500;
    }
  }
}

.cost_basic_emp_month_avg {
  /*padding: 8px 16px 0 16px;*/
  .cost_basic_emp_month_avg_title {
    margin-top: 12px;
    margin-bottom: 12px;
    display: inline-block;
    padding-left: 8px;
    font-size: 12px;
  }
}

.cost_basic_emp_month_avg_table {
  .ivu-table-header {
    thead {
      tr:first-child {
        th:not(:last-child) {
          border-right: 1px solid #dbe3eb !important;
        }
      }
      tr:first-child th {
        text-align: center;
      }
      tr:nth-child(2) {
        th:nth-child(2n) {
          border-right: 1px solid #dbe3eb !important;
        }
        th:last-child {
          border-right: none !important;
        }
      }
    }
    th {
      background-color: #f5f8fb !important;
    }
  }
  .ivu-table-body {
    .ivu-table-tbody {
      td:nth-child(2n) {
        border-right: 1px solid #dbe3eb !important;
      }
      td:last-child {
        border-right: none !important;
      }
    }
  }
}

/*异常偏差管理-项目集详情页*/
.prj_bug_warp {
  &.header_fix{
    .prjHeader.header_menu{
      position: fixed !important;
      top:0;
    }
    .prj_bug_con{
      padding-top: 76px;
      height: 100%;
      overflow: auto;
    }
  }
  .prj_bug_con {
    min-height: calc(100% - 60px);
    background: #f8f8f9;
    padding: 16px;
    display: flex;
    position: relative;

    .prj_bug_left {
      width: 100%;
      height: 100%;
      .prj_b_l_bot {
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgb(144 144 144 / 20%);
        padding-top: 12px;
        .b_base_info {
          > div {
            margin-bottom: 12px;
          }
          .prj_b_b_filter {
            display: flex;
            align-items: center;
            padding: 0 16px;
            flex-wrap: wrap;
            &.col_4{
              .fol_item {
                width: calc(100% / 4);
              }
            }
            .fol_item {
              display: flex;
              height: 32px;
              align-items: center;
              margin-bottom: 4px;
              &.pdL20 {
                padding-left: 20px;
              }
              > div {
                padding-left: 24px;
                color: #262626;
                width: calc(100% - 6em);
              }
              .span_ellipsis {
                display: flex;
                > span {
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  max-width: calc(100% - 10px);
                }
              }
              * {
                font-size: 12px;
              }
              label {
                width: 6em;
                color: #666666;
              }
            }
          }
          .new_table_warp {
            padding: 0 16px;
            border-top: 1px solid #f1f5f9;
            padding-bottom: 12px;
          }
        }
      }

      .prj_dvt_b_l_bot {
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgb(144 144 144 / 20%);
        padding: 12px 0;
        min-height: calc(100% - 242px);
        .b_base_info {
          > div {
            margin-bottom: 24px;
          }
          .prj_dvt_top {
            display: flex;
            padding: 0 16px;
            .prj_dvt_top_cond {
              label {
                width: 6em;
                color: #666666;
                font-size: 12px;
                display: inline-block;
              }
              .prj_dvt_top_cond_month {
                display: inline-block;
                .ivu-input {
                  font-size: 12px;
                }
                .ivu-select-dropdown {
                  font-size: 12px;
                }
              }
            }
            .tab_blank {
              margin-left: auto;
              display: flex;
              font-size: 12px;
              span {
                text-align: center;
                cursor: pointer;
                padding: 0 12px;
                border: 1px solid #dddee1;
                padding-top: 2px;
                .ivu-badge {
                  border: none;
                  > span {
                    border: none;
                  }
                  .ivu-badge-count {
                    top: -9px;
                    right: 12px;
                  }
                }
              }
              span:first-child {
                border-radius: 5px 0 0 5px;
              }
              span:last-child {
                border-radius: 0 5px 5px 0;
              }
              span:not(:first-child) {
                margin-left: -1px;
              }
              span.active {
                color: #3883e5;
                border-color: #3883e5;
                z-index: 2;
                position: relative;
              }

            }
          }
          .notional_pooling {
            .prj_b_title {
              .ivu-icon {
                margin-left: 8px;
                cursor: pointer;
              }
              .costStatistic {
                font-size: 12px;
                margin-left: auto;
                padding-right: 34px;
                cursor: pointer;
                .icon-statistic {
                  font-size: 14px;
                }
              }
            }
            .new_table_warp {
              padding: 0 16px;
            }
          }

          .prj_b_b_filter {
            display: flex;
            align-items: center;
            padding: 0 16px;
            flex-wrap: wrap;
            &.col_2 {
              .fol_item {
                width: calc(100% / 2);
              }
            }
            &.col_3 {
              .fol_item {
                width: calc(100% / 3);
              }
            }
            &.col_4 {
              .fol_item {
                width: calc(100% / 4);
              }
            }
            .fol_item {
              display: flex;
              height: auto;
              align-items: center;
              margin-bottom: 4px;
              min-height: 32px;
              &.pdL20 {
                padding-left: 20px;
              }
              > div {
                padding-left: 24px;
                color: #262626;
                width: calc(100% - 7em);
              }
              .span_ellipsis {
                display: flex;
                > span {
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  max-width: calc(100% - 10px);
                }
              }
              * {
                font-size: 12px;
              }
              label {
                width: 7em;
                color: #666666;
              }
            }
          }

        }
      }

    }

    .prj_b_title {
      height: 22px;
      line-height: 22px;
      padding-left: 16px;
      border-left: 3px solid #3883e5;
      position: relative;
      color: #262626;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
    }

  }
}
