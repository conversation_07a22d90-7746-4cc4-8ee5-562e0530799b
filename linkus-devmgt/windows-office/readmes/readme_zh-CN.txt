

======================================================================

LibreOffice 7.1 自述文档

======================================================================





本自述文件的最新更新请见 https://git.libreoffice.org/core/tree/master/README.md



本文件包含了关于 LibreOffice 软件的重要信息。建议您在安装前仔细阅读这些信息。



LibreOffice 社区负责此产品的开发，我们邀请您参与进来，成为全球社区中的一员。如果您是新用户，可以访问 LibreOffice 网站查阅 LibreOffice 项目以及相关信息。访问 https://zh-cn.libreoffice.org。



LibreOffice 是不是真的对任何用户都免费?

----------------------------------------------------------------------



LibreOffice 免费给所有人使用。您可以复制 LibreOffice 的副本并安装在任意数量的计算机上，并以任何目的使用 (包括商用、政府、公共管理以及教育用途)。更多详情请见随 LibreOffice 下载而来的许可文本。



为什么 LibreOffice 对所有用户免费?

----------------------------------------------------------------------



您可以免费使用此 LibreOffice 副本，因为是独立贡献者以及合作赞助商们设计、开发、测试、翻译、编写、支持、营销并以很多其他方式帮助实现了 LibreOffice 今天的样子——世界领先的家用、办公用开源生产软件。



如果您感谢他们的工作，并愿意确保 LibreOffice 将来能继续开发，请考虑为此项目作贡献——详情请见 https://zh-cn.libreoffice.org/get-involved/。每个人都可以作出自己的贡献。



----------------------------------------------------------------------

安装注意事项

----------------------------------------------------------------------



LibreOffice 要求更新版本的 Java 运行时环境 (JRE) 才能提供完整功能。JRE 未包含在 LibreOffice 安装包中，应该单独安装。



系统要求

----------------------------------------------------------------------



* Microsoft Windows 7 SP1, 8, 8.1 Update (S14) 或 Windows 10



请注意，安装过程需要拥有管理员权限。



通过使用下列含有该安装程序的命令行开关项，可以强制或禁止将 LibreOffice 注册为 Microsoft Office 格式默认的应用程序:



* REGISTER_ALL_MSO_TYPES=1 可强制将 LibreOffice 注册为 Microsoft Office 格式的默认应用程序。

* REGISTER_NO_MSO_TYPES=1 可阻止将 LibreOffice 注册为 Microsoft Office 格式的默认应用程序。



请确保系统的临时目录有足够的空闲空间，还请确保已取得读取、写入与执行权限。在开始安装过程之前请先关闭所有其他程序。



在基于 Debian/Ubuntu 的 Linux 系统上安装 LibreOffice

----------------------------------------------------------------------



有关在安装完美国英语版本的 LibreOffice 之后如何安装语言包的说明，请阅读如下题为「安装语言包」的部分。



将下载好的压缩包解压，您会看到内容位于子目录中。打开文件管理器窗口，并进入名称以「LibreOffice_」开头、后接版本号与平台信息的目录。



该目录中有一个名为「DEBS」的子目录。切换至该「DEBS」目录。



右击该目录，并选择「在终端中打开」。这将打开一个新的终端窗口。在终端窗口的命令行处输入以下的命令 (命令执行前将提示您输入 root 用户密码):



以下命令将安装 LibreOffice 以及桌面环境整合软件包 (您可以直接将其复制粘贴至命令提示界面中，不用手动输入):



sudo dpkg -i *.deb



安装过程已经完成。现在您应该可以在桌面的「应用程序/办公」菜单中看到所有 LibreOffice 应用程序的图标了。



在 Fedora、openSUSE、Mandriva 以及其他 Linux系统中使用 RPM 软件包安装 LibreOffice

----------------------------------------------------------------------



有关在安装完美国英语版本的 LibreOffice 之后如何安装语言包的说明，请阅读如下题为「安装语言包」的部分。



将下载好的压缩包解压，您会看到内容位于子目录中。打开文件管理器窗口，并进入名称以「LibreOffice_」开头、后接版本号与平台信息的目录。



此目录包含名为 "RPMS" 的子目录。切换至"RPMS"目录。



右击该目录，并选择「在终端中打开」。这将打开一个新的终端窗口。在终端窗口的命令行处输入以下的命令 (命令执行前将提示您输入 root 用户密码):



对于基于 Fedora 的系统: sudo dnf install *.rpm



对于基于 Mandriva 的系统: sudo urpmi *.rpm



对于其他基于 RPM 的系统 (openSUSE 等): rpm -Uvh *.rpm



安装过程已经完成。现在您应该可以在桌面的「应用程序/办公」菜单中看到所有 LibreOffice 应用程序的图标了。



作为备选方式，您可以使用位于本归档目录根文件夹中的「install"脚本。该脚本会配置 LibreOffice 的安装，以使其拥有一个独立于您的常规 LibreOffice 配置文件的配置文件。请注意，这将不会安装桌面环境集成部分，例如桌面菜单项以及桌面 MIME 类型注册。



对上述安装说明中未提及的Linux发行版中的桌面集成的说明

----------------------------------------------------------------------



对于本安装说明中未特别提及的其他 Linux 发行版，安装 LibreOffice 应该很简单。主要的差异大概在桌面集成方面。



RPMS 或 DEBS 目录中分别还包含名为 libreoffice7.1-freedesktop-menus-*******-1.noarch.rpm (或者 libreoffice7.1-debian-menus_*******-1_all.deb 或类似的) 的软件包。该软件包可供所有支持 Freedesktop.org 规范/推荐 (https://en.wikipedia.org/wiki/Freedesktop.org) 的 Linux 发行版使用，也可在前文说明中未提及的其他 Linux 发行版中安装。



安装语言包

----------------------------------------------------------------------



下载适合您的语言与平台的语言包。可从主安装包所在位置下载。在 Nautilus 文件管理器中，将下载的压缩包解压到一个目录 (例如您的桌面)。请确保您已退出包括「快速启动器」在内的所有 LibreOffice 应用程序。



切换至您解压缩语言包所在的目录。



现在请进入解压得到的目录。例如，对于 32 位基于 Debian/Ubuntu 系统的法语语言包，其目录名称为「LibreOffice_」后接版本信息，后接「Linux_x86_langpack-deb_fr」。



现在切换至要安装的软件包所在目录。对于基于 Debian/Ubuntu 的系统，该目录应为 DEBS。对于 Fedora、openSUSE 或 Mandriva 系统，该目录应为 RPMS。



在 Nautilus 文件管理器中，右击该目录，并选择「在终端中打开」命令。在刚打开的终端窗口中执行安装语言包的命令 (下述各命令都将提示您输入 root 用户的密码):



对于基于 Debian/Ubuntu 的系统: sudo dpkg -i *.deb



对于基于 Fedora 的系统: su -c 'dnf install *.rpm'



对于基于 Mandriva 的系统: sudo urpmi *.rpm



对于其它使用RPM的系统 (openSUSE等): rpm -Uvh *.rpm



现在启动一个 LibreOffice 应用程序 - 比如 Writer。进入「Tools」菜单并选择 Options。在 Options 对话框中点击「Language Settings」，然后点击「Languages」。在「User interface」下拉框中选择您刚安装的语言。如果愿意，为「Locale setting」(区域设置)、「Default currency」(默认货币) 以及「Default languages for documents」(默认文档语言) 执行相同的操作。



调整这些设置后点击「确定」。对话框将关闭，您将看到一条信息提示，告知您您的变更需要退出并重新启动 LibreOffice 方可生效 (如果启动了「快速启动器」，也应退出它)。



下次您启动 LibreOffice 时，它将以您刚安装的语言启动。



----------------------------------------------------------------------

程序启动时出现的问题

----------------------------------------------------------------------



显卡驱动程序经常会引发 LibreOffice 启动问题 (例如应用程序挂起) 和屏幕显示问题。如果出现这些问题，请更新您的显卡驱动程序或尝试使用操作系统附带的图形驱动程序。如果显示 3D 对象时出现问题，则通常可以通过禁用「工具」-「选项」- LibreOffice -「视图」-「3D 视图」下的「使用 OpenGL」选项解决。



----------------------------------------------------------------------

Windows 下的 ALPS/Synaptics 笔记本触摸板

----------------------------------------------------------------------



由于 Windows 驱动的问题，您将不能通过使用 ALPS/Synaptics 触摸板的滚动条控制 LibreOffice 的上下滚动。



要启用触摸板滚动功能，请将下面几行内容添加到配置文件「C:\Program Files\Synaptics\SynTP\SynTPEnh.ini」中，然后重新启动您的计算机:



[LibreOffice]



FC = "SALFRAME"



SF = 0x10000000



SF |= 0x00004000



在 Windows 的不同版本中配置文件的位置可能会不同。



----------------------------------------------------------------------

快捷键

----------------------------------------------------------------------



在 LibreOffice 中只能使用操作系统尚未使用的快捷键组合。如果 LibreOffice 帮助中描述的一些快捷键键在使用 LibreOffice 时根本无法起作用，请首先检查这些快捷键是否已经被操作系统占用。要解除此类冲突，您可以更改操作系统的快捷键指定。另外，您也可以更改 LibreOffice 中绝大部分的快捷键。有关此主题的详细信息，请参阅 LibreOffice 帮助或操作系统的帮助文档。



----------------------------------------------------------------------

从 LibreOffice 中将文档发送为电子邮件时出现问题

----------------------------------------------------------------------



通过「文件」-「发送」-「文档作为电子邮件」或「文档作为 PDF 附件」发送文档时可能会出现问题（程序崩溃或死锁）。这是由于某些文件版本的 Windows 系统文件「Mapi」（消息应用程序编程接口）导致的问题。不幸的是，我们无法确定具体哪一个版本有问题。若需要更多详细信息，请访问 https://www.microsoft.com 并在知识库中搜索「mapi dll」。



----------------------------------------------------------------------

辅助功能的重要提示

----------------------------------------------------------------------



更多关于 LibreOffice 辅助功能的信息，请参考: https://www.libreoffice.org/accessibility/



----------------------------------------------------------------------

用户支持

----------------------------------------------------------------------



主支持页面 http://zh-cn.libreoffice.org/get-help/community-support/ 提供有关 LibreOffice 的各种可能的帮助。您的问题可能已有答复——请到 http://www.libreofficechina.org 中文社区论坛或 QQ 群 190535062 查找问题或提问，或在 http://www.libreoffice.org/lists/users/ 中搜索 '<EMAIL>' 邮件列表的归档。您还可以将问题发到 *********************邮件列表。如果您想订阅该列表 (以获取邮件答复)，请发送一封空邮件给: <EMAIL>。



也请查阅 LibreOffice 网站上的 FAQ 段落。



----------------------------------------------------------------------

报告 bug & 问题

----------------------------------------------------------------------



当前我们使用 BugZilla 报告、追踪和解决 bug。该系统托管在 https://bugs.documentfoundation.org/。我们鼓励所有的用户觉得自己有权力，而且很欢迎，报告在您特定的平台中遇到的 bug。积极的报告 bug 是用户社区能为持续开发并改进的 LibreOffice 所做的最重要的贡献。



----------------------------------------------------------------------

参与我们

----------------------------------------------------------------------



您在该重要的开源项目发展中的积极参与将会对 LibreOffice 社区非常有帮助。



作为一名用户，您已经是本套件开发过程中重要的一环。我们想鼓励您成为更加活跃的社区长期贡献者。请加入并查看 LibreOffice 网站的贡献页面。



如何开始

----------------------------------------------------------------------



开始贡献最简单的方式是订阅一个或多个邮件列表，潜水一段时间，并逐渐使用邮件列表归档熟悉自 2000 年 10 月 LibreOffice 源码发布以来所涉及到的诸多话题。当您感到合适时，唯一要做的就是发送一封自我介绍的邮件并加入讨论。如果您熟悉开源项目，请查看 LibreOffice 网站 的 To-Do 列表，看是否有您能帮到忙的地方。



订阅邮件

----------------------------------------------------------------------



这里有一些可供订阅的邮件列表: https://zh-cn.libreoffice.org/get-help/mailing-lists/



* 新闻: <EMAIL> *推荐所有用户订阅* (低流量)

* 主要的用户邮件列表: <EMAIL> *容易淹没在讨论当中* (高流量)

* 市场营销项目: <EMAIL> *日后发展* (流量日益增长)

* 常规开发者邮件列表: <EMAIL> (比较繁忙)



参与一个或多个项目

----------------------------------------------------------------------



您可以为这个重要的开源项目做出主要的贡献，即使您的软件设计和编码经验有限。是的，您可以！



我们希望您能够愉快地使用新的 LibreOffice 7.1 并在线加入我们。



LibreOffice 社区



----------------------------------------------------------------------

已经使用的/修改的源代码

----------------------------------------------------------------------



部分版权所有 1998, 1999 James Clark。部分版权所有 1996, 1998 Netscape Communications Corporation。
