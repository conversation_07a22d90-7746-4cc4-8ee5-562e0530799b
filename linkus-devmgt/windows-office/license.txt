# Licensing and Legal information

This product is made available subject to the terms of the Mozilla Public License, v. 2.0. A copy of the MPL
    Version 2 license can be found below.

Jump to MPL Version 2

Third Party Code Additional copyright notices and license terms applicable to portions of the Software can be
    found below in this document.

All trademarks and registered trademarks mentioned herein are the property of their respective owners.

Copyright © 2000–2021 LibreOffice contributors. All rights reserved.

This product is based on OpenOffice.org. Portions of this software are copyright © 2000-2011, Oracle and/or its
    affiliates.

This product has been created by The Document Foundation, incorporating many modifications from different
    contributors, see http://www.libreoffice.org/ for more details.

Note: Do not translate or localize this document. Only English version is legally binding.

## Contents

Libraries

Extensions

Fonts

Dictionaries

Artwork

GNU Lesser General Public License Version 3

GNU Lesser General Public License Version 2.1

GNU Library General Public License Version 2

GNU General Public License Version 3

GNU General Public License Version 2

Common Public License Version 1.0 (CPL)

Mozilla Public License Version 1.1

Mozilla Public License Version 2.0

SIL Open Font License Version 1.1

Apache License

The LaTeX Project Public License

Creative Commons Attribution-ShareAlike 3.0 Unported

Creative Commons Attribution-ShareAlike 4.0 International

# Third Party Code Additional Copyright
    Notices and License Terms

# Libraries

## Apache Commons

The following software may be included in this product: Apache Commons (codec, httpclient, lang, logging).
        Use of any of this software is governed by the terms of the license below:

Jump to Apache License Version 2.0

## beanshell

The following software may be included in this product: beanshell. Use of any of this software is governed
        by the terms of the license below:

Jump to Apache License Version 2.0

## C++ Boost Library

The following software may be included in this product: C++ Boost Library. Use of any of this software is
        governed by the terms of the license below:

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization obtaining a copy of the software
        and accompanying documentation covered by this license (the "Software") to use, reproduce, display, distribute,
        execute, and transmit the Software, and to prepare derivative works of the Software, and to permit
        third-parties to whom the Software is furnished to do so, all subject to the following:

The copyright notices in the Software and this entire statement, including the above license grant, this
        restriction and the following disclaimer, must be included in all copies of the Software, in whole or in part,
        and all derivative works of the Software, unless such copies or derivative works are solely in the form of
        machine-executable object code generated by a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN
        NO EVENT SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE FOR ANY DAMAGES OR OTHER
        LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR
        THE USE OR OTHER DEALINGS IN THE SOFTWARE.

## Breakpad

The following software may be included in this product: Google breakpad crash-reporting library. Use of any
        of this software is governed by the terms of the license below:

Copyright (c) 2006, Google Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of Google Inc. nor the names of its contributors may be used to endorse or promote
            products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

Copyright 2001-2004 Unicode, Inc.

Disclaimer

This source code is provided as is by Unicode, Inc. No claims are made as to fitness for any particular
        purpose. No warranties of any kind are expressed or implied. The recipient agrees to determine applicability of
        information provided. If this file has been purchased on magnetic or optical media from Unicode, Inc., the sole
        remedy for any claim will be exchange of defective media within 90 days of receipt.

Limitations on Rights to Redistribute This Code Unicode, Inc. hereby grants the right to freely use the
        information supplied in this file in the creation of products supporting the Unicode Standard, and to make
        copies of this file in any form for internal or external distribution as long as this notice remains
        attached.

## CLEW

The following software may be included in this product: CLEW (OpenCL Extension Wrangler). Use of any of
        this software is governed by the terms of the license below:

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization obtaining a copy of the software
        and accompanying documentation covered by this license (the "Software") to use, reproduce, display, distribute,
        execute, and transmit the Software, and to prepare derivative works of the Software, and to permit
        third-parties to whom the Software is furnished to do so, all subject to the following:

The copyright notices in the Software and this entire statement, including the above license grant, this
        restriction and the following disclaimer, must be included in all copies of the Software, in whole or in part,
        and all derivative works of the Software, unless such copies or derivative works are solely in the form of
        machine-executable object code generated by a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN
        NO EVENT SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE FOR ANY DAMAGES OR OTHER
        LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR
        THE USE OR OTHER DEALINGS IN THE SOFTWARE.

## CLucene

The following software may be included in this product: CLucene. Use of any of this software is governed by
        the terms of the license below:

CLucene is distributed under the GNU Lesser General Public License (LGPL) or the Apache License, Version
        2.0

Jump to LGPL Version 2.1

Jump to Apache License Version 2.0

## CoinMP

The following software may be included in this product: CoinMP. Use of any of this software is governed by
        the terms of the license below:

Jump to Common Public License Version 1.0 (CPL)

## Epoxy

The following software may be included in this product: epoxy.

The libepoxy project code is covered by the MIT license:

Copyright © 2013-2014 Intel Corporation

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
            associated documentation files (the "Software"), to deal in the Software without restriction, including
            without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
            copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the
            following conditions:

The above copyright notice and this permission notice (including the next paragraph) shall be included
            in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
            LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
            EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
            IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
            USE OR OTHER DEALINGS IN THE SOFTWARE.

The generated code is derived from Khronos's xml files, which appear under the following license:

Copyright (c) 2013 The Khronos Group Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and/or
            associated documentation files (the "Materials"), to deal in the Materials without restriction, including
            without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
            copies of the Materials, and to permit persons to whom the Materials are furnished to do so, subject to the
            following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
            portions of the Materials.

THE MATERIALS ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
            LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
            EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
            IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE MATERIALS OR
            THE USE OR OTHER DEALINGS IN THE MATERIALS.

## expat XML Parser Toolkit

The following software may be included in this product: expat XML Parser Toolkit. Use of any of this
        software is governed by the terms of the license below:

Copyright (c) 1998, 1999, 2000 Thai Open Source Software Center Ltd and Clark Cooper

Copyright (c) 2001, 2002, 2003 Expat maintainers.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
        SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
        OTHER DEALINGS IN THE SOFTWARE.

## Flute

The following software may be included in this product: Flute. Use of any of this software is governed by the
        terms of the license below:

### W3C IPR SOFTWARE NOTICE

Copyright © 2002 World Wide Web Consortium, (Massachusetts Institute of Technology, Institut National de
        Recherche en Informatique et en Automatique, Keio University). All Rights Reserved.

Note: The original version of the W3C Software Copyright Notice and License could be
        found at http://www.w3.org/Consortium/Legal/copyright-software-19980720

Copyright © 1994-2000 World Wide Web Consortium, (Massachusetts Institute of Technology, Institut
        National de Recherche en Informatique et en Automatique, Keio University).
        All Rights Reserved. http://www.w3.org/Consortium/Legal/

This W3C work (including software, documents, or other related items) is being provided by the copyright holders
        under the following license. By obtaining, using and/or copying this work, you (the licensee) agree that you have
        read, understood, and will comply with the following terms and conditions:

Permission to use, copy, and modify this software and its documentation, with or without modification, for any
        purpose and without fee or royalty is hereby granted, provided that you include the following on ALL copies of the
        software and documentation or portions thereof, including modifications, that you make:

The full text of this NOTICE in a location viewable to users of the redistributed or derivative work.

Any pre-existing intellectual property disclaimers, notices, or terms and conditions. If none exist, a
            short notice of the following form (hypertext is preferred, text is permitted) should be used within the body
            of any redistributed or derivative code: "Copyright © 2002 World Wide Web
            Consortium, (Massachusetts Institute of Technology, Institut National de Recherche en Informatique et en Automatique, Keio University). All Rights Reserved. http://www.w3.org/Consortium/Legal/" 

Notice of any changes or modifications to the W3C files, including the date changes were made. (We
            recommend you provide URIs to the location from which the code is derived.)

THIS SOFTWARE AND DOCUMENTATION IS PROVIDED "AS IS," AND COPYRIGHT HOLDERS MAKE NO REPRESENTATIONS OR
        WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY OR FITNESS FOR ANY
        PARTICULAR PURPOSE OR THAT THE USE OF THE SOFTWARE OR DOCUMENTATION WILL NOT INFRINGE ANY THIRD PARTY PATENTS,
        COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.

COPYRIGHT HOLDERS WILL NOT BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF
        ANY USE OF THE SOFTWARE OR DOCUMENTATION.

The name and trademarks of copyright holders may NOT be used in advertising or publicity pertaining to the
        software without specific, written prior permission. Title to copyright in this software and any associated
        documentation will at all times remain with copyright holders.

## Firebird

The applicable and approved licenses for the source files of the Firebird RDBMS project are:

1) InterBase Public License (IPL), version 1.0

2) Initial Developer's Public License (IDPL), version 1.0

The IPL is copyright of Borland Corp., the other licenses are copyright by the source code authors and
        contributors. Both are variants of the Mozilla Public License V.1.1 (MPL). See http://www.firebirdsql.org/en/licensing/

## GLM

The following software may be included in this product: OpenGL Mathematics (GLM). Use of any of this
        software is governed by the terms of the license below:

### The MIT License

Copyright (c) 2005 - 2013 G-Truc Creation (www.g-truc.net)

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
        SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
        OTHER DEALINGS IN THE SOFTWARE.

## GPGME

The following software may be included in this product: GnuPG Made Easy (GPGME). Use of any of this
        software is governed by the terms of the license below:

Copyright 2001-2017 g10 Code GmbH

Jump to LGPL Version 2.1

## Graphite2

The following software may be included in this product: Graphite2. Use of any of this software is governed
        by the terms of the license below:

Copyright 2010, SIL International All rights reserved.

This library is free software; you can redistribute it and/or modify it under the terms of the GNU Lesser
        General Public License as published by the Free Software Foundation; either version 2.1 of License, or (at your
        option) any later version.

This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the
        implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public
        License for more details.

You should also have received a copy of the GNU Lesser General Public License along with this library in the
        file named "LICENSE". If not, write to the Free Software Foundation, Inc., 59 Temple Place,  Suite 330,
        Boston, MA 02111-1307, USA or visit their web page on the  internet at http://www.fsf.org/licenses/lgpl.html.

Alternatively, you may use this library under the terms of the Mozilla Public License (http://mozilla.org/MPL) or under the GNU General Public License, as published by
        the Free Software Foundation; either version 2 of the license or (at your option) any later version.

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

## HarfBuzz

HarfBuzz is licensed under the so-called "Old MIT" license.  Details follow. For parts of HarfBuzz that are
        licensed under different licenses see individual files names COPYING in subdirectories where applicable.

Copyright © 2010,2011,2012  Google, Inc.
        Copyright © 2012  Mozilla Foundation
        Copyright © 2011  Codethink Limited
        Copyright © 2008,2010  Nokia Corporation and/or its subsidiary(-ies)
        Copyright © 2009  Keith Stribley
        Copyright © 2009  Martin Hosken and SIL International
        Copyright © 2007  Chris Wilson
        Copyright © 2006  Behdad Esfahbod
        Copyright © 2005  David Turner
        Copyright © 2004,2007,2008,2009,2010  Red Hat, Inc.
        Copyright © 1998-2004  David Turner and Werner Lemberg

For full copyright notices consult the individual files in the package.

Permission is hereby granted, without written agreement and without license or royalty fees, to use, copy,
        modify, and distribute this software and its documentation for any purpose, provided that the above copyright
        notice and the following two paragraphs appear in all copies of this software.

IN NO EVENT SHALL THE COPYRIGHT HOLDER BE LIABLE TO ANY PARTY FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR
        CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OF THIS SOFTWARE AND ITS DOCUMENTATION, EVEN IF THE COPYRIGHT
        HOLDER HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

THE COPYRIGHT HOLDER SPECIFICALLY DISCLAIMS ANY WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE SOFTWARE PROVIDED HEREUNDER IS ON AN
        "AS IS" BASIS, AND THE COPYRIGHT HOLDER HAS NO OBLIGATION TO PROVIDE MAINTENANCE, SUPPORT, UPDATES,
        ENHANCEMENTS, OR MODIFICATIONS.

## HSQLDB

The following software may be included in this product: HSQLDB. Use of any of this software is governed by
        the terms of the license below:

### ORIGINAL LICENSE (a.k.a. "hypersonic_lic.txt")

For content, code, and products originally developed by Thomas Mueller and the Hypersonic SQL Group:

Copyright (c) 1995-2000 by the Hypersonic SQL Group. All rights reserved. Redistribution and use in source
        and binary forms, with or without modification, are permitted provided that the following conditions are
        met:

Redistribution of source code must retain the above copyright notice, this list of conditions and the
        following disclaimer.

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and the
        following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of the Hypersonic SQL Group nor the names of its contributors may be used to endorse or
        promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE HYPERSONIC SQL GROUP, OR CONTRIBUTORS BE LIABLE FOR
        ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE. This software consists of voluntary contributions made by many individuals on behalf of the Hypersonic
        SQL Group.

### For work added by the HSQL Development Group (a.k.a. hsqldb_lic.txt)

Copyright (c) 2001-2004, The HSQL Development Group All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistribution of source code must retain the above copyright notice, this list of conditions and the
        following disclaimer.

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and the
        following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of the HSQL Development Group nor the names of its contributors may be used to endorse or
        promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL HSQL DEVELOPMENT GROUP, HSQLDB.ORG, OR CONTRIBUTORS BE
        LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
        LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
        INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
        (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
        POSSIBILITY OF SUCH DAMAGE.

## Hunspell

The following software may be included in this product: Hunspell. Use of any of this software is governed by
        the terms of the license below:

GPL 2.0/LGPL 2.1/MPL 1.1 tri-license

The contents of this software may be used under the terms of the GNU General Public License Version 2 or
        later (the "GPL"), or the GNU Lesser General Public License Version 2.1 or later (the "LGPL") or (excepting the
        LGPLed GNU gettext library in the intl/ directory) the Mozilla Public License Version 1.1 or later (the
        "MPL").

Software distributed under these licenses is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied. See the licenses for the specific language governing rights and limitations under
        the licenses.

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

## Hyphen

The following software may be included in this product: Hyphen. Use of any of this software is governed by
        the terms of the license below:

GPL 2.0/LGPL 2.1/MPL 1.1 tri-license

The contents of this software may be used under the terms of the GNU General Public License Version 2 or
        later (the "GPL"), or the GNU Lesser General Public License Version 2.1 or later (the "LGPL") or (excepting the
        LGPLed GNU gettext library in the intl/ directory) the Mozilla Public License Version 1.1 or later (the
        "MPL").

The Plain TeX hyphenation tables "hyphen.tex" by Donald E. Knuth has a non MPL/LGPL compatible license, but
        freely redistributable: "Unlimited copying and redistribution of this file are permitted as long as this file
        is not modified. Modifications are permitted, but only if the resulting file is not named hyphen.tex."

Software distributed under these licenses is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied. See the licenses for the specific language governing rights and limitations under
        the licenses.

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

## IAccessible2

The following software may be included in this product: IAccessible2 API.

IAccessible2 IDL Specification

Copyright (c) 2007, 2013 Linux Foundation
    Copyright (c) 2006 IBM Corporation
    Copyright (c) 2000, 2006 Sun Microsystems, Inc.
    All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
    following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
        following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the
        following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of the Linux Foundation nor the names of its contributors may be used to endorse or
        promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
    WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
    GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This BSD License conforms to the Open Source Initiative "Simplified BSD License" as published at: http://www.opensource.org/licenses/bsd-license.php

IAccessible2 is a trademark of the Linux Foundation. The IAccessible2 mark may be used in accordance with the
    Linux Foundation
    Trademark Policy to indicate compliance with the IAccessible2 specification.

Find out more about IAccessible2 at http://accessibility.linuxfoundation.org/.

## ICU

The following software may be included in this product: ICU. Use of any of this software is governed by the
        terms of the license below:

### ICU License - ICU 1.8.1 and later

COPYRIGHT AND PERMISSION NOTICE

Copyright (c) 1995-2002 International Business Machines Corporation and others All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, and/or sell copies of the Software, and to permit
        persons to whom the Software is furnished to do so, provided that the above copyright notice(s) and this
        permission notice appear in all copies of the Software and that both the above copyright notice(s) and this
        permission notice appear in supporting documentation.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD
        PARTY RIGHTS. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS NOTICE BE LIABLE FOR ANY
        CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
        DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
        CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not be used in advertising or
        otherwise to promote the sale, use or other dealings in this Software without prior written authorization of
        the copyright holder.

All trademarks and registered trademarks mentioned herein are the property of their respective owners.

## libabw

The following software may be included in this product: libabw. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## Libassuan

The following software may be included in this product: Libassuan. Use of any of this software is governed
        by the terms of the license below:

Copyright (C) 2001-2013 Free Software Foundation, Inc.

Copyright (C) 2001-2017 g10 Code GmbH

Jump to LGPL Version 2.1

## libcdr

The following software may be included in this product: libcdr. Use of any of this software is governed by
        the terms of the license below:

MPL 1.1 / LGPL v2+ / GPL v2+

Jump to GPL Version 2

Jump to LGPL Version 2

Jump to MPL Version 1.1

## libcmis

The following software may be included in this product: libcmis. Use of any of this software is governed by
        the terms of the license below:

MPL 1.1 / LGPL v2+ / GPL v2+

Jump to GPL Version 2

Jump to LGPL Version 2

Jump to MPL Version 1.1

## libcurl

The following software may be included in this product: libcurl. Use of any of this software is governed by
        the terms of the license below:

Copyright (c) 1996 - 2009, Daniel Stenberg, <<EMAIL>>.

All rights reserved.

Permission to use, copy, modify, and distribute this software for any purpose with or without fee is hereby
        granted, provided that the above copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD
        PARTY RIGHTS. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
        SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not be used in advertising or
        otherwise to promote the sale, use or other dealings in this Software without prior written authorization of
        the copyright holder.

## libe-book

The following software may be included in this product: libe-book. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## libepubgen

The following software may be included in this product: libepubgen. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## libetonyek

The following software may be included in this product: libetonyek. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## libfreehand

The following software may be included in this product: libfreehand. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## Libgpg-error

The following software may be included in this product: Libgpg-error. Use of any of this software is governed
        by the terms of the license below:

Copyright (C) 2001-2017 g10 Code GmbH

Jump to LGPL Version 2.1

## libjpeg-turbo

The following software may be included in this product: libjpeg-turbo. libjpeg-turbo is covered by three
        compatible BSD-style open source licenses:

The IJG (Independent JPEG Group) License, which is listed belowThis license applies to the libjpeg API library and associated programs (any code inherited from
                libjpeg, and any modifications to that code.)

The IJG (Independent JPEG Group) License, which is listed below

This license applies to the libjpeg API library and associated programs (any code inherited from
                libjpeg, and any modifications to that code.)

The Modified (3-clause) BSD License, which is listed belowThis license covers the TurboJPEG API library and associated programs, as well as the build
                system.

The Modified (3-clause) BSD License, which is listed below

This license covers the TurboJPEG API library and associated programs, as well as the build
                system.

The zlib LicenseThis license is a subset of the other two, and it covers the libjpeg-turbo SIMD extensions.

The zlib License

This license is a subset of the other two, and it covers the libjpeg-turbo SIMD extensions.

### Complying with the libjpeg-turbo Licenses

This section provides a roll-up of the libjpeg-turbo licensing terms, to the best of our understanding.

If you are distributing a modified version of the libjpeg-turbo source, then:You cannot alter or remove any existing copyright or license notices from the source.OriginClause 1 of the IJG LicenseClause 1 of the Modified BSD LicenseClauses 1 and 3 of the zlib LicenseYou must add your own copyright notice to the header of each source file you modified, so
                        others can tell that you modified that file (if there is not an existing copyright header in
                        that file, then you can simply add a notice stating that you modified the file.)OriginClause 1 of the IJG LicenseClause 2 of the zlib LicenseYou must include the IJG README file, and you must not alter any of the copyright or license
                        text in that file.OriginClause 1 of the IJG License

If you are distributing a modified version of the libjpeg-turbo source, then:

You cannot alter or remove any existing copyright or license notices from the source.OriginClause 1 of the IJG LicenseClause 1 of the Modified BSD LicenseClauses 1 and 3 of the zlib License

You cannot alter or remove any existing copyright or license notices from the source.

Origin

Clause 1 of the IJG License

Clause 1 of the Modified BSD License

Clauses 1 and 3 of the zlib License

You must add your own copyright notice to the header of each source file you modified, so
                        others can tell that you modified that file (if there is not an existing copyright header in
                        that file, then you can simply add a notice stating that you modified the file.)OriginClause 1 of the IJG LicenseClause 2 of the zlib License

You must add your own copyright notice to the header of each source file you modified, so
                        others can tell that you modified that file (if there is not an existing copyright header in
                        that file, then you can simply add a notice stating that you modified the file.)

Origin

Clause 1 of the IJG License

Clause 2 of the zlib License

You must include the IJG README file, and you must not alter any of the copyright or license
                        text in that file.OriginClause 1 of the IJG License

You must include the IJG README file, and you must not alter any of the copyright or license
                        text in that file.

Origin

Clause 1 of the IJG License

If you are distributing only libjpeg-turbo binaries without the source, or if you are distributing
                an application that statically links with libjpeg-turbo, then:Your product documentation must include a message stating:This software is based in part on the work of the Independent JPEG Group.OriginClause 2 of the IJG licenseIf your binary distribution includes or uses the TurboJPEG API, then your product
                        documentation must include the text of the Modified BSD License (see below.)OriginClause 2 of the Modified BSD License

If you are distributing only libjpeg-turbo binaries without the source, or if you are distributing
                an application that statically links with libjpeg-turbo, then:

Your product documentation must include a message stating:This software is based in part on the work of the Independent JPEG Group.OriginClause 2 of the IJG license

Your product documentation must include a message stating:

This software is based in part on the work of the Independent JPEG Group.

Origin

Clause 2 of the IJG license

If your binary distribution includes or uses the TurboJPEG API, then your product
                        documentation must include the text of the Modified BSD License (see below.)OriginClause 2 of the Modified BSD License

If your binary distribution includes or uses the TurboJPEG API, then your product
                        documentation must include the text of the Modified BSD License (see below.)

Origin

Clause 2 of the Modified BSD License

You cannot use the name of the IJG or The libjpeg-turbo Project or the contributors thereof in
                advertising, publicity, etc.OriginIJG LicenseClause 3 of the Modified BSD License

You cannot use the name of the IJG or The libjpeg-turbo Project or the contributors thereof in
                advertising, publicity, etc.

Origin

IJG License

Clause 3 of the Modified BSD License

The IJG and The libjpeg-turbo Project do not warrant libjpeg-turbo to be free of defects, nor do we
                accept any liability for undesirable consequences resulting from your use of the software.OriginIJG LicenseModified BSD Licensezlib License

The IJG and The libjpeg-turbo Project do not warrant libjpeg-turbo to be free of defects, nor do we
                accept any liability for undesirable consequences resulting from your use of the software.

Origin

IJG License

Modified BSD License

zlib License

### The Modified (3-clause) BSD License

Copyright (C)2009-2019 D. R. Commander. All Rights Reserved. Copyright (C)2015 Viktor Szathmáry. All Rights
        Reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of the libjpeg-turbo Project nor the names of its contributors may be used to endorse
            or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS", AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

### Why Three Licenses?

The zlib License could have been used instead of the Modified (3-clause) BSD License, and since the IJG
        License effectively subsumes the distribution conditions of the zlib License, this would have effectively
        placed libjpeg-turbo binary distributions under the IJG License. However, the IJG License specifically refers
        to the Independent JPEG Group and does not extend attribution and endorsement protections to other entities.
        Thus, it was desirable to choose a license that granted us the same protections for new code that were granted
        to the IJG for code derived from their software.

### IJG JPEG Library License

In plain English:

We don't promise that this software works. (But if you find any bugs, please let us know!) 

You can use this software for whatever you want. You don't have to pay us.

You may not pretend that you wrote this software. If you use it in a program, you must acknowledge
            somewhere in your documentation that you've used the IJG code.

In legalese:

The authors make NO WARRANTY or representation, either express or implied, with respect to this software,
        its quality, accuracy, merchantability, or fitness for a particular purpose.  This software is provided "AS
        IS", and you, its user, assume the entire risk as to its quality and accuracy.

This software is copyright (C) 1991-1998, Thomas G. Lane. All Rights Reserved except as specified below.

Permission is hereby granted to use, copy, modify, and distribute this software (or portions thereof) for
        any purpose, without fee, subject to these conditions:

If any part of the source code for this software is distributed, then this README file must be
            included, with this copyright and no-warranty notice unaltered; and any additions, deletions, or changes to
            the original files must be clearly indicated in accompanying documentation.

If only executable code is distributed, then the accompanying documentation must state that "this
            software is based in part on the work of the Independent JPEG Group".

Permission for use of this software is granted only if the user accepts full responsibility for any
            undesirable consequences; the authors accept NO LIABILITY for damages of any kind.

These conditions apply to any software derived from or based on the IJG code, not just to the unmodified
        library.  If you use our work, you ought to acknowledge us.

Permission is NOT granted for the use of any IJG author's name or company name in advertising or publicity
        relating to this software or products derived from it.  This software may be referred to only as "the
        Independent JPEG Group's software".

We specifically permit and encourage the use of this software as the basis of commercial products, provided
        that all warranty or liability claims are assumed by the product vendor.

ansi2knr.c is included in this distribution by permission of L. Peter Deutsch, sole proprietor of its
        copyright holder, Aladdin Enterprises of Menlo Park, CA. ansi2knr.c is NOT covered by the above copyright and
        conditions, but instead by the usual distribution terms of the Free Software Foundation; principally, that you
        must include source code if you redistribute it.  (See the file ansi2knr.c for full details.)  However, since
        ansi2knr.c is not needed as part of any program generated from the IJG code, this does not limit you more than
        the foregoing paragraphs do.

The Unix configuration script "configure" was produced with GNU Autoconf. It is copyright by the Free
        Software Foundation but is freely distributable. The same holds for its supporting scripts (config.guess,
        config.sub, ltconfig, ltmain.sh).  Another support script, install-sh, is copyright by M.I.T. but is also
        freely distributable.

It appears that the arithmetic coding option of the JPEG spec is covered by patents owned by IBM, AT&T,
        and Mitsubishi.  Hence arithmetic coding cannot legally be used without obtaining one or more licenses.  For
        this reason, support for arithmetic coding has been removed from the free JPEG software. (Since arithmetic
        coding provides only a marginal gain over the unpatented Huffman mode, it is unlikely that very many
        implementations will support it.) So far as we are aware, there are no patent restrictions on the remaining
        code.

The IJG distribution formerly included code to read and write GIF files. To avoid entanglement with the
        Unisys LZW patent, GIF reading support has been removed altogether, and the GIF writer has been simplified to
        produce "uncompressed GIFs".  This technique does not use the LZW algorithm; the resulting GIF files are
        larger than usual, but are readable by all standard GIF decoders.

We are required to state that

### The zlib/libpng License (Zlib)

This software is provided 'as-is', without any express or implied warranty. In no event will the authors be
        held liable for any damages arising from the use of this software.

Permission is granted to anyone to use this software for any purpose, including commercial applications, and
        to alter it and redistribute it freely, subject to the following restrictions:

The origin of this software must not be misrepresented; you must not claim that you wrote the original
            software. If you use this software in a product, an acknowledgment in the product documentation would be
            appreciated but is not required.

Altered source versions must be plainly marked as such, and must not be misrepresented as being the
            original software.

This notice may not be removed or altered from any source distribution.

## liblangtag

The following software may be included in this product: liblangtag. Use of any of this software is governed
        by the terms of the license below:

Jump to LGPL Version 3

Jump to MPL Version 2.0

## libmspub

The following software may be included in this product: libmspub. Use of any of this software is governed by
        the terms of the license below:

MPL 1.1 / LGPL v2+ / GPL v2+

Jump to GPL Version 2

Jump to LGPL Version 2

Jump to MPL Version 1.1

## libmwaw

The following software may be included in this product: libmwaw. Use of any of this software is governed by
        the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libnumbertext

The following software may be included in this product: libnumbertext. Use of any of this software is
        governed by the terms of the license below:

Copyright 2009–2019 László Németh et al.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of the copyright holder nor the names of its contributors may be used to endorse or
            promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

## libodfgen

The following software may be included in this product: libodfgen. Use of any of this software is governed
        by the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libpng

The following software may be included in this product: libpng. Use of any of this software is governed by
        the terms of the license below:

This copy of the libpng notices is provided for your convenience.  In case of
        any discrepancy between this copy and the notices in the file png.h that is
        included in the libpng distribution, the latter shall prevail.
        COPYRIGHT NOTICE, DISCLAIMER, and LICENSE:
        If you modify libpng you may insert additional notices immediately following
        this sentence.
        This code is released under the libpng license.
        libpng versions 1.2.6, August 15, 2004, through 1.5.1, February 3, 2011, are
        Copyright (c) 2004, 2006-2011 Glenn Randers-Pehrson, and are
        distributed according to the same disclaimer and license as libpng-1.2.5
        with the following individual added to the list of Contributing Authors
          Cosmin Truta
        libpng versions 1.0.7, July 1, 2000, through 1.2.5 - October 3, 2002, are
        Copyright (c) 2000-2002 Glenn Randers-Pehrson, and are
        distributed according to the same disclaimer and license as libpng-1.0.6
        with the following individuals added to the list of Contributing Authors
          Simon-Pierre Cadieux
          Eric S. Raymond
          Gilles Vollant
        and with the following additions to the disclaimer:
          There is no warranty against interference with your enjoyment of the
          library or against infringement.  There is no warranty that our
          efforts or the library will fulfill any of your particular purposes
          or needs.  This library is provided with all faults, and the entire
          risk of satisfactory quality, performance, accuracy, and effort is with
          the user.
        libpng versions 0.97, January 1998, through 1.0.6, March 20, 2000, are
        Copyright (c) 1998, 1999 Glenn Randers-Pehrson, and are
        distributed according to the same disclaimer and license as libpng-0.96,
        with the following individuals added to the list of Contributing Authors: <AUTHORS>
          Glenn Randers-Pehrson
          Willem van Schaik
        libpng versions 0.89, June 1996, through 0.96, May 1997, are
        Copyright (c) 1996, 1997 Andreas Dilger
        Distributed according to the same disclaimer and license as libpng-0.88,
        with the following individuals added to the list of Contributing Authors: <AUTHORS>
          Kevin Bracey
          Sam Bushell
          Magnus Holmgren
          Greg Roelofs
          Tom Tanner
        libpng versions 0.5, May 1995, through 0.88, January 1996, are
        Copyright (c) 1995, 1996 Guy Eric Schalnat, Group 42, Inc.
        For the purposes of this copyright and license, "Contributing Authors"
        is defined as the following set of individuals:
          Andreas Dilger
          Dave Martindale
          Guy Eric Schalnat
          Paul Schmidt
          Tim Wegner
        The PNG Reference Library is supplied "AS IS".  The Contributing Authors
        and Group 42, Inc. disclaim all warranties, expressed or implied,
        including, without limitation, the warranties of merchantability and of
        fitness for any purpose.  The Contributing Authors and Group 42, Inc.
        assume no liability for direct, indirect, incidental, special, exemplary,
        or consequential damages, which may result from the use of the PNG
        Reference Library, even if advised of the possibility of such damage.
        Permission is hereby granted to use, copy, modify, and distribute this
        source code, or portions hereof, for any purpose, without fee, subject
        to the following restrictions:
        1. The origin of this source code must not be misrepresented.
        2. Altered versions must be plainly marked as such and must not
          be misrepresented as being the original source.
        3. This Copyright notice may not be removed or altered from any
          source or altered source distribution.
        The Contributing Authors and Group 42, Inc. specifically permit, without
        fee, and encourage the use of this source code as a component to
        supporting the PNG file format in commercial products.  If you use this
        source code in a product, acknowledgment is not required but would be
        appreciated.
        A "png_get_copyright" function is available, for convenient use in "about"
        boxes and the like:
          printf("%s",png_get_copyright(NULL));
        Also, the PNG logo (in PNG format, of course) is supplied in the
        files "pngbar.png" and "pngbar.jpg (88x31) and "pngnow.png" (98x31).
        Libpng is OSI Certified Open Source Software.  OSI Certified Open Source is a
        certification mark of the Open Source Initiative.
        Glenn Randers-Pehrson
        glennrp at users.sourceforge.net
        February 3, 2011  

## libpagemaker

The following software may be included in this product: libpagemaker. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## libqxp

The following software may be included in this product: libqxp. Use of any of this software is governed
        by the terms of the license below:

Jump to MPL Version 2.0

## librevenge

The following software may be included in this product: librevenge. Use of any of this software is governed
        by the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libstaroffice

The following software may be included in this product: libstaroffice. Use of any of this software
        is governed by the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## LibTomMath

The following software may be included in this product: LibTomMath. LibTomMath is public domain.

## libvisio

The following software may be included in this product: libvisio. Use of any of this software is governed by
        the terms of the license below:

Jump to MPL Version 2.0

## libwpd

The following software may be included in this product: libwpd. Use of any of this software is governed by
        the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libwpg

The following software may be included in this product: libwpg. Use of any of this software is governed by
        the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libwps

The following software may be included in this product: libwps. Use of any of this software is governed by
        the terms of the license below:

LGPL v2.1+ / MPL 2

Jump to LGPL Version 2.1

Jump to MPL Version 2.0

## libxml2

The following software may be included in this product: libxml2. Use of any of this software is governed by
        the terms of the license below:

Except where otherwise noted in the source code (e.g. the files hash.c, list.c and the trio files, which are
        covered by a similar license but with different Copyright notices) all the files are:

Copyright (C) 1998-2003 Daniel Veillard.  All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT- NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO
        EVENT SHALL THE DANIEL VEILLARD BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON- NECTION WITH THE SOFTWARE OR THE USE OR OTHER
        DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Daniel Veillard shall not be used in advertising or
        otherwise to promote the sale, use or other dealings in this Software without prior written authorization from
        him.

## libxslt

The following software may be included in this product: libxslt. Use of any of this software is governed by
        the terms of the license below:

### License for libxslt except libexslt

Copyright (C) 2001-2002 Daniel Veillard.  All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT- NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO
        EVENT SHALL THE DANIEL VEILLARD BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON- NECTION WITH THE SOFTWARE OR THE USE OR OTHER
        DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Daniel Veillard shall not be used in advertising or
        otherwise to promote the sale, use or other dealings in this Software without prior written authorization from
        him.

### License for libexslt

Copyright (C) 2001-2002 Thomas Broyer, Charlie Bozeman and Daniel Veillard. All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT- NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO
        EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
        TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON- NECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
        THE SOFTWARE.

Except as contained in this notice, the name of the authors shall not be used in advertising or otherwise to
        promote the sale, use or other dealings in this Software without prior written authorization from him.

## libzmf

The following software may be included in this product: libzmf. Use of any of this software is governed by
        the terms of the license below:

Jump to MPL Version 2.0

## Little CMS (lcms2)

The following software may be included in this product: Little CMS (lcms2). Use of any of this software is
        governed by the terms of the license below:

Copyright (c) 1998-2011 Marti Maria Saguer

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
        SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
        OTHER DEALINGS IN THE SOFTWARE.

## lpsolve

The following software may be included in this product: lpsolve. Use of any of this software is governed by
        the terms of the license below:

Jump to LGPL Version 2.1

## MariaDB Connector/C

The following software may be included in this product: MariaDB Connector/C. Use of any of this software is
        governed by the terms of the license below:

Jump to LGPL Version 2.1

## mdds

The following software may be included in this product: mdds. Use of any of this software is governed by the
        terms of the license below:

Copyright (c) 2010 Kohei Yoshida

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
        SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
        OTHER DEALINGS IN THE SOFTWARE.

## mDNSResponder

The following software may be included in this product: mDNSResponder. The majority of the source code in
        the mDNSResponder project is licensed under the terms of the Apache License, Version 2.0.

Jump to Apache License Version 2.0

To accommodate license compatibility with the widest possible range of client code licenses, the shared
        library code, which is linked at runtime into the same address space as the client using it, is licensed under
        the terms of the "Three-Clause BSD License".

The Linux Name Service Switch code, contributed by National ICT Australia Ltd (NICTA) is licensed under the
        terms of the NICTA Public Software Licence (which is substantially similar to the "Three-Clause BSD License",
        with some additional language pertaining to Australian law).

## Microsoft Visual C++ 2015 Runtime Libraries

The following software may be included in this product: Microsoft Visual C++ 2015 Runtime Libraries. Use of
        any of this software is governed by the terms of the license below:

MICROSOFT SOFTWARE LICENSE TERMS

MICROSOFT VISUAL STUDIO 2015 ADD-ONs, VISUAL STUDIO SHELLS and C++ REDISTRIBUTABLE

These license terms are an agreement between Microsoft Corporation (or based on where you live, one of its
        affiliates) and you. They apply to the software named above. The terms also apply to any Microsoft services or
        updates for the software, except to the extent those have different terms.

IF YOU COMPLY WITH THESE LICENSE TERMS, YOU HAVE THE RIGHTS BELOW.

INSTALLATION AND USE RIGHTS.

You may install and use any number of copies of the software.

Backup copy. You may make one backup copy of the software, for reinstalling the software.

TERMS FOR SPECIFIC COMPONENTS.

Utilities. The software may contain some items on the Utilities List at <http://go.microsoft.com/fwlink/?LinkID=523763&clcid=0x409>.
                You may copy and install those items, if included with the software, on your machines or third party
                machines, to debug and deploy your applications and databases you develop with the software. Please
                note that Utilities are designed for temporary use, that Microsoft may not be able to patch or update
                Utilities separately from the rest of the software, and that some Utilities by their nature may make it
                possible for others to access machines on which they are installed. As a result, you should delete all
                Utilities you have installed after you finish debugging or deploying your applications and databases.
                Microsoft is not responsible for any third party use or access of Utilities you install on any
                machine.

Microsoft Platforms. The software may include components from Microsoft Windows; Microsoft Windows
                Server; Microsoft SQL Server; Microsoft Exchange; Microsoft Office; and Microsoft SharePoint. These
                components are governed by separate agreements and their own product support policies, as described in
                the license terms found in the installation directory for that component or in the “Licenses”
                folder accompanying the software.

Third Party Components. The software may include third party components with separate legal notices
                or governed by other agreements, as may be described in the ThirdPartyNotices file accompanying the
                software. Even if such components are governed by other agreements, the disclaimers and the limitations
                on and exclusions of damages below also apply.

DATA. The software may collect information about you and your use of the software, and send that to
            Microsoft. Microsoft may use this information to provide services and improve our products and services.
            You may opt-out of many of these scenarios, but not all, as described in the product documentation. There
            are also some features in the software that may enable you to collect data from users of your applications.
            If you use these features to enable data collection in your applications, you must comply with applicable
            law, including providing appropriate notices to users of your applications. You can learn more about data
            collection and use in the help documentation and the privacy statement at <http://go.microsoft.com/fwlink/?LinkID=528096&clcid=0x409>.
            Your use of the software operates as your consent to these practices.

SCOPE OF LICENSE. The software is licensed, not sold. This agreement only gives you some rights to use
            the software. Microsoft reserves all other rights. Unless applicable law gives you more rights despite this
            limitation, you may use the software only as expressly permitted in this agreement. In doing so, you must
            comply with any technical limitations in the software that only allow you to use it in certain ways. You
            may not

work around any technical limitations in the software;

reverse engineer, decompile or disassemble the software, except and only to the extent that
                applicable law expressly permits, despite this limitation;

remove, minimize, block or modify any notices of Microsoft or its suppliers in the software;

use the software in any way that is against the law; or

share, publish or lend the software, or provide the software as a stand-alone hosted as solution
                for others to use, or transfer the software or this agreement to any third party.

EXPORT RESTRICTIONS. Microsoft software, online services, professional services and related technology
            are subject to U.S. export jurisdiction. You must comply with all applicable international and national
            laws, including the U.S. Export Administration Regulations, the International Traffic in Arms Regulations,
            Office of Foreign Assets Control sanctions programs, and end-user, end use and destination restrictions by
            the U.S. and other governments related to Microsoft products, services and technologies. For additional
            information, see www.microsoft.com/exporting <http://www.microsoft.com/exporting>.

SUPPORT SERVICES. Because this software is “as is,” we may not provide support services for
            it.

ENTIRE AGREEMENT. This agreement, and the terms for supplements, updates, Internet-based services and
            support services that you use, are the entire agreement for the software and support services.

APPLICABLE LAW. If you acquired the software in the United States, Washington law applies to
            interpretation of and claims for breach of this agreement, and the laws of the state where you live apply
            to all other claims. If you acquired the software in any other country, its laws apply.

LEGAL EFFECT. This agreement describes certain legal rights. You may have other rights under the laws
            of your state or country. This agreement does not change your rights under the laws of your state or
            country if the laws of your state or country do not permit it to do so. Without limitation of the
            foregoing, for Australia, YOU HAVE STATUTORY GUARANTEES UNDER THE AUSTRALIAN CONSUMER LAW AND NOTHING IN
            THESE TERMS IS INTENDED TO AFFECT THOSE RIGHTS

DISCLAIMER OF WARRANTY. THE SOFTWARE IS LICENSED “AS-IS.” YOU BEAR THE RISK OF USING IT. MICROSOFT
            GIVES NO EXPRESS WARRANTIES, GUARANTEES OR CONDITIONS. TO THE EXTENT PERMITTED UNDER YOUR LOCAL LAWS,
            MICROSOFT EXCLUDES THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
            NON-INFRINGEMENT.

LIMITATION ON AND EXCLUSION OF DAMAGES. YOU CAN RECOVER FROM MICROSOFT AND ITS SUPPLIERS ONLY DIRECT
            DAMAGES UP TO U.S. $5.00. YOU CANNOT RECOVER ANY OTHER DAMAGES, INCLUDING CONSEQUENTIAL, LOST PROFITS,
            SPECIAL, INDIRECT OR INCIDENTAL DAMAGES.
            This limitation applies to (a) anything related to the software, services, content (including code) on
            third party Internet sites, or third party applications; and (b) claims for breach of contract, breach of
            warranty, guarantee or condition, strict liability, negligence, or other tort to the extent permitted by
            applicable law.
            It also applies even if Microsoft knew or should have known about the possibility of the damages. The above
            limitation or exclusion may not apply to you because your country may not allow the exclusion or limitation
            of incidental, consequential or other damages.

EULAID: VS2015_RTM_ShellsRedist_ENU

## MyThes

The following software may be included in this product: MyThes. Use of any of this software is governed by
        the terms of the license below:

Copyright 2003 Kevin B. Hendricks, Stratford, Ontario, Canada And Contributors. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

1. Redistribution of source code must retain the above copyright notice, this list of conditions and the
        following disclaimer.

2. Redistribution in binary form must reproduce the above copyright notice, this list of conditions and the
        following disclaimer in the documentation and/or other materials provided with the distribution.

3. All modifications to the source code must be clearly marked as such. Binary redistribution based on
        modified source code must be clearly marked as modified versions in the documentation and/or other materials
        provided with the distribution.

THIS SOFTWARE IS PROVIDED BY KEVIN B. HENDRICKS AND CONTRIBUTORS ``AS IS'' AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL KEVIN B. HENDRICKS OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

## neon

The following software may be included in this product: neon. Use of any of this software is governed by the
        terms of the license below:

neon is Copyright (C) 1999-2007 Joe Orton <<EMAIL>>
        Portions are:
        Copyright (C) 1999-2000 Tommi Komulainen <<EMAIL>>
        Copyright (C) 1999-2000 Peter Boos <<EMAIL>>
        Copyright (C) 1991, 1995, 1996, 1997 Free Software Foundation, Inc.
        Copyright (C) 2004 Aleix Conchillo Flaque <<EMAIL>>
        Copyright (C) 2004 Jiang Lei <<EMAIL>>
        Copyright (C) 2004-2005 Vladimir Berezniker @ http://public.xdi.org/=vmpn

Jump to LGPL Version 2.1

## Network Security Services (NSS)

The following software may be included in this product: Network Security Services (NSS). Use of any of this
        software is governed by the terms of the license below:

Jump to MPL 1.1

## OpenSSL

The following software may be included in this product: OpenSSL. Use of any of this software is governed by
        the terms of the license below:

The OpenSSL toolkit stays under a dual license, i.e. both the conditions of the OpenSSL License and the
        original SSLeay license apply to the toolkit.

See below for the actual license texts. Actually both licenses are BSD-style Open Source licenses. In case
        of any license issues related to OpenSSL <NAME_EMAIL>.

### OpenSSL License

Copyright (c) 1998-2007 The OpenSSL Project. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistribution of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution. 

All advertising materials mentioning features or use of this software must display the following
            acknowledgment: "This product includes software developed by the OpenSSL Project for use in the OpenSSL
            Toolkit. (http://www.openssl.org/)" 

The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to endorse or promote products
            derived from this software without prior written permission. For written permission, please contact
            <EMAIL>. 

Products derived from this software may not be called "OpenSSL" nor may "OpenSSL" appear in their names
            without prior written permission of the OpenSSL Project. 

Redistribution of any form whatsoever must retain the following acknowledgment: "This product includes
            software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/)" 

THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
        INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
        ARE DISCLAIMED. IN NO EVENT SHALL THE OpenSSL PROJECT OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
        INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
        SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
        ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
        ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This product includes cryptographic software written by Eric Young (<EMAIL>). This product
        includes software written by Tim Hudson (<EMAIL>).

### Original SSLeay License

Copyright (C) 1995-1998 Eric Young (<EMAIL>) All rights reserved.

This package is an SSL implementation written by Eric Young (<EMAIL>).
        The implementation was written so as to conform with Netscapes SSL.

This library is free for commercial and non-commercial use as long as the following conditions are aheared
        to. The following conditions apply to all code found in this distribution, be it the RC4, RSA, lhash, DES,
        etc., code; not just the SSL code. The SSL documentation included with this distribution is covered by the same
        copyright terms except that the holder is Tim Hudson (<EMAIL>).

Copyright remains Eric Young's, and as such any Copyright notices in the code are not to be removed. If this
        package is used in a product, Eric Young should be given attribution as the author of the parts of the library
        used. This can be in the form of a textual message at program startup or in documentation (online or textual)
        provided with the package.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistribution of source code must retain the copyright notice, this list of conditions and the
            following disclaimer.

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution. 

All advertising materials mentioning features or use of this software must display the following
            acknowledgment: "This product includes cryptographic software written by Eric Young (<EMAIL>)"
            The word 'cryptographic' can be left out if the routines from the library being used are not cryptographic
            related :-). 

If you include any Windows specific code (or a derivative thereof) from the apps directory (application
            code) you must include an acknowledgment: "This product includes software written by Tim Hudson
            (<EMAIL>)" 

THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
        LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN
        NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
        OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
        USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
        CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
        THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

The license and distribution terms for any publicly available version or derivative of this code cannot be
        changed. i.e. this code cannot simply be copied and put under another distribution license [including the GNU
        Public License.]

## PDFium

The following software may be included in this product: PDFium. Use of any of this software is governed by
        the terms of the license below:

Copyright 2014 PDFium Authors. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of Google Inc. nor the names of its contributors may be used to endorse or promote
            products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

## Pentaho Reporting Flow Engine

The following software may be included in this product: Pentaho Reporting Flow Engine (including core,
        flow-engine, libbase, libfonts, libformula, liblayout, libloader, librepository, libserializer, and libxml).
        Use of any of this software is governed by the terms of the license below:

Jump to LGPL Version 2.1

## poppler

The following software may be included in this product: poppler. Use of any of this software is governed by
        the terms of the license below:

Jump to GPL Version 2

## PostgreSQL

The following software may be included in this product: PostgreSQL. Use of any of this software is governed
        by the terms of the license below:

PostgreSQL is released under the PostgreSQL License, a liberal Open Source license, similar to the BSD or
        MIT licenses.

PostgreSQL Database Management System
        (formerly known as Postgres, then as Postgres95)

Portions Copyright (c) 1996-2012, The PostgreSQL Global Development Group

Portions Copyright (c) 1994, The Regents of the University of California

Permission to use, copy, modify, and distribute this software and its documentation for any purpose, without
        fee, and without a written agreement is hereby granted, provided that the above copyright notice and this
        paragraph and the following two paragraphs appear in all copies.

IN NO EVENT SHALL THE UNIVERSITY OF CALIFORNIA BE LIABLE TO ANY PARTY FOR DIRECT, INDIRECT, SPECIAL,
        INCIDENTAL, OR CONSEQUENTIAL DAMAGES, INCLUDING LOST PROFITS, ARISING OUT OF THE USE OF THIS SOFTWARE AND ITS
        DOCUMENTATION, EVEN IF THE UNIVERSITY OF CALIFORNIA HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

THE UNIVERSITY OF CALIFORNIA SPECIFICALLY DISCLAIMS ANY WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE SOFTWARE PROVIDED HEREUNDER IS
        ON AN "AS IS" BASIS, AND THE UNIVERSITY OF CALIFORNIA HAS NO OBLIGATIONS TO PROVIDE MAINTENANCE, SUPPORT,
        UPDATES, ENHANCEMENTS, OR MODIFICATIONS.

## libffi

The following software may be included in this product: libffi

The libffi project code is covered by the MIT license:

libffi - Copyright (c) 1996-2019  Anthony Green, Red Hat, Inc and others.

See source files for details.

Permission is hereby granted, free of charge, to any person obtaining
        a copy of this software and associated documentation files (the
        ``Software''), to deal in the Software without restriction, including
        without limitation the rights to use, copy, modify, merge, publish,
        distribute, sublicense, and/or sell copies of the Software, and to
        permit persons to whom the Software is furnished to do so, subject to
        the following conditions:

The above copyright notice and this permission notice shall be
        included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED ``AS IS'', WITHOUT WARRANTY OF ANY KIND,
        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
        IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
        CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
        TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
        SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

## Python

The following software may be included in this product: Python. Use of any of this software is governed by
        the terms of the license below:

### PSF LICENSE AGREEMENT FOR PYTHON 2.3

1. This LICENSE AGREEMENT is between the Python Software Foundation ("PSF"), and the Individual or
        Organization ("Licensee") accessing and otherwise using Python 2.3 software in source or binary form and its
        associated documentation.

2. Subject to the terms and conditions of this License Agreement, PSF hereby grants Licensee a nonexclusive,
        royalty-free, world-wide license to reproduce, analyze, test, perform and/or display publicly, prepare
        derivative works, distribute, and otherwise use Python 2.3 alone or in any derivative version, provided,
        however, that PSF's License Agreement and PSF's notice of copyright, i.e., "Copyright (c) 2001, 2002, 2003,
        2004 Python Software Foundation; All Rights Reserved" are retained in Python 2.3 alone or in any derivative
        version prepared by Licensee.

3. In the event Licensee prepares a derivative work that is based on or incorporates Python 2.3 or any part
        thereof, and wants to make the derivative work available to others as provided herein, then Licensee hereby
        agrees to include in any such work a brief summary of the changes made to Python 2.3.

4. PSF is making Python 2.3 available to Licensee on an "AS IS" basis.  PSF MAKES NO REPRESENTATIONS OR
        WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND DISCLAIMS ANY
        REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON
        2.3 WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON 2.3 FOR ANY INCIDENTAL, SPECIAL, OR
        CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON 2.3, OR ANY
        DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

6. This License Agreement will automatically terminate upon a material breach of its terms and
        conditions.

7. Nothing in this License Agreement shall be deemed to create any relationship of agency, partnership, or
        joint venture between PSF and Licensee.  This License Agreement does not grant permission to use PSF
        trademarks or trade name in a trademark sense to endorse or promote products or services of Licensee, or any
        third party.

8. By copying, installing or otherwise using Python 2.3, Licensee agrees to be bound by the terms and
        conditions of this License Agreement.

BEOPEN.COM LICENSE AGREEMENT FOR PYTHON 2.0

BEOPEN PYTHON OPEN SOURCE LICENSE AGREEMENT VERSION 1

1. This LICENSE AGREEMENT is between BeOpen.com ("BeOpen"), having an office at 160 Saratoga Avenue, Santa
        Clara, CA 95051, and the Individual or Organization ("Licensee") accessing and otherwise using this software in
        source or binary form and its associated documentation ("the Software").

2. Subject to the terms and conditions of this BeOpen Python License Agreement, BeOpen hereby grants
        Licensee a non-exclusive, royalty-free, world-wide license to reproduce, analyze, test, perform and/or display
        publicly, prepare derivative works, distribute, and otherwise use the Software alone or in any derivative
        version, provided, however, that the BeOpen Python License is retained in the Software, alone or in any
        derivative version prepared by Licensee.

3. BeOpen is making the Software available to Licensee on an "AS IS" basis.  BEOPEN MAKES NO
        REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, BEOPEN MAKES NO AND
        DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE
        USE OF THE SOFTWARE WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

4. BEOPEN SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF THE SOFTWARE FOR ANY INCIDENTAL, SPECIAL, OR
        CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THE SOFTWARE, OR ANY DERIVATIVE
        THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

5. This License Agreement will automatically terminate upon a material breach of its terms and
        conditions.

6. This License Agreement shall be governed by and interpreted in all respects by the law of the State of
        California, excluding conflict of law provisions.  Nothing in this License Agreement shall be deemed to create
        any relationship of agency, partnership, or joint venture between BeOpen and Licensee.  This License Agreement
        does not grant permission to use BeOpen trademarks or trade names in a trademark sense to endorse or promote
        products or services of Licensee, or any third party.  As an exception, the "BeOpen Python" logos available at
        http://www.pythonlabs.com/logos.html may be used according
        to the permissions granted on that web page.

7. By copying, installing or otherwise using the software, Licensee agrees to be bound by the terms and
        conditions of this License Agreement.

### CNRI LICENSE AGREEMENT FOR PYTHON 1.6.1

1. This LICENSE AGREEMENT is between the Corporation for National Research Initiatives, having an office at
        1895 Preston White Drive, Reston, VA 20191 ("CNRI"), and the Individual or Organization ("Licensee") accessing
        and otherwise using Python 1.6.1 software in source or binary form and its associated documentation.

2. Subject to the terms and conditions of this License Agreement, CNRI hereby grants Licensee a
        nonexclusive, royalty-free, world-wide license to reproduce, analyze, test, perform and/or display publicly,
        prepare derivative works, distribute, and otherwise use Python 1.6.1 alone or in any derivative version,
        provided, however, that CNRI's License Agreement and CNRI's notice of copyright, i.e., "Copyright (c) 1995-2001
        Corporation for National Research Initiatives; All Rights Reserved" are retained in Python 1.6.1 alone or in
        any derivative version prepared by Licensee.  Alternately, in lieu of CNRI's License Agreement, Licensee may
        substitute the following text (omitting the quotes): "Python 1.6.1 is made available subject to the terms and
        conditions in CNRI's License Agreement.  This Agreement together with Python 1.6.1 may be located on the
        Internet using the following unique, persistent identifier (known as a handle): 1895.22/1013.  This Agreement
        may also be obtained from a proxy server on the Internet using the following URL: http://hdl.handle.net/1895.22/1013".

3. In the event Licensee prepares a derivative work that is based on or incorporates Python 1.6.1 or any
        part thereof, and wants to make the derivative work available to others as provided herein, then Licensee
        hereby agrees to include in any such work a brief summary of the changes made to Python 1.6.1.

4. CNRI is making Python 1.6.1 available to Licensee on an "AS IS" basis.  CNRI MAKES NO REPRESENTATIONS OR
        WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, CNRI MAKES NO AND DISCLAIMS ANY
        REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON
        1.6.1 WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

5. CNRI SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON 1.6.1 FOR ANY INCIDENTAL, SPECIAL, OR
        CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON 1.6.1, OR ANY
        DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

6. This License Agreement will automatically terminate upon a material breach of its terms and
        conditions.

7. This License Agreement shall be governed by the federal intellectual property law of the United States,
        including without limitation the federal copyright law, and, to the extent such U.S. federal law does not
        apply, by the law of the Commonwealth of Virginia, excluding Virginia's conflict of law provisions.
        Notwithstanding the foregoing, with regard to derivative works based on Python 1.6.1 that incorporate
        non-separable material that was previously distributed under the GNU General Public License (GPL), the law of
        the Commonwealth of Virginia shall govern this License Agreement only as to issues arising under or with
        respect to Paragraphs 4, 5, and 7 of this License Agreement.  Nothing in this License Agreement shall be
        deemed to create any relationship of agency, partnership, or joint venture between CNRI and Licensee.  This
        License Agreement does not grant permission to use CNRI trademarks or trade name in a trademark sense to
        endorse or promote products or services of Licensee, or any third party.

8. By clicking on the "ACCEPT" button where indicated, or by copying, installing or otherwise using Python
        1.6.1, Licensee agrees to be bound by the terms and conditions of this License Agreement.

        ACCEPT

### CWI LICENSE AGREEMENT FOR PYTHON 0.9.0 THROUGH 1.2

Copyright (c) 1991 - 1995, Stichting Mathematisch Centrum Amsterdam, The Netherlands.  All rights
        reserved.

Permission to use, copy, modify, and distribute this software and its documentation for any purpose and
        without fee is hereby granted, provided that the above copyright notice appear in all copies and that both that
        copyright notice and this permission notice appear in supporting documentation, and that the name of Stichting
        Mathematisch Centrum or CWI not be used in advertising or publicity pertaining to distribution of the software
        without specific, written prior permission.

STICHTING MATHEMATISCH CENTRUM DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING ALL IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO EVENT SHALL STICHTING MATHEMATISCH CENTRUM BE LIABLE FOR ANY
        SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR
        PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION
        WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

## QR-Code-Generator

The following software may be included in this product: QR Code Generator.

The libqrcodegen project code is covered by the MIT license:

 Copyright © 2019 Project Nayuki. (MIT License) [https://www.nayuki.io/page/qr-code-generator-library]

Permission is hereby granted, free of charge, to any person obtaining a copy of
    this software and associated documentation files (the "Software"), to deal in
    the Software without restriction, including without limitation the rights to
    use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
    the Software, and to permit persons to whom the Software is furnished to do so,
    subject to the following conditions:

*The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

*The Software is provided "as is", without warranty of any kind, express or
    implied, including but not limited to the warranties of merchantability,
    fitness for a particular purpose and noninfringement. In no event shall the
    authors or copyright holders be liable for any claim, damages or other
    liability, whether in an action of contract, tort or otherwise, arising from,
    out of or in connection with the Software or the use or other dealings in the
    Software.

## dtoa

The following software may be included in this product: dtoa.

dtoa code is covered by the MIT license:

 The author of this software is David M. Gay.

 Copyright (c) 1991, 2000, 2001 by Lucent Technologies.

Permission to use, copy, modify, and distribute this software for any
    purpose without fee is hereby granted, provided that this entire notice
    is included in all copies of any software which is or includes a copy
    or modification of this software and in all copies of the supporting
    documentation for such software.

THIS SOFTWARE IS BEING PROVIDED "AS IS", WITHOUT ANY EXPRESS OR IMPLIED
    WARRANTY.  IN PARTICULAR, NEITHER THE AUTHOR NOR LUCENT MAKES ANY
    REPRESENTATION OR WARRANTY OF ANY KIND CONCERNING THE MERCHANTABILITY
    OF THIS SOFTWARE OR ITS FITNESS FOR ANY PARTICULAR PURPOSE.

## Random123: a Library of Counter-Based Random Number Generators

The following software may be included in this product: Random123: a Library of Counter-Based Random Number
    Generators. Use of any of this software is governed by the terms of the license below:

Copyright 2010-2011, D. E. Shaw Research.

All rights reserved.

Redistribution and use in source and binary forms, with or without

modification, are permitted provided that the following conditions are

met:

* Redistributions of source code must retain the above copyright notice, this list of conditions, and the
    following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice, this list of conditions, and the
    following disclaimer in the documentation and/or other materials provided with the distribution.

* Neither the name of D. E. Shaw Research nor the names of its contributors may be used to endorse or promote
    products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
    WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
    GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

## Raptor RDF Parser Library

The following software may be included in this product: Raptor RDF Parser Library. Use of any of this software
    is governed by the terms of the license below:

Copyright (C) 2000-2008 David Beckett
    Copyright (C) 2000-2005 University of Bristol. All Rights Reserved.

All the licenses below are alternatives and if you select one license, that one alone applies.

Jump to LGPL Version 2.1

Jump to Apache License Version 2.0

## Rasqal RDF Query Library

The following software may be included in this product: Rasqal RDF Query Library. Use of any of this software is
    governed by the terms of the license below:

Copyright (C) 2000-2008 David Beckett
    Copyright (C) 2000-2005 University of Bristol. All Rights Reserved.

All the licenses below are alternatives and if you select one license, that one alone applies.

Jump to LGPL Version 2.1

Jump to Apache License Version 2.0

## Redland RDF Application Framework

The following software may be included in this product: Redland RDF Application Framework. Use of any of
        this software is governed by the terms of the license below:

Copyright (C) 2000-2008 David Beckett
        Copyright (C) 2000-2005 University of Bristol. All Rights Reserved.

All the licenses below are alternatives and if you select one license, that one alone applies.

Jump to LGPL Version 2.1

Jump to Apache License Version 2.0

## Rhino

The following software may be included in this product: Rhino. Use of any of this software is governed by
        the terms of the license below:

Jump to MPL Version 1.1

## SANE

The following software may be included in this product: SANE. Use of any of this software is governed by the
        terms of the license below:

sane - Scanner Access Now Easy. Copyright (C) 1997-1999 David Mosberger-Tang and Andreas Beck

This file is part of the SANE package.

This file is in the public domain. You may use and modify it as you see fit, as long as this copyright message
        is included and that there is an indication as to what modifications have been made (if any).

SANE is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied
        warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

This file declares SANE application interface. See the SANE standard for a detailed explanation of the
        interface.

## SVGPathSeg polyfill

The following software may be included in this product: SVGPathSeg polyfill. Use of any of this software is
    governed by the terms of the license below:

Copyright 2015 The Chromium Authors. All rights reserved.

The Chromium Authors can be found at https://src.chromium.org/viewvc/chrome/trunk/src/AUTHORS

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
    following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
        following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the
        following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of Google Inc. nor the names of its contributors may be used to endorse or promote
        products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS" AND ANY EXPRESS OR IMPLIED
    WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
    GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

## TWAIN

The following software may be included in this product: TWAIN. Use of any of this software is governed by the
        terms of the license below:

### The TWAIN License

The TWAIN Working Group grants customer ("Customer") the worldwide, royalty-free, non-exclusive license to
        reproduce and distribute the software and documentation of the TWAIN toolkit ("TWAIN Toolkit"). The TWAIN Toolkit
        was designed to be used by third parties to assist them in becoming compliant with the TWAIN standard, but it has
        not been developed to the standards of a commercial product. Consequently, the TWAIN toolkit is provided AS IS
        without any warranty. THE TWAIN Working Group disclaims all warranties in the TWAIN toolkit whether implied,
        express or statutory, including, without limitation, the implied warranties of merchantability, non infringement of
        third party rights and fitness for a particular purpose. The TWAIN Working Group disclaims all liability for
        damages, whether direct, indirect, special, incidental, or consequential, arising from the reproduction,
        distribution, modification, or other use of the TWAIN Toolkit.

As a condition of this license, Customer agrees to include in software programs based in whole or in part on the
        TWAIN Toolkit the following provisions in (i) the header or similar file in such software and (ii) prominently in
        its documentation and to require its sublicensees to include these provisions in similar locations: The TWAIN
        Toolkit is distributed as is. The developer and distributors of the TWAIN Toolkit expressly disclaim all implied,
        express or statutory warranties including, without limitation, the implied warranties of merchantability, non
        infringement of third party rights and fitness for a particular purpose. Neither the developers nor the
        distributors will be liable for damages, whether direct, indirect, special, incidental, or consequential, as a
        result of the reproduction, modification, distribution or other use of the TWAIN Toolkit.

## Unicode CLDR data repository

The following software may be included in this product: Unicode's CLDR data repository. Use of any of this
        software is governed by the terms of the license below:

Copyright 1991-2005 Unicode, Inc. All rights reserved. Distributed under the Terms of Use in http://www.unicode.org/copyright.html.

Permission is hereby granted, free of charge, to any person obtaining a copy of the Unicode data files and any
        associated documentation (the "Data Files") or Unicode software and any associated documentation (the "Software")
        to deal in the Data Files or Software without restriction, including without limitation the rights to use, copy,
        modify, merge, publish, distribute, and/or sell copies of the Data Files or Software, and to permit persons to whom
        the Data Files or Software are furnished to do so, provided that (a) the above copyright notice(s) and this
        permission notice appear with all copies of the Data Files or Software, (b) both the above copyright notice(s) and
        this permission notice appear in associated documentation, and (c) there is clear notice in each modified Data File
        or in the Software as well as in the documentation associated with the Data File(s) or Software that the data or
        software has been modified.

THE DATA FILES AND SOFTWARE ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
        BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD
        PARTY RIGHTS. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS NOTICE BE LIABLE FOR ANY CLAIM, OR
        ANY SPECIAL INDIRECT OR CONSEQUENTIAL DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR
        PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION
        WITH THE USE OR PERFORMANCE OF THE DATA FILES OR SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not be used in advertising or otherwise
        to promote the sale, use or other dealings in these Data Files or Software without prior written authorization of
        the copyright holder.

## UnixODBC

The following software may be included in this product: unixODBC. Use of any of this software is governed by the
    terms of the license below:

Jump to LGPL Version 2.1

## W3C Simple API for CSS (SAC)

The following software may be included in this product: W3C Simple API for CSS (SAC). Use of any of this
        software is governed by the terms of the license below:

### W3C IPR SOFTWARE NOTICE

Copyright © 2002 World Wide Web Consortium, (Massachusetts Institute of Technology, Institut National de
        Recherche en Informatique et en Automatique, Keio University). All Rights Reserved.

Note: The original version of the W3C Software Copyright Notice and License could be
        found at http://www.w3.org/Consortium/Legal/copyright-software-19980720

Copyright © 1994-2002 World Wide Web Consortium, (Massachusetts Institute of Technology, Institut
        National de Recherche en Informatique et en Automatique, Keio University).
        All Rights Reserved. http://www.w3.org/Consortium/Legal/

This W3C work (including software, documents, or other related items) is being provided by the copyright holders
        under the following license. By obtaining, using and/or copying this work, you (the licensee) agree that you have
        read, understood, and will comply with the following terms and conditions:

Permission to use, copy, and modify this software and its documentation, with or without modification, for any
        purpose and without fee or royalty is hereby granted, provided that you include the following on ALL copies of the
        software and documentation or portions thereof, including modifications, that you make:

The full text of this NOTICE in a location viewable to users of the redistributed or derivative
            work. 

Any pre-existing intellectual property disclaimers, notices, or terms and conditions. If none exist, a
            short notice of the following form (hypertext is preferred, text is permitted) should be used within the body
            of any redistributed or derivative code: "Copyright © 2002 World Wide Web
            Consortium, (Massachusetts Institute of Technology, Institut National deRecherche en Informatique et en
            Automatique, Keio University). All Rights Reserved. http://www.w3.org/Consortium/Legal/" 

Notice of any changes or modifications to the W3C files, including the date changes were made. (We
            recommend you provide URIs to the location from which the code is derived.)

THIS SOFTWARE AND DOCUMENTATION IS PROVIDED "AS IS," AND COPYRIGHT HOLDERS MAKE NO REPRESENTATIONS OR
        WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY OR FITNESS FOR ANY
        PARTICULAR PURPOSE OR THAT THE USE OF THE SOFTWARE OR DOCUMENTATION WILL NOT INFRINGE ANY THIRD PARTY PATENTS,
        COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.

COPYRIGHT HOLDERS WILL NOT BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF
        ANY USE OF THE SOFTWARE OR DOCUMENTATION.

The name and trademarks of copyright holders may NOT be used in advertising or publicity pertaining to the
        software without specific, written prior permission. Title to copyright in this software and any associated
        documentation will at all times remain with copyright holders.

X11 XRender Extension

The following software may be included in this product: X11 XRender Extension. Use of any of this software is
        governed by the terms of the license below:

Copyright © 2000 SuSE, Inc.

Permission to use, copy, modify, distribute, and sell this software and its documentation for any purpose is
        hereby granted without fee, provided that the above copyright notice appear in all copies and that both that
        copyright notice and this permission notice appear in supporting documentation, and that the name of SuSE not be
        used in advertising or publicity pertaining to distribution of the software without specific, written prior
        permission. SuSE makes no representations about the suitability of this software for any purpose. It is provided
        "as is" without express or implied warranty.

SuSE DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
        AND FITNESS, IN NO EVENT SHALL SuSE BE LIABLE FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
        WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
        TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

## XML Security Library (xmlsec)

The following software may be included in this product: XML Security Library (xmlsec). Use of any of this
        software is governed by the terms of the license below:

### xmlsec, xmlsec-openssl, xmlsec-gnutls libraries

Copyright (C) 2002-2003 Aleksey Sanin. All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT- NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
        EVENT SHALL THE ALEKSEY SANIN BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON- NECTION WITH THE SOFTWARE OR THE USE OR OTHER
        DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Aleksey Sanin shall not be used in advertising or otherwise
        to promote the sale, use or other dealings in this Software without prior written authorization from him.

### xmlsec-nss library

Copyright (C) 2002-2003 Aleksey Sanin. All Rights Reserved.

Copyright (c) 2003 America Online, Inc. All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the "Software"), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
        and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Software.

Portions of the Software were created using source code and/or APIs governed by the Mozilla Public License
        (MPL). The MPL is available at http://www.mozilla.org/MPL/MPL-1.1.html. The MPL permits such
        portions to be distributed with code not governed by MPL, as long as the requirements of MPL are fulfilled for
        such portions.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT- NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
        EVENT SHALL THE ALEKSEY SANIN BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON- NECTION WITH THE SOFTWARE OR THE USE OR OTHER
        DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Aleksey Sanin shall not be used in advertising or otherwise
        to promote the sale, use or other dealings in this Software without prior written authorization from him.

### xmlsec-mscrypto library

Copyright (C) 2002-2016 Aleksey Sanin <<EMAIL>>. All Rights Reserved.

Copyright (C) 2003 Cordys R&D BV, All rights reserved.

Copyright (C) 2007 Roumen Petrov.

Copyright (c) 2005-2006 Cryptocom LTD (http://www.cryptocom.ru).

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
            associated documentation files (the "Software"), to deal in the Software without restriction, including
            without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
            copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the
            following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
            portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
            LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
            EVENT SHALL THE ALEKSEY SANIN BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
            CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
            DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Aleksey Sanin shall not be used in advertising or
            otherwise to promote the sale, use or other dealings in this Software without prior written authorization
            from him.

### xmlsec-mscng library

Copyright (C) 2018 Aleksey Sanin. All Rights Reserved.

Copyright (C) 2018 Miklos Vajna. All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
            associated documentation files (the "Software"), to deal in the Software without restriction, including
            without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
            copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the
            following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
            portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
            LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
            EVENT SHALL THE ALEKSEY SANIN BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
            CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
            DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Aleksey Sanin shall not be used in advertising or
            otherwise to promote the sale, use or other dealings in this Software without prior written authorization
            from him.

## XSLT MathML Library

The following software may be included in this product: XSLT MathML Library. Use of any of this software is
        governed by the terms of the license below:

### Copyright

Copyright (C) 2001-2003 Vasil Yaroshevich

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
        documentation files (the ``Software''), to deal in the Software without restriction, including without limitation
        the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
        permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of
        the Software.

Except as contained in this notice, the names of individuals credited with contribution to this software shall
        not be used in advertising or otherwise to promote the sale, use or other dealings in this Software without prior
        written authorization from the individuals in question.

Any stylesheet derived from this Software that is publicly distributed will be identified with a different name
        and the version strings in any derived Software will be changed so that no possibility of confusion between the
        derived package and this Software will exist.

### Warranty

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
        THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL NORMAN
        WALSH OR ANY OTHER CONTRIBUTOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
        IN THE SOFTWARE.

## zlib

The following software may be included in this product: zlib. Use of any of this software is governed by the
        terms of the license below:

(C) 1995-2002 Jean-loup Gailly and Mark Adler

This software is provided 'as-is', without any express or implied warranty. In no event will the authors be
        held liable for any damages arising from the use of this software.

Permission is granted to anyone to use this software for any purpose, including commercial applications, and
        to alter it and redistribute it freely, subject to the following restrictions:

1. The origin of this software must not be misrepresented; you must not claim that you wrote the original
        software. If you use this software in a product, an acknowledgment in the product documentation would be
        appreciated but is not required.

2. Altered source versions must be plainly marked as such, and must not be misrepresented as being the
        original software.

3. This notice may not be removed or altered from any source distribution.

## Skia

The following software may be included in this product: Skia. Use of any of this software is governed by
        the terms of the license below:

Copyright (c) 2011 Google Inc. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution.

Neither the name of Google Inc. nor the names of its contributors may be used to endorse or promote
            products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

# Extensions

Only third party extensions are listed here whose source code is not in the LibreOffice tree.

## Numbertext

The following software may be included in this product: Numbertext. Use of any of this software is governed
        by the terms of the license below:

License: LGPL/BSD dual-license, 2009-2010 (C) László Németh (nemeth at openoffice dot org)

Numbertext language data (Soros programs):

LGPL/BSD dual-license, 2009-2010 (C) László Németh et al. (see AUTHORS)

Serbian modules:

CC/LGPL/BSD tri-license, 2009 (C) Goran Rakić (grakic at devbase dot net)

Note: for full distribution with specifications, IDE and JavaScript implementation, see http://NUMBERTEXT.org

Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied.

Jump to LGPL Version 3

## Solver for Nonlinear Programming

The following software may be included in this product: Solver for Nonlinear Programming. Use of any of this
        software is governed by the terms of the license below:

Copyright 2009 by Sun Microsystems, Inc.

Jump to LGPL Version 3

# Fonts

## Alef

Copyright (c) 2012, HaGilda & Mushon Zer-Aviv (<http://hagilda.com|<EMAIL>>), with
        Reserved Font Name Alef Regular.

Copyright (c) 2012, HaGilda & Mushon Zer-Aviv (<http://hagilda.com|<EMAIL>>), with
        Reserved Font Name Alef Bold.

This Font Software is licensed under the SIL Open Font License, Version 1.1 as shown below.

Jump to SIL Open Font License, Version 1.1

## Amiri

Copyright (c) 2010-2017, Khaled Hosny (<<EMAIL>>)

This Font Software is licensed under the SIL Open Font License, Version 1.1 as shown below.

Jump to SIL Open Font License, Version 1.1

## Caladea

Copyright (c) 2012 Huerta Tipográfica

This Font Software is licensed under the Apache License, Version 2.0 as shown below.

Jump to Apache License Version 2.0

## Carlito

Copyright (c) 2010-2013 by tyPoland Lukasz Dziedzic with Reserved Font Name "Carlito".

This Font Software is licensed under the SIL Open Font License, Version 1.1 as shown below.

Jump to SIL Open Font License, Version 1.1

## Culmus

This package is distributed under the terms of GNU General Public License version 2.

Jump to GPL Version 2

Shofar font family is Copyright 2011-2012 by Yoram Gnat (<EMAIL>) Latin glyphs and
            punctuation Copyright (URW)++,Copyright 1999 by (URW)++ Design & Development;

Hebrew Cantillation marks GSUB and GPOS OpenType positioning rules copyright (c) 2010 by Yoram Gnat
            (<EMAIL>)

Inspired by the Hebrew OpenType Layout logic copyright (c) 2003 & 2007, Ralph Hancock & John
            Hudson published under the MIT License.

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Keter YG font family is Copyright 2009 by Yoram Gnat (<EMAIL>) Hebrew OpenType Layout logic
            copyright (c) 2003 & 2007, Ralph Hancock & John Hudson. This layout logic for Biblical Hebrew is
            open source software under the MIT License; for details contact copyright holders at
            <<EMAIL>>.

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Hadasim font family is copyright 2010 by Yoram Gnat (<EMAIL>). Latin and parts of general
            punctuation marks based on modified forms from the LiberationSerif font, created by Digitized data 2007
            Ascender Corporation. All rights reserved.

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Simple font family is copyright 2007-2010 by Yoram Gnat (<EMAIL>). Latin glyphs, digits
            and punctuation copyright 1999 by (URW)++ Design & Development. All rights reserved.

Hebrew vowel marks positioning algorithms Copyright 2010 by Yoram Gnat 2010. (<EMAIL>.

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Stam Ashkenaz font is copyright 2007-2010 by Yoram Gnat (<EMAIL>).

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Stam Sefarad font is copyright 2008-2010 by Yoram Gnat (<EMAIL>).

As a special exception, if you create a document which uses this font, and embed this font or unaltered
            portions of this font into the document, this font does not by itself cause the resulting document to be
            covered by the GNU General Public License. This exception does not however invalidate any other reasons why
            the document might be covered by the GNU General Public License. If you modify this font, you may extend
            this exception to your version of the font, but you are not obligated to do so. If you do not wish to do
            so, delete this exception statement from your version.

 

Miriam font family is copyright 2004-2010 by Maxim Iorsh (<EMAIL>). All rights
            reserved.

 

Yehuda font family is copyright 2004 by Maxim Iorsh (<EMAIL>). All rights
            reserved.

 

Drugulin font family (Hebrew glyphs) is copyright 2003,2004 by Maxim Iorsh (<EMAIL>).
            Latin glyphs, digits and punctuation contained in the Drugulin font family are part of Nimbus Roman No9 L
            font family and are copyright 1999 by (URW)++ Design & Development. All rights reserved.

 

Ellinia font family is copyright 2003,2004 by Maxim Iorsh (<EMAIL>). All rights
            reserved.

 

Caladings collection is copyright 2003,2004 by Maxim Iorsh (<EMAIL>). All rights
            reserved.

 

Digital version of Aharoni font family (Hebrew glyphs, numerals and part of punctuation) is copyright
            2002-2004 by Maxim Iorsh (<EMAIL>). Latin glyphs, digits and part of punctuation
            contained in the Aharoni font family are part of URW Gothic L font family and are copyright 1999 by (URW)++
            Design & Development. All rights reserved.

 

Digital version of David font family (Hebrew glyphs, numerals and part of punctuation) is copyright
            2002-2010 by Maxim Iorsh (<EMAIL>). Latin glyphs and part of punctuation contained in
            the David font family are part of Bitstream Charter font family and are copyright 1990 as an unpublished
            work by Bitstream Inc. All rights reserved. See also LICENSE-BITSTREAM below:

(c) Copyright 1989-1992, Bitstream Inc., Cambridge, MA.

You are hereby granted permission under all Bitstream propriety rights to use, copy, modify,
                sublicense, sell, and redistribute the 4 Bitstream Charter (r) Type 1 outline fonts and the 4 Courier
                Type 1 outline fonts for any purpose and without restriction; provided, that this notice is left intact
                on all copies of such fonts and that Bitstream's trademark is acknowledged as shown below on all
                unmodified copies of the 4 Charter Type 1 fonts.

BITSTREAM CHARTER is a registered trademark of Bitstream Inc.

 

Frank-Ruehl font family (Hebrew glyphs) is copyright 2002-2011 by Maxim Iorsh
            (<EMAIL>). Latin glyphs, digits and punctuation contained in the Frank-Ruehl font
            family are part of Century Schoolbook L font family and are copyright 1999 by (URW)++ Design &
            Development. All rights reserved.

 

Nachlieli font family (Hebrew glyphs) is copyright 2002-2017 by Maxim Iorsh
            (<EMAIL>). Latin glyphs, digits and punctuation contained in the Nachlieli font family
            are part of Nimbus Sans L font family and are copyright 1999 by (URW)++ Design & Development. All
            rights reserved.

 

Miriam Mono font family (Hebrew glyphs) is copyright 2002-2004 by Maxim Iorsh
            (<EMAIL>). Hebrew vowel marks positioning algorithms implementation by Yoram Gnat
            (<EMAIL>), 2010. Latin glyphs, digits and punctuation contained in the Miriam Mono font family
            are part of Nimbus Mono L font family and are copyright 1999 by (URW)++ Design & Development. All
            rights reserved.

## Deja Vu

The following software may be included in this product: Deja Vu fonts. Use of any of this software is
        governed by the terms of the license below:

Fonts are (c) Bitstream (see below). DejaVu changes are in public domain. Glyphs imported from Arev fonts
        are (c) Tavmjong Bah (see below)

### Bitstream Vera Fonts Copyright

Copyright (c) 2003 by Bitstream, Inc. All Rights Reserved. Bitstream Vera is a trademark of Bitstream,
        Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy of the fonts accompanying this
        license ("Fonts") and associated documentation files (the "Font Software"), to reproduce and distribute the
        Font Software, including without limitation the rights to use, copy, merge, publish, distribute, and/or sell
        copies of the Font Software, and to permit persons to whom the Font Software is furnished to do so, subject to
        the following conditions:

The above copyright and trademark notices and this permission notice shall be included in all copies of one
        or more of the Font Software typefaces.

The Font Software may be modified, altered, or added to, and in particular the designs of glyphs or
        characters in the Fonts may be modified and additional glyphs or characters may be added to the Fonts, only if
        the fonts are renamed to names not containing either the words "Bitstream" or the word "Vera".

This License becomes null and void to the extent applicable to Fonts or Font Software that has been modified
        and is distributed under the "Bitstream Vera" names.

The Font Software may be sold as part of a larger software package but no copy of one or more of the Font
        Software typefaces may be sold by itself.

THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF
        COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL BITSTREAM OR THE GNOME FOUNDATION BE LIABLE FOR
        ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL
        DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO USE
        THE FONT SOFTWARE OR FROM OTHER DEALINGS IN THE FONT SOFTWARE.

Except as contained in this notice, the names of Gnome, the Gnome Foundation, and Bitstream Inc., shall not
        be used in advertising or otherwise to promote the sale, use or other dealings in this Font Software without
        prior written authorization from the Gnome Foundation or Bitstream Inc., respectively. For further information,
        contact: fonts at gnome dot org.

### Arev Fonts Copyright

Copyright (c) 2006 by Tavmjong Bah. All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of the fonts accompanying this
        license ("Fonts") and associated documentation files (the "Font Software"), to reproduce and distribute the
        modifications to the Bitstream Vera Font Software, including without limitation the rights to use, copy, merge,
        publish, distribute, and/or sell copies of the Font Software, and to permit persons to whom the Font Software
        is furnished to do so, subject to the following conditions:

The above copyright and trademark notices and this permission notice shall be included in all copies of one
        or more of the Font Software typefaces.

The Font Software may be modified, altered, or added to, and in particular the designs of glyphs or
        characters in the Fonts may be modified and additional glyphs or characters may be added to the Fonts, only if
        the fonts are renamed to names not containing either the words "Tavmjong Bah" or the word "Arev".

This License becomes null and void to the extent applicable to Fonts or Font Software that has been modified
        and is distributed under the "Tavmjong Bah Arev" names.

The Font Software may be sold as part of a larger software package but no copy of one or more of the Font
        Software typefaces may be sold by itself.

THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
        LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF
        COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL TAVMJONG BAH BE LIABLE FOR ANY CLAIM, DAMAGES
        OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER IN
        AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR
        FROM OTHER DEALINGS IN THE FONT SOFTWARE.

Except as contained in this notice, the name of Tavmjong Bah shall not be used in advertising or otherwise
        to promote the sale, use or other dealings in this Font Software without prior written authorization from
        Tavmjong Bah. For further information, contact: tavmjong @ free . fr.

## EmojiOne Color

### Licenses

Copyright 2016 Brad Erickson CC-BY-4.0/MIT

Copyright 2015 Ranks.com Inc. CC-BY-4.0

Copyright 2013 Joe Loughry and Terence Eden MIT

Applies to SVG files and TTF font files

License: Creative Commons Attribution 4.0 International

Human Readable License: http://creativecommons.org/licenses/by/4.0/

Complete Legal Terms: http://creativecommons.org/licenses/by/4.0/legalcode

Applies to everything else

License: MIT

Complete Legal Terms: http://opensource.org/licenses/MIT

The SVG files of the EmojiOne project have been modified to create the
        fallback emoji glyphs and used as-is for the SVGinOT color glyphs. Files are stored in
        assets/emojione-svg.

Source: https://github.com/Ranks/emojione

Art License: Creative Commons Attribution 4.0 International

Please review the specific attribution requirements for commercial use of EmojiOne graphics:
        http://emojione.com/licensing/

The SVG files from the Unicode Power Symbol project have been
        modified to create B&W Power Symbol glyphs. Files are stored in assets/svg-bw.

Source: https://github.com/jloughry/Unicode

Art License: MIT

## Gentium

The following software may be included in this product: Gentium fonts. Use of any of this software is
        governed by the terms of the license below:

Copyright (c) 2003-2008 SIL International (http://www.sil.org/), with
        Reserved Font Names "Gentium" and "SIL".

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## KACST

KACST fonts are free fonts donated by King Abdulaziz City For Science And Technology (KACST) under GPL.

KACST holds the copyright of the included Arabic font which is donated under GPL by KACST.

Jump to GPL Version 2

## Liberation

The following software may be included in this product: Liberation fonts. Use of any of this software is
        governed by the terms of the license below:

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

### LICENSE AGREEMENT AND LIMITED PRODUCT WARRANTY
        LIBERATION FONT SOFTWARE

This agreement governs the use of the Software and any updates to the Software, regardless of the delivery
        mechanism. Subject to the following terms, Red Hat, Inc. ("Red Hat") grants to the user ("Client") a license to
        this work pursuant to the GNU General Public License v.2 with the exceptions set forth below and such other
        terms as are set forth in this End User License Agreement.

1. The Software and License Exception. LIBERATION font software (the "Software") consists of
        TrueType-OpenType formatted font software for rendering LIBERATION typefaces in sans-serif, serif, and
        monospaced character styles. You are licensed to use, modify, copy, and distribute the Software pursuant to the
        GNU General Public License v.2 with the following exceptions:

 (a) As a special exception, if you create a document which uses this font, and embed this font or
        unaltered portions of this font into the document, this font does not by itself cause the resulting document to
        be covered by the GNU General Public License. This exception does not however invalidate any other reasons why
        the document might be covered by the GNU General Public License. If you modify this font, you may extend this
        exception to your version of the font, but you are not obligated to do so. If you do not wish to do so, delete
        this exception statement from your version.

 (b) As a further exception, any distribution of the object code of the Software in a physical product must
        provide you the right to access and modify the source code for the Software and to reinstall that modified
        version of the Software in object code form on the same physical product on which you received it.

2. Intellectual Property Rights. The Software and each of its components, including the source code,
        documentation, appearance, structure and organization are owned by Red Hat and others and are protected under
        copyright and other laws. Title to the Software and any component, or to any copy, modification, or merged
        portion shall remain with the aforementioned, subject to the applicable license. The "LIBERATION" trademark is
        a trademark of Red Hat, Inc. in the U.S. and other countries. This agreement does not permit Client to
        distribute modified versions of the Software using Red Hat's trademarks. If Client makes a redistribution of a
        modified version of the Software, then Client must modify the files names to remove any reference to the Red
        Hat trademarks and must not use the Red Hat trademarks in any way to reference or promote the modified
        Software.

3. Limited Warranty. To the maximum extent permitted under applicable law, the Software is provided and
        licensed "as is" without warranty of any kind, expressed or implied, including the implied warranties of
        merchantability, non-infringement or fitness for a particular purpose. Red Hat does not warrant that the
        functions contained in the Software will meet Client's requirements or that the operation of the Software will
        be entirely error free or appear precisely as described in the accompanying documentation.

4. Limitation of Remedies and Liability. To the maximum extent permitted by applicable law, Red Hat or any
        Red Hat authorized dealer will not be liable to Client for any incidental or consequential damages, including
        lost profits or lost savings arising out of the use or inability to use the Software, even if Red Hat or such
        dealer has been advised of the possibility of such damages.

5. General. If any provision of this agreement is held to be unenforceable, that shall not affect the
        enforceability of the remaining provisions. This agreement shall be governed by the laws of the State of North
        Carolina and of the United States, without regard to any conflict of laws provisions, except that the United
        Nations Convention on the International Sale of Goods shall not apply.

Copyright © 2007 Red Hat, Inc. All rights reserved. LIBERATION is a trademark of Red Hat, Inc.

## Linux Libertine G and Linux Biolinum G

This Font Software is Copyright (c) 2003-2006, Philipp H. Poll (http://linuxlibertine.sf.net/).
        All Rights Reserved.
        "Linux Libertine" is a Reserved Font Name for this Font Software.

Graphite extension of the original Linux Libertine font was made by Laszlo Nemeth under the same
        license.
        Our fonts are free in the sense of the GPL. In short: Changing the font is allowed as long as the derivative
        work is published under the same license again. Pedantics keep claiming that the embedded use of GPL-fonts in
        i.e. PDFs requires the free publication of the PDF as well. This is why our GPL contains the so called "font
        exception".

Jump to GPL version 2

As a special exception, if you create a document which uses this font, and embed this font or unaltered
        portions of this font into the document, this font does not by itself cause the resulting document to be
        covered by the GNU General Public License. This exception does not however invalidate any other reasons why the
        document might be covered by the GNU General Public License. If you modify this font, you may extend this
        exception to your version of the font, but you are not obligated to do so. If you do not wish to do so, delete
        this exception statement from your version.

Additionally our fonts are licensed under the Open Fonts License (see below).

This Font Software is licensed under the SIL Open Font License, Version 1.0. No modification of the license
        is permitted, only verbatim copy is allowed. This license is copied below, and is also available with a FAQ at:
        http://scripts.sil.org/OFL

### SIL OPEN FONT LICENSE Version 1.0 - 22 November 2005

PREAMBLE The goals of the Open Font License (OFL) are to stimulate worldwide development of cooperative font
        projects, to support the font creation efforts of academic and linguistic communities, and to provide an open
        framework in which fonts may be shared and improved in partnership with others.

The OFL allows the licensed fonts to be used, studied, modified and redistributed freely as long as they are
        not sold by themselves. The fonts, including any derivative works, can be bundled, embedded, redistributed and
        sold with any software provided that the font names of derivative works are changed. The fonts and derivatives,
        however, cannot be released under any other type of license.

DEFINITIONS "Font Software" refers to any and all of the following:  - font files -
        data files - source code - build scripts -
        documentation

"Reserved Font Name" refers to the Font Software name as seen by users and any other names as specified
        after the copyright statement.

"Standard Version" refers to the collection of Font Software components as distributed by the Copyright
        Holder.

"Modified Version" refers to any derivative font software made by adding to, deleting, or substituting -- in
        part or in whole -- any of the components of the Standard Version, by changing formats or by porting the Font
        Software to a new environment.

"Author" refers to any designer, engineer, programmer, technical writer or other person who contributed to
        the Font Software.

PERMISSION & CONDITIONS Permission is hereby granted, free of charge, to any person obtaining a copy of
        the Font Software, to use, study, copy, merge, embed, modify, redistribute, and sell modified and unmodified
        copies of the Font Software, subject to the following conditions:

1) Neither the Font Software nor any of its individual components, in Standard or Modified Versions, may be
        sold by itself.

2) Standard or Modified Versions of the Font Software may be bundled, redistributed and sold with any
        software, provided that each copy contains the above copyright notice and this license. These can be included
        either as stand-alone text files, human-readable headers or in the appropriate machine-readable metadata fields
        within text or binary files as long as those fields can be easily viewed by the user.

3) No Modified Version of the Font Software may use the Reserved Font Name(s), in part or in whole, unless
        explicit written permission is granted by the Copyright Holder. This restriction applies to all references
        stored in the Font Software, such as the font menu name and other font description fields, which are used to
        differentiate the font from others.

4) The name(s) of the Copyright Holder or the Author(s) of the Font Software shall not be used to promote,
        endorse or advertise any Modified Version, except to acknowledge the contribution(s) of the Copyright Holder
        and the Author(s) or with their explicit written permission.

5) The Font Software, modified or unmodified, in part or in whole, must be distributed using this license,
        and may not be distributed under any other license.

TERMINATION This license becomes null and void if any of the above conditions are not met.

DISCLAIMER THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
        INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
        NONINFRINGEMENT OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE COPYRIGHT HOLDER BE
        LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR
        CONSEQUENTIAL DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR
        INABILITY TO USE THE FONT SOFTWARE OR FROM OTHER DEALINGS IN THE FONT SOFTWARE.

## Libre Hebrew

The following software may be included in this product: Libre Hebrew fonts (David Libre, Frank Ruhl Hofsi,
        Miriam Libre, Rubik). Use of any of this software is governed by the terms of the license below:

Copyright 2015 The Frank Ruhl Hofshi Project Authors.

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Noto

The following software may be included in this product: Google Noto fonts. Use of any of this software is
        governed by the terms of the license below:

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Reem Kufi

The following software may be included in this product: Reem Kufi fonts. Use of any of this software is
        governed by the terms of the license below:

Portions copyright (c) 2015, Khaled Hosny (<EMAIL>). Copyright (c) 2010 by Typemade, with
        Reserved Font Name 'Josefin. All Rights Reserved.

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Scheherazade

The following software may be included in this product: Scheherazade fonts. Use of any of this software is
        governed by the terms of the license below:

Copyright (c) 1994-2015, SIL International (http://www.sil.org/).

This Font Software is licensed under the SIL Open Font License, Version 1.1, with Reserved Font Names
        "Scheherazade" and "SIL". This license is copied below, and is also available with a FAQ at:
        http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Source Code Pro

The following software may be included in this product: Source Code Pro fonts. Use of any of this software
        is governed by the terms of the license below:

Copyright 2010, 2012 Adobe Systems Incorporated (http://www.adobe.com/),
        with Reserved Font Name 'Source'. All Rights Reserved. Source is a trademark of Adobe Systems Incorporated in
        the United States and/or other countries.

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Source Sans Pro

The following software may be included in this product: Source Sans Pro fonts. Use of any of this software
        is governed by the terms of the license below:

Copyright 2010, 2012 Adobe Systems Incorporated (http://www.adobe.com/),
        with Reserved Font Name 'Source'. All Rights Reserved. Source is a trademark of Adobe Systems Incorporated in
        the United States and/or other countries.

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

## Source Serif Pro

The following software may be included in this product: Source Serif Pro fonts. Use of any of this software
        is governed by the terms of the license below:

Copyright 2014 Adobe Systems Incorporated (http://www.adobe.com/),
        with Reserved Font Name 'Source'. All Rights Reserved. Source is a trademark of Adobe Systems Incorporated in
        the United States and/or other countries.

This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is copied below,
        and is also available with a FAQ at: http://scripts.sil.org/OFL

Jump to SIL Open Font License, Version 1.1

# Dictionaries

## Afrikaans

### Spelling dictionary

The following software may be included in this product: Afrikaans spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

The Afrikaans wordlist used by MySpell is made up of multiple wordlists. These are wither in the public
        domain, are used with the authors permission or are licensed under the LGPL. The majority
        of the words are derived from the Nieuwoudt and Viljoen list.

The Nieuwoudt list was first published in this form in 1993 by Bernard A Nieuwoudt. Contact details: origen
        at icon co za or bnieuwoudt at acm org

Copyright (C) 1993, 2003 Bernard A Nieuwoudt relicensed under the LGPL.

The Viljoen list is derived from the original Nieuwoudt list and is used with permission of the author and
        relicensed under the LGPL.

Copyright (C) 1998 Danie Viljoen

Copyright (C) 2003 Dwayne Bailey under the LGPL based on the original ispell list by
        Renier de Vos which was released under the BSD license.

### Hyphenation patterns

The following software may be included in this product: Afrikaans hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2005 Friedel Wolff

This library is free software; you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License as published by the Free Software
        Foundation; either version 2.1 of the License, or (at your option) any later version.

## Albanian

### Spelling dictionary

The following software may be included in this product: Albanian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GNU GPL version 2.0

## Arabic

### Spelling dictionary

The following software may be included in this product: Arabic spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Author of Hunspell-ar, the arabic dictionary for Hunspell (spellchecker): 2006-2008, Mohamed Kebdani,
        med.kebdani (at) gmail.com

GPL 2.0/LGPL 2.1/MPL 1.1
        tri-license

The contents of this software may be used under the terms of the GNU General Public License Version 2 or
        later (the "GPL"), or the GNU Lesser General Public License Version 2.1 or later (the "LGPL") the Mozilla
        Public License Version 1.1 or later (the "MPL").

Software distributed under these licenses is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied. See the licenses for the specific language governing rights and limitations under
        the licenses.

### Thesaurus

The following software may be included in this product: Arabic thesaurus. Use of any of this software is
        governed by the terms of the license below:

2006-2009, Taha Zerrouki, taha_zerrouki at gawab.com

GPL 2.0/LGPL 2.1/MPL 1.1
        tri-license

The contents of this software may be used under the terms of the GNU General Public License Version 2 or
        later (the "GPL"), or the GNU Lesser General Public License Version 2.1 or later (the "LGPL") the Mozilla
        Public License Version 1.1 or later (the "MPL").

Software distributed under these licenses is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied. See the licenses for the specific language governing rights and limitations under
        the licenses.

## Aragonese

### Spelling dictionary

The following software may be included in this product: Aragonese spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (c) 2011 Santiago Paricio (sparicio<at>gmail<dot>com)
        Copyright (c) 2011 Juan Pablo Martínez (jpmart<at>unizar<dot>es)

MPL1.1/GPLv3+/LGPLv3+
        Tri-licence

Version 0.2 of the wordlist (an_ES.dic) has been built using free corpuses have been used as Wikipedia in
        Aragonese (http://an.wikipedia.org) and wordlists obtained from Apertium
        dictionaries (http://apertium.svn.sourceforge.net/viewvc/apertium/tags/apertium-es-an/release-2.0/).

## Belarusian

### Spelling dictionary

The following software may be included in this product: Belarusian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Creative Commons CC-BY-SA

Author: Mikalai Udodau <<EMAIL>>
        Origin: Словазбор аўтарскі; арфаграфія паводле ТСБМ-2005

### Hyphenation Dictionary

The following software may be included in this product: Belarusian hyphenation dictionary. Use of any of this
        software is governed by the terms of the license below:

Creative Commons CC-BY-SA or LGPLv3

Created by: Alex Buloichik <<EMAIL>>
        Hyphenation rules according to 'Pravapis 2008'

## Bengali

### Spelling dictionary

The following software may be included in this product: Bengali spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GNU GPL version 2.0

## Bosnian

### Spelling dictionary

The following software may be included in this product: Bosnian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

GPL 2.0/LGPL 2.1/MPL 1.1
        tri-license

## Breton

### Spelling dictionary

The following software may be included in this product: Breton spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

LICENSE: "An Drouizig" spelling dictionary: LGPL

## Bulgarian

### Spelling dictionary

The following software may be included in this product: Bulgarian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GNU GPL version 2.0

### Hyphenation patterns

The following software may be included in this product: Bulgarian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2001 Anton Zinoviev and Borislav Mitev
        Maintained by Radostin Radnev

Jump to GNU GPL version 2.0

### Thesaurus

The following software may be included in this product: Bulgarian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2001 Radostin Radnev

Jump to GNU GPL version 2.0

## Catalan

### Spelling dictionary

The following software may be included in this product: Catalan spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2002-2008 Joan Moratinos <<EMAIL>>

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version. This program is distributed in the hope that it will be useful, but WITHOUT ANY
        WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

See the GNU General Public License for more details. You should have received a copy of the GNU General
        Public License along with this program; if not, write to the Free Software Foundation, Inc., 59 Temple Place -
        Suite 330, Boston, MA 02111-1307, USA.

Jump to GPL version 3

### Hyphenation patterns

The following software may be included in this product: Catalan hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL version 3

### Thesaurus

The following software may be included in this product: Catalan thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2009 Joan Montané <<EMAIL>> --- Softcatalà

License: GPL/LGPL

Jump to GPL version 3

Jump to LGPL version 3

## Czech

### Spelling dictionary

The following software may be included in this product: Czech spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GNU GPL version 2.0

### Hyphenation patterns

The following software may be included in this product: Czech hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Jump to GNU GPL version 2.0

### Thesaurus

The following software may be included in this product: Czech thesaurus. Use of any of this software is
        governed by the terms of the license below:

Origin: Generated from an English <-> Czech translation dictionary, https://www.svobodneslovniky.cz/

License: GNU Free Documentation License 1.1 or later, details in Appendix A

The thesaurus is generated automatically via a script by Jan Holesovsky <<EMAIL>>, based
        on idea of Zdenek Zabokrtsky - big thanks!

## Croatian

### Spelling dictionary

The following software may be included in this product: Croatian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to LGPL Version 2.1

### Hyphenation patterns

The following software may be included in this product: Croatian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

These patterns were manually converted from TeX hyphenation patterns using the guide at http://wiki.services.openoffice.org/wiki/Documentation/SL/Using_TeX_hyphenation_patterns_in_OpenOffice.org

Original version: http://tug.org/svn/texhyphen/trunk/hyph-utf8/tex/generic/hyph-utf8/patterns/txt/hyph-hr.pat.txt?revision=416

License: OpenOffice.org adaption of this file is licensed under the GNU LGPL license.

Jump to LGPL Version 2.1

Original license text: This file is part of hyph-utf8 package and resulted from semi-manual conversions of
        hyphenation patterns into UTF-8 in June 2008.

Source: hrhyph.tex (1996-04-10) Author: Marinović Igor <migor at student.math.hr>

The above mentioned file should become obsolete, and the author of the original file should preferably
        modify this file instead.

Modifications were needed in order to support native UTF-8 engines, but functionality (hopefully) didn't
        change in any way, at least not intentionally. This file is no longer stand-alone; at least for 8-bit engines
        you probably want to use loadhyph-foo.tex (which will load this file) instead.

Modifications were done by Jonathan Kew, Mojca Miklavec & Arthur Reutenauer with help & support
        from: - Karl Berry, who gave us free hands and all resources - Taco Hoekwater, with useful macros - Hans Hagen,
        who did the unicodifisation of patterns already long before and helped with testing, suggestions and bug
        reports - Norbert Preining, who tested & integrated patterns into TeX Live

However, the "copyright/copyleft" owner of patterns remains the original author.

The copyright statement of this file is thus:

Do with this file whatever needs to be done in future for the sake of "a better world" as long as you
        respect the copyright of original file. If you're the original author of patterns or taking over a new
        revolution, please remove all of the TUG comments & credits that we added here - you are the Queen / the
        King, we are only the servants.

If you want to change this file, rather than uploading directly to CTAN, we would be grateful if you could
        send it to us (http://tug.org/tex-hyphen) or ask for credentials for
        SVN repository and commit it yourself; we will then upload the whole "package" to CTAN.

Before a new "pattern-revolution" starts, please try to follow some guidelines if possible:

- \lccode is *forbidden*, and I really mean it - all the patterns should be in UTF-8 - the only "allowed"
        TeX commands in this file are: \patterns, \hyphenation, and if you really cannot do without, also \input and
        \message - in particular, please no \catcode or \lccode changes, they belong to loadhyph-foo.tex, and no
        \lefthyphenmin and \righthyphenmin, they have no influence here and belong elsewhere - \begingroup and/or
        \endinput is not needed - feel free to do whatever you want inside comments

We know that TeX is extremely powerful, but give a stupid parser at least a chance to read your
        patterns.

For more information see http://tug.org/tex-hyphen

## Danish

### Spelling dictionary

The following software may be included in this product: Danish spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Stavekontrolden - Danish dictionary files for Hunspell

Version 1.6 - 2010-09-19

da_DK.dic, da_DK.aff: © 2010 Foreningen for frit tilgængelige sprogværktøjer

http://www.stavekontrolden.dk

These files are published under the following open source licenses:

GNU GPL version 2.0

GNU LGPL version 2.1

Mozilla MPL version 1.1

This dictionary is based on data from Det Danske Sprog- og Litteraturselskab

(The Danish Society for Language and Literature), http://www.dsl.dk.

### Hyphenation patterns

The following software may be included in this product: Danish hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

### Thesaurus

The following software may be included in this product: Danish thesaurus. Use of any of this software is
        governed by the terms of the license below:

GNU GPL version 2.0

GNU LGPL version 2.1

Mozilla MPL version 1.1

Stavekontrolden - Danish thesaurus files for OpenOffice.org 3.0.

Den Danske Ordbog - Synonymer

DanNet - leksikalsk-semantisk ordnet fra Det Danske Sprog- og Litteraturselskab og Center for
        Sprogteknologi, Københavns Universitet

© 2007 Foreningen for frit tilgængelige sprogværktøjer

## Dutch

### Spelling dictionary and hyphenation patterns

The following software may be included in this product: Dutch spelling dictionary and hyphenation patterns.
        Use of any of this software is governed by the terms of the license below:

1. Name: Dutch word list for spell checking - OpenTaal

2. Version of words list: 2.10G; version of spell checking: 2.10G.

3. Requirements: Hunspell 1.2.8 and higher

4. Spelling Seal of Dutch Language Union: The OpenTaal list of lemmas has received the Spelling Seal of
        Approval from the Dutch Language Union, the formal Dutch language institute. For more information please see:
        http://www.taalunieversum.org/keurmerk/

5. Copyrights: © 2006-2010 OpenTaal, © 2001-2005 Simon Brouwer e.a., © 1996 Nederlandstalige Tex
        Gebruikersgroep

6. Licenses: OpenTaal aims to create and publish free Dutch language files. To enable the broadest (re)use
        the language files are freely available under the below, liberal licenses at the discretion of the user. We
        strongly recommend to read the applicable license before usage.

   A. BSD (revised version):

   - Full license text: http://creativecommons.org/licenses/BSD/legalcode

   - Summary: http://creativecommons.org/licenses/BSD/deed.en

   B. Creative Commons, Attribution 3.0 (unported)

   - Full license text: http://creativecommons.org/licenses/by/3.0/legalcode

   - Summary: http://creativecommons.org/licenses/by/3.0/deed.en

7. Support OpenTaal: OpenTaal is a non-profit volunteer project. With your (small) financial support
        OpenTaal will further expand its activities and enhance its professionalism. Your donation is welcome at
        account number: 15.62.32.782, BIC: RABONL2U, IBAN: ****************** of Stichting OpenTaal / OpenTaal
        Foundation. In the Netherlands your donations are tax deductible. OpenTaal Foundation has been designated by
        the Dutch Tax Administration as an Institution for General Benefit (algemeen nut beogende instelling or ANBI).
        Please see: http://belastingdienst.nl/anbi/

8. Participate: Everyone is welcome to participate. Please give feedback, discuss on the mailing list or run
        Harvester. By contributing to the project you agree that your contribution is available under free or/open
        source licenses. In case you wish, your name will be mentioned on the website. Your are invited to send us your
        written request.

9. Rights of third parties: OpenTaal respects the rights of third parties and aims to keep its data freely
        available. Therefore you may no use protected sources of third parties, i.e. dictionaries, without their
        permission when you contribute to the project. It is permitted to use the materials of the Dutch Language
        Union, i.e. their spelling instruction and word list. In case you believe OpenTaal is violating your rights, we
        ask you to send us a written notice as soon as possible.

10.Contact: OpenTaal Foundation, http://www.opentaal.org,
        <EMAIL>

## German

### Spelling dictionary

The following software may be included in this product: German spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Author: Franz Michael Baumann <<EMAIL>>

License: GNU GPL Version 2 or GPL Version
        3 or OASIS 0.1

The "frami"-dictionary contains the complete word list of Björn Jacke's "igerman98"

(Version: 2011-03-21) and numerous supplements by Franz Michael Baumann according to the reform of
        2006-08-01.

Without any modifications this dictionary may be distributed with programs that support the OASIS Open
        Document Format for Office Applications and whose PRIMARY format for saving documents is the Open Document
        Format.

This requires that all licenses and copyright files are also distributed together with the package the
        dictionary is shipped with.

Any modifications of the dictionary files are not allowed for this agreement, modifications require the use
        of the GNU GENERAL PUBLIC LICENSE.

If you have questions or don't get along with this, send me your comments/questions/ideas to Bjoern Jacke
        <<EMAIL>>

Jump to GPL version 3

Jump to GPL version 2

### Hyphenation patterns

The following software may be included in this product: German hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Authors: <AUTHORS>

Version: 2011-05-05 (author and license information in source file added)

License: GNU LGPL

Jump to LGPL Version 2.1

### Thesaurus

The following software may be included in this product: German thesaurus. Use of any of this software is
        governed by the terms of the license below:

OpenThesaurus - Deutscher Thesaurus - Synonyme und Assoziationen

Version: 2011-05-04 AT

License: GNU LGPL

Jump to LGPL Version 2.1

## Greek

### Spelling dictionary

The following software may be included in this product: Greek spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

GPL 2.0/LGPL 2.1/MPL 1.1
        tri-license

### Hyphenation patterns

The following software may be included in this product: Greek hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Jump to LGPL Version 2.1

## English (Australia)

### Spelling dictionary

The following software may be included in this product: English (Australia) spelling dictionary. Use of any
        of this software is governed by the terms of the license below:

Copyright (C) 2006  Cameron Roy

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version.

Jump to GPL version 2

## English (Canada)

### Spelling dictionary

The following software may be included in this product: English (Canada) spelling dictionary. Use of any of
        this software is governed by the terms of the license below:

Wordlist en_CA spelling and morphological dictionary for OpenOffice.org

Version 2008-12-18

 

Based on Wordlist Hunspell dictionaries version 2008-12-05

and Wordlist POS and AGID data created by Kevin Atkinson

and released on http://wordlist.sourceforge.net.

 

Other fixes:

 

OOo Issue 48060 - add numbers with affixes by COMPOUNDRULE (1st, 111th, 1990s etc.)

New REP items (better suggestions for accented words and a few mistakes)

OOo Issue 63541 - remove *dessicated, *dessication

 

László Németh <nemeth at OO.o>

 

Original license:

 

2008-12-05 Release

 

README file for en_US and en_CA Hunspell dictionaries

 

These dictionaries are created using the speller/make-hunspell-dict

dictionary in SCOWL, SVN revision 74.

 

The NOSUGGEST flag was added to certain taboo words.  While I made an

honest attempt to flag the strongest taboo words with the NOSUGGEST

flag, I MAKE NO GUARANTEE THAT I FLAGGED EVERY POSSIBLE TABOO WORD.

The list was originally derived from Németh László, however I removed

some words which, while being considered taboo by some dictionaries,

are not really considered swear words in today's society.

 

You can find SCOWL and friend at http://wordlist.sourceforge.net/.

Bug reports should go to the Issue Tracker found on the previously

mentioned web site.  General discussion should go to the

wordlist-devel at sourceforge net mailing list.

 

COPYRIGHT, SOURCES, and CREDITS:

 

The en_US and en_CA dictionaries come directly from SCOWL (up to level

60) and is thus under the same copyright of SCOWL.  The affix file is

a heavily modified version of the original english.aff file which was

released as part of Geoff Kuenning's Ispell and as such is covered by

his BSD license.  Part of SCOWL is also based on Ispell thus the

Ispell copyright is included with the SCOWL copyright.

 

The collective work is Copyright 2000-2007 by Kevin Atkinson as well

as any of the copyrights mentioned below:

 

  Copyright 2000-2007 by Kevin Atkinson

 

  Permission to use, copy, modify, distribute and sell these word

  lists, the associated scripts, the output created from the scripts,

  and its documentation for any purpose is hereby granted without fee,

  provided that the above copyright notice appears in all copies and

  that both that copyright notice and this permission notice appear in

  supporting documentation. Kevin Atkinson makes no representations

  about the suitability of this array for any purpose. It is provided

  "as is" without express or implied warranty.

 

Alan Beale <<EMAIL>> also deserves special credit as he has,

in addition to providing the 12Dicts package and being a major

contributor to the ENABLE word list, given me an incredible amount of

feedback and created a number of special lists (those found in the

Supplement) in order to help improve the overall quality of SCOWL.

 

The 10 level includes the 1000 most common English words (according to

the Moby (TM) Words II [MWords] package), a subset of the 1000 most

common words on the Internet (again, according to Moby Words II), and

frequently class 16 from Brian Kelk's "UK English Wordlist

with Frequency Classification".

 

The MWords package was explicitly placed in the public domain:

 

    The Moby lexicon project is complete and has

    been place into the public domain. Use, sell,

    rework, excerpt and use in any way on any platform.

 

    Placing this material on internal or public servers is

    also encouraged. The compiler is not aware of any

    export restrictions so freely distribute world-wide.

 

    You can verify the public domain status by contacting

 

    Grady Ward

    3449 Martha Ct.

    Arcata, CA  95521-4884

 

    <EMAIL>

    <EMAIL>

 

The "UK English Wordlist With Frequency Classification" is also in the

Public Domain:

 

  Date: Sat, 08 Jul 2000 20:27:21 +0100

  From: Brian Kelk <<EMAIL>>

 

  > I was wondering what the copyright status of your "UK English

  > Wordlist With Frequency Classification" word list as it seems to

  > be lacking any copyright notice.

 

  There were many many sources in total, but any text marked

  "copyright" was avoided. Locally-written documentation was one

  source. An earlier version of the list resided in a filespace called

  PUBLIC on the University mainframe, because it was considered public

  domain.

 

  Date: Tue, 11 Jul 2000 19:31:34 +0100

 

  > So are you saying your word list is also in the public domain?

 

  That is the intention.

 

The 20 level includes frequency classes 7-15 from Brian's word list.

 

The 35 level includes frequency classes 2-6 and words appearing in at

least 11 of 12 dictionaries as indicated in the 12Dicts package.  All

words from the 12Dicts package have had likely inflections added via

my inflection database.

 

The 12Dicts package and Supplement is in the Public Domain.

 

The WordNet database, which was used in the creation of the

Inflections database, is under the following copyright:

 

  This software and database is being provided to you, the LICENSEE,

  by Princeton University under the following license.  By obtaining,

  using and/or copying this software and database, you agree that you

  have read, understood, and will comply with these terms and

  conditions.:

 

  Permission to use, copy, modify and distribute this software and

  database and its documentation for any purpose and without fee or

  royalty is hereby granted, provided that you agree to comply with

  the following copyright notice and statements, including the

  disclaimer, and that the same appear on ALL copies of the software,

  database and documentation, including modifications that you make

  for internal use or for distribution.

 

  WordNet 1.6 Copyright 1997 by Princeton University.  All rights

  reserved.

 

  THIS SOFTWARE AND DATABASE IS PROVIDED "AS IS" AND PRINCETON

  UNIVERSITY MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR

  IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PRINCETON

  UNIVERSITY MAKES NO REPRESENTATIONS OR WARRANTIES OF MERCHANT-

  ABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF THE

  LICENSED SOFTWARE, DATABASE OR DOCUMENTATION WILL NOT INFRINGE ANY

  THIRD PARTY PATENTS, COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.

 

  The name of Princeton University or Princeton may not be used in

  advertising or publicity pertaining to distribution of the software

  and/or database.  Title to copyright in this software, database and

  any associated documentation shall at all times remain with

  Princeton University and LICENSEE agrees to preserve same.

 

The 40 level includes words from Alan's 3esl list found in version 4.0

of his 12dicts package.  Like his other stuff the 3esl list is also in the

public domain.

 

The 50 level includes Brian's frequency class 1, words appearing

in at least 5 of 12 of the dictionaries as indicated in the 12Dicts

package, and uppercase words in at least 4 of the previous 12

dictionaries.  A decent number of proper names is also included: The

top 1000 male, female, and Last names from the 1990 Census report; a

list of names sent to me by Alan Beale; and a few names that I added

myself.  Finally a small list of abbreviations not commonly found in

other word lists is included.

 

The name files form the Census report is a government document which I

don't think can be copyrighted.

 

The file special-jargon.50 uses common.lst and word.lst from the

Unofficial Jargon File Word Lists" which is derived from "The Jargon

.  All of which is in the Public Domain.  This file also contain

a few extra UNIX terms which are found in the file "unix-terms" in the

special/ directory.

The 55 level includes words from Alan's 2of4brif list found in version

4.0 of his 12dicts package.  Like his other stuff the 2of4brif is also

in the public domain.

 

The 60 level includes Brian's frequency class 0 and all words

appearing in at least 2 of the 12 dictionaries as indicated by the

12Dicts package.  A large number of names are also included: The 4,946

female names and the 3,897 male names from the MWords package.

 

The 70 level includes the 74,550 common dictionary words and the

21,986 names list from the MWords package The common dictionary words,

like those from the 12Dicts package, have had all likely inflections

added.  The 70 level also included the 5desk list from version 4.0 of

the 12Dics package which is the public domain

 

The 80 level includes the ENABLE word list, all the lists in the

ENABLE supplement package (except for ABLE), the "UK Advanced Cryptics

(UKACD), the list of signature words in from YAWL package,

and the 10,196 places list from the MWords package.

 

The ENABLE package, mainted by M\Cooper <<EMAIL>>,

is in the Public Domain:

 

  The ENABLE master word list, WORD.LST, is herewith formally released

  into the Public Domain. Anyone is free to use it or distribute it in

  any manner they see fit. No fee or registration is required for its

  use nor are "contributions" solicited (if you feel you absolutely

  must contribute something for your own peace of mind, the authors of

  the ENABLE list ask that you make a donation on their behalf to your

  favorite charity). This word list is our gift to the Scrabble

  community, as an alternate to "official" word lists. Game designers

  may feel free to incorporate the WORD.LST into their games. Please

  mention the source and credit us as originators of the list. Note

  that if you, as a game designer, use the WORD.LST in your product,

  you may still copyright and protect your product, but you may *not*

  legally copyright or in any way restrict redistribution of the

  WORD.LST portion of your product. This *may* under law restrict your

  rights to restrict your users' rights, but that is only fair.

 

UKACD, by J Ross Beresford <<EMAIL>>, is under the

following copyright:

 

  Copyright (c) J Ross Beresford 1993-1999. All Rights Reserved.

 

  The following restriction is placed on the use of this publication:

  if The UK Advanced Cryptics Dictionary is used in a software package

  or redistributed in any form, the copyright notice must be

  prominently displayed and the text of this document must be included

  verbatim.

 

  There are no other restrictions: I would like to see the list

  distributed as widely as possible.

 

The 95 level includes the 354,984 single words and 256,772 compound

words from the MWords package, ABLE.LST from the ENABLE Supplement,

and some additional words found in my part-of-speech database that

were not found anywhere else.

 

Accent information was taken from UKACD.

 

My VARCON package was used to create the American, British, and

Canadian word list.

 

Since the original word lists used in the VARCON package came

from the Ispell distribution they are under the Ispell copyright:

 

  Copyright 1993, Geoff Kuenning, Granada Hills, CA

  All rights reserved.

 

  Redistribution and use in source and binary forms, with or without

  modification, are permitted provided that the following conditions

  are met:

 

  1. Redistribution of source code must retain the above copyright

     notice, this list of conditions and the following disclaimer.

  2. Redistribution in binary form must reproduce the above copyright

     notice, this list of conditions and the following disclaimer in the

     documentation and/or other materials provided with the distribution.

  3. All modifications to the source code must be clearly marked as

     such.  Binary redistribution based on modified source code

     must be clearly marked as modified versions in the documentation

     and/or other materials provided with the distribution.

  (clause 4 removed with permission from Geoff Kuenning)

  5. The name of Geoff Kuenning may not be used to endorse or promote

     products derived from this software without specific prior

     written permission.

 

  THIS SOFTWARE IS PROVIDED BY GEOFF KUENNING AND CONTRIBUTORS ``AS

  IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT

  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS

  FOR A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL GEOFF

  KUENNING OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,

  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,

  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;

  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER

  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT

  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN

  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE

  POSSIBILITY OF SUCH DAMAGE.

## English (South Africa)

### Spelling dictionary

The following software may be included in this product: English (South Africa) spelling dictionary. Use of
        any of this software is governed by the terms of the license below:

Jump to LGPL Version 2.1

## English (United Kingdom)

### Spelling dictionary

The following software may be included in this product: English (United Kingdom) spelling dictionary. Use of
        any of this software is governed by the terms of the license below:

Jump to LGPL Version 2.1

### Hyphenation patterns

The following software may be included in this product: English (United Kingdom) hyphenation patterns. Use
        of any of this software is governed by the terms of the license below:

BSD-style. Unlimited copying, redistribution and modification of this file is permitted with this copyright
        and license information.

## English (USA)

### Spelling dictionary

The following software may be included in this product: English (USA) spelling dictionary. Use of any of
        this software is governed by the terms of the license below:

See English (Canada).

### Hyphenation patterns

The following software may be included in this product: English (USA) hyphenation patterns. Use of any of
        this software is governed by the terms of the license below:

BSD-style. Unlimited copying, redistribution and modification of this file
        is permitted with this copyright and license information.

### Thesaurus

The following software may be included in this product: English (USA) thesaurus. Use of any of this software
        is governed by the terms of the license below:

WordNet Release 2.1

This software and database is being provided to you, the LICENSEE, by Princeton University under the
        following license. By obtaining, using and/or copying this software and database, you agree that you have read,
        understood, and will comply with these terms and conditions.:

Permission to use, copy, modify and distribute this software and database and its documentation for any
        purpose and without fee or royalty is hereby granted, provided that you agree to comply with the following
        copyright notice and statements, including the disclaimer, and that the same appear on ALL copies of the
        software, database and documentation, including modifications that you make for internal use or for
        distribution.

WordNet 2.1 Copyright 2005 by Princeton University. All rights reserved.

THIS SOFTWARE AND DATABASE IS PROVIDED "AS IS" AND PRINCETON UNIVERSITY MAKES NO REPRESENTATIONS OR
        WARRANTIES, EXPRESS OR IMPLIED. BY WAY OF EXAMPLE, BUT NOT LIMITATION, PRINCETON UNIVERSITY MAKES NO
        REPRESENTATIONS OR WARRANTIES OF MERCHANT- ABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF THE
        LICENSED SOFTWARE, DATABASE OR DOCUMENTATION WILL NOT INFRINGE ANY THIRD PARTY PATENTS, COPYRIGHTS, TRADEMARKS
        OR OTHER RIGHTS.

The name of Princeton University or Princeton may not be used in advertising or publicity pertaining to
        distribution of the software and/or database. Title to copyright in this software, database and any associated
        documentation shall at all times remain with Princeton University and LICENSEE agrees to preserve same.

### English sentence checker for LibreOffice

The following software may be included in this product: English sentence checker. Use of any of this
        software is governed by the terms of the license below:

2011-2012 (c) László Németh, license: MPL 1.1 / GPLv3+ / LGPLv3+

Jump to GPL Version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

## Estonian

### Spelling dictionary

The following software may be included in this product: Estonian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Dictionary is adapted to myspell by Jaak Pruulmann (<EMAIL>, http://www.meso.ee/~jjpp/speller/ ) on the base of word list created
        and shared by IEL (Institute of the Estonian Language, <EMAIL>). The original of EKI software license
        is available at http://www.eki.ee/eki/licence.html. The work
        of Jaak Pruulmann is licensed under LGPL (GNU Lesser General Public License). The copies of licenses are added
        to current file. IEL is informed about the use of this word list.

Institute of the Estonian Language

Copyright © Institute of the Estonian Language

E-mail: <EMAIL>

URL: http://www.eki.ee/tarkvara/

The present License Agreement gives the user of this Software Product (hereinafter: Product) the right to
        use the Product for whatever purpose (incl. distribution, copying, altering, inclusion in other software, and
        selling) on the following conditions:

The present License Agreement should belong unaltered to each copy ever made of this Product;

Neither the Institute of the Estonian Language (hereinafter: IEL) nor the author(s) of the Product will take
        responsibility for any detriment, direct or indirect, possibly ensuing from the application of the Product;

The IEL is ready to share the Product with other users as we wish to advance research on the Estonian
        language and to promote the use of Estonian in IT-technology now rapidly developing, yet we refuse to bind
        ourselves to any further obligation, which means that the IEL is not obliged either to warrant the suitability
        of the Product for a concrete use, to improve the program, or to provide a more detailed description of the
        underlying algorithms. (Which does not mean, though, that we may not do it.)

Whenever you use the Product, we request that you inform us by writing to the e-<NAME_EMAIL>
        or to street address listed below.

Institute of the Estonian Language

Roosikrantsi 6
        EE-10119 Tallinn
        ESTONIA

E-mail: <EMAIL>
        Phone & Fax: +372-6411443

Jump to LGPL Version 2.1

### Hyphenation patterns

The following software may be included in this product: Estonian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Hyphenation file is adapted to OpenOffice.org by Jaak Pruulmann (<EMAIL>, http://www.meso.ee/~jjpp/speller/) on the base of the LaTeX hyphenation
        file created by Enn Saar (<EMAIL>), who has signed the JCA (Joint Copyright Agreement) allowing to use his
        work for OpenOffice.org. The original file is available at address http://www.cs.ut.ee/~tqnu/eehyph.tex and in the heading of the file
        it is written that this file is licensed under LPPL. The work of Jaak Pruulmann is licensed under LGPL (GNU
        Lesser General Public License).

Jump to LPPL

Jump to LGPL Version 2.1

## French

### Spelling dictionary

The following software may be included in this product: French spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Olivier R. - dicollecte<at>free<dot>fr

Dicollecte: http://www.dicollecte.org/

Licenses:

MPL: Mozilla Public License version 1.1 or
            higher 

GPL: GNU General Public License version 2.0 or
            higher 

LGPL: GNU Lesser General Public License
            version 2.1 or higher 

### Hyphenation patterns

The following software may be included in this product: French hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Origin: Based on the TeX hyphenation tables frhyph.tex (V2.12) <2002/12/11> http://mirror.ctan.org/language/hyphenation/frhyph.tex

The TeX hyphenation tables are released under the LaTeX Project Public License (LPPL)

Jump to LPPL

License: OpenOffice.org adaptions of this package are licensed under the GNU Lesser General Public License
        (LGPL) version 2.1 or higher.

Jump to LGPL Version 2.1

Author: Conversion author is Paul Pichaureau <<EMAIL>>

Based on a previous conversion by Blaise Drayer <<EMAIL>>

### Thesaurus

The following software may be included in this product: French thesaurus. Use of any of this software is
        governed by the terms of the license below:

Licence: LGPL: GNU Lesser General Public License version 2.1 or higher.

Jump to LGPL Version 2.1

## Galician

### Spelling dictionary

The following software may be included in this product: Galician spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

This extension was made by Frco. Javier Rial Rodríguez for Mancomún, Centro de Referencia e Servizos de
        Software Libre 2008

Spellchecker files (gl_ES.aff, gl_ES.dic) from Mar Castro Pereiro also developed for Mancomún.

Distributed under the GPL License.

Jump to GPL Version 3

## Guarani

### Spelling dictionary

The following software may be included in this product: Guarani spelling dictionary.

### Thesaurus

The following software may be included in this product: Guarani thesaurus. Use of any of this software is
        governed by the terms of the license below:

Permission is granted to copy, distribute and/or modify this document under the terms of the GNU Free
        Documentation License, Version 1.2 or any later version published by the Free Software Foundation; with no
        Invariant Sections, no Front-Cover Texts, and no Back-Cover Texts. A copy of the license is included in the
        section entitled "GNU Free Documentation License".

## Gujarati

### Spelling dictionary

The following software may be included in this product: Gujarati spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL Version 3

## Hebrew

### Spelling dictionary

The following software may be included in this product: Hebrew spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

This dictionary is Copyright (C) 2000-2009, Nadav Har'El (<EMAIL>) and Dan Kenigsberg
        (<EMAIL>).

It is licensed under the GNU General Public License (GPL).

Jump to GPL Version 3

## Hindi

The following software may be included in this product: Hindi spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

GNU Aspell Hindi Word List Package
        Copyright © 2005 Swapnil {Hajare, Sant} <<EMAIL>>

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version.

Conversion made by Laszlo Nemeth, for demonstration of Hunspell Unicode support.

Jump to GPL Version 2

## Hungarian

### Spelling dictionary

The following software may be included in this product: Hungarian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

MPLv2 and LesserGPLv3+

The contents of this software may be used under the terms of the GNU Lesser General Public License Version 3
        or later (the "LGPL", see COPYING.LGPL) or the Mozilla Public License Version 2.0 or later (the "MPL", see
        COPYING.MPL in the root folder of the source tree).

Software distributed under these licenses is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND,
        either express or implied. See the licences for the specific language governing rights and limitations under
        the licenses.

2018 (c) László Németh & Ferenc Godó

Jump to LGPL Version 3

Jump to MPL Version 2

### Hyphenation patterns

The following software may be included in this product: Hungarian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright © Bence Nagy <gimb (at) freemail (dot) hu>, 2006

Original license: GPL v2

Patch author: László Németh <nemeth (at) OOo>

Patch license: MPL/GPL/LGPL

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

### Thesaurus

The following software may be included in this product: Hungarian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2009, László Németh

Jump to GPL Version 2

### Hungarian sentence checker for LibreOffice

The following software may be included in this product: Hungarian sentence checker. Use of any of this
        software is governed by the terms of the license below:

2009-2012 (c) László Németh, license: MPL 1.1 / GPLv3+ / LGPLv3+

Jump to GPL Version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

## Indonesian

### Spelling dictionary

The following software may be included in this product: Indonesian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to LGPL Version 3

### Hyphenation patterns

The following software may be included in this product: Indonesian hyphenation patterns.

### Thesaurus

The following software may be included in this product: Indonesian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Jump to LGPL Version 2.1

## Icelandic

### Spelling dictionary, hyphenation and thesaurus

The wordlist was developed by Orðabók Háskólans in cooperation with Reiknistofnun Háskóla Íslands in
        the early nineties and was released into the public domain. Further modifications to the wordlist are also
        released into the public domain.

The thesaurus and words in the spell checker with additional morphological information are from the
        Icelandic Wiktionary Project, http://is.wiktionary.org. Works released by the Wiktionary project are under the
        Creative Commons Attribution-ShareAlike 3.0 Unported license.

The hyphenation rules were developed by Icelandic Language Institute which now is a part of the Árni
        Magnússon Institute for Icelandic Studies. The rules were made available at http://www.malfong.is/index.php?lang=en&pg=hyphen
        under a Creative Commons Attribution 4.0 International license (CC BY 4.0).

You are free:
           to Share — to copy, distribute and transmit the work
           to Remix — to adapt the work
           to make commercial use of the work
        Under the following conditions:
           Attribution — You must attribute the work in the manner specified by the author or licensor (but not in
        any way that suggests that they endorse you or your use of the work).
           Share Alike — If you alter, transform, or build upon this work, you may distribute the resulting work
        only under the same or similar license to this one.

Jump to CC-BY-SA 3.0

## Italian

### Spelling dictionary

The following software may be included in this product: Italian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2001, 2002 Gianluca Turconi
        Copyright (C) 2002, 2003, 2004 Gianluca Turconi and Davide Prina
        Copyright (C) 2004, 2005, 2006, 2007  Davide Prina
        Copyright (C) 2010  Andrea Pescetti

Jump to GPL Version 3

### Hyphenation patterns

The following software may be included in this product: Italian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Origin:   Based on the TeX hyphenation tables by Claudio Beccari

Author:   conversion author is Giuseppe Bilotta <<EMAIL>>

Jump to LGPL Version 3

### Thesaurus

The following software may be included in this product: Italian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2004,2005,2006,2007,2008 Daniela Volta
        Copyright (C) 2008 Giovanni Zuliani
        Copyright (C) 2006,2007,2008 Davide Prina
        Copyright (C) 2010  Andrea Pescetti

Jump to GPL Version 3

## Kurdish

### Spelling dictionary

The following software may be included in this product: Kurdish spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

(C) Copyright Reimar Heider <hunspell at ferheng dot org>

With contributions from Kevin P. Scannell <<EMAIL>> and Rêzan Tovjîn

The original word list used for this package was augmented using Kevin Scannell's web crawling software "An
        Crúbadán" and then hand-checked by Ronahi and Tovjîn.

Originally GPL, relicensed on 04-07-2007 to GPLv3, LGPLv3, MPL 1.1

Jump to GPL Version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

## Lao

### Spelling dictionary

The following software may be included in this product: Lao spelling dictionary. Use of any of this software
        is governed by the terms of the license below:

Copyright (C) 2013 by Brian Eugene Wilson, Robert Martin Campbell

Jump to LGPL Version 2.1

## Latvian

### Spelling dictionary

The following software may be included in this product: Latvian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2002-2010 Janis Eisaks, <EMAIL>, http://dict.dv.lv

Jump to LGPL Version 2.1

### Hyphenation patterns

The following software may be included in this product: Latvian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2004-2005 Jânis Vilims, <EMAIL>

Jump to LGPL Version 2.1

### Thesaurus

The following software may be included in this product: Latvian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2002-2010 Janis Eisaks, <EMAIL>, http://dict.dv.lv

Jump to LGPL Version 2.1

## Lithuanian

### Spelling dictionary

The following software may be included in this product: Lithuanian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (c) Albertas Agejevas <<EMAIL>>, 2000, 2001. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistribution of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer. 

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution. 

Neither the name of the Albertas Agejevas nor the names of its contributors may be used to endorse or
            promote products derived from this software without specific prior written permission. 

THIS SOFTWARE IS PROVIDED BY ALBERTAS AGEJEVAS AND CONTRIBUTORS ``AS IS'' AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL ALBERTAS AGEJEVAS OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

### Hyphenation patterns

The following software may be included in this product: Lithuanian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Origin: TeX hyphenation tables by Sigitas Tolusis and Vytas Statulevicius. The original tables can be found
        at http://www.vtex.lt/tex/download/zip/texmf.zip
         as lthyphen.tex.

Author: Converted to OOo format by Albertas Agejevas <<EMAIL>>

License: LaTeX Project Public Licence

Jump to LPPL

## Nepali

### Spelling dictionary

The following software may be included in this product: Nepali spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Compiled by Madan Puraskar Pustakalaya

Jump to LGPL Version 2.1

### Thesaurus

The following software may be included in this product: Nepali thesaurus. Use of any of this software is
        governed by the terms of the license below:

Compiled by Madan Puraskar Pustakalaya

Jump to LGPL Version 2.1

## Norwegian

### Spelling dictionary

The following software may be included in this product: Norwegian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL Version 2

### Hyphenation patterns

The following software may be included in this product: Norwegian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL Version 2

### Thesaurus

The following software may be included in this product: Norwegian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Jump to GPL Version 2

## Occitan

### Spelling dictionary

The following software may be included in this product: Occitan spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2006 Bruno GALLART

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version.

Jump to GPL Version 2

## Polish

### Spelling dictionary

The following software may be included in this product: Polish spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

This dictionary for spell-checking Polish texts is licensed under GPL, LGPL, MPL (Mozilla Public License)
        and Creative Commons ShareAlike licenses (see http://creativecommons.org/licenses/sa/1.0).

### Hyphenation patterns

The following software may be included in this product: Polish hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Origin:   Based on the TeX hyphenation patterns plhyph.tex, version 3.0a, Wednesday, May 17th, 1995. The
        original file is in CTAN archives, for example here:http://ctan.binkerton.com/ctan.readme.php?filename=language/polish/plhyph.tex
        and is licensed under LPPL.

License: OpenOffice.org Adaptions of this package are licensed under the GNU LGPL license.

Author: conversion and corrects author is Artur Polaczyński <<EMAIL>>

Jump to LGPL Version 3

### Thesaurus

The following software may be included in this product: Polish thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (C) 2004-2008 Marcin Miłkowski <<EMAIL>>

This product is made available subject to the terms of GNU Lesser General Public License Version 2.1.

Jump to LGPL Version 2.1

## Portuguese

### Spelling dictionary

The following software may be included in this product: Portuguese spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2006  Jose Joao de Almeida, Rui Vilela, Alberto Simões

Dep. Informatica, Universidade do Minho, Portugal

GPL 2.0/LGPL 2.1/MPL 1.1 tri-license

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Portuguese hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Based on the TeX hyphenation tables by Pedro J. de Rezende (Brazilian) and tuned up by J.Joao Dias
        Almeida

Author: conversion author is Paulo Morgado

Jump to GPL Version 2

### Thesaurus

The following software may be included in this product: Portuguese thesaurus. Use of any of this software is
        governed by the terms of the license below:

Jump to GPL Version 2

## Portuguese (Brazilian)

### Spelling dictionary

The following software may be included in this product: Portuguese (Brazilian) spelling dictionary. Use of
        any of this software is governed by the terms of the license below:

This dictionary is under continuous development by Raimundo Moura and his team. It is icensed under the
        terms of the GNU Lesser General Public License version 3 (LGPLv3), as published by the Free Software
        Foundation, and Mozilla Public License as published by The Mozilla Foundation.  The credits are available at
        http://pt-br.libreoffice.org/projetos/projeto-vero-verificador-ortografico/
        and you can find new releases at http://extensions.libreoffice.org

Copyright (C) 2006 - 2013 by Raimundo Santos Moura <<EMAIL>>

Jump to LGPL Version 3

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Portuguese (Brazilian) hyphenation patterns. Use of
        any of this software is governed by the terms of the license below:

These hyphenation patterns are under continuous development by Raimundo Moura and his team. It is icensed
        under the terms of the GNU Lesser General Public License version 3 (LGPLv3), as published by the Free Software
        Foundation, and Mozilla Public License as published by The Mozilla Foundation.  The credits are available at
        http://pt-br.libreoffice.org/projetos/projeto-vero-verificador-ortografico/
        and you can find new releases at http://extensions.libreoffice.org

Copyright (C) 2006 - 2013 by Raimundo Santos Moura <<EMAIL>>

Jump to LGPL Version 3

Jump to MPL Version 1.1

### Portuguese Brazilian sentence checker for LibreOffice

The following software may be included in this product: Portuguese (Brazilian) sentence checker. Use of any
        of this software is governed by the terms of the license below:

MPL 1.1 / GPLv3+ / LGPLv3+

2013 © Raimundo Santos Moura

Jump to GPL Version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

## Romanian

### Spelling dictionary

The following software may be included in this product: Romanian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

GPL 2.0/LGPL 2.1/MPL 1.1 tri-license.

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Romanian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

GNU General Public License Version 2

Jump to GPL Version 2

### Thesaurus

The following software may be included in this product: Romanian thesaurus. Use of any of this software is
        governed by the terms of the license below:

GNU General Public License Version 2 or later

Jump to GPL Version 2

## Russian

### Spelling dictionary

The following software may be included in this product: Russian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (c) 1997-2008, Alexander I. Lebedev

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
        the following conditions are met:

Redistribution of source code must retain the above copyright notice, this list of conditions and the
            following disclaimer. 

Redistribution in binary form must reproduce the above copyright notice, this list of conditions and
            the following disclaimer in the documentation and/or other materials provided with the distribution. 

Modified versions must be clearly marked as such. 

The name of Alexander I. Lebedev may not be used to endorse or promote products derived from this
            software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
        WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
        OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
        DAMAGE.

### Hyphenation patterns

The following software may be included in this product: Russian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

unknown

### Thesaurus

The following software may be included in this product: Russian thesaurus. Use of any of this software is
        governed by the terms of the license below:

License: GNU LGPL

Author: Mikhail Korolyov <<EMAIL>>

Origin: Абрамовъ, Н. Словарь русскихъ синонимовъ и сходныхъ по
        смыслу выраженiй. Изд. 3-е, доп., Пг., 1911

### Russian grammar checker

The following software may be included in this product: Russian grammar checker. Use of any of this software
        is governed by the terms of the license below:

2009, 2011, 2012 (c) Yakov Reztsov <yr at myooo dot ru>, license: MPL 1.1 / GPL / LGPL

## Scottish Gaelic

### Spelling dictionary

The following software may be included in this product: Scottish Gaelic spelling dictionary. Use of any of
        this software is governed by the terms of the license below:

Jump to GPL Version 3

## Serbian

### Spelling dictionary

The following software may be included in this product: Serbian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Serbian spelling dictionary is developed by Milutin Smiljanic <<EMAIL>> and is
        released under GNU LGPL version 3 or later / MPL version 2 or later / GNU GPL version 3 or later, giving
        you the choice of one of the three sets of free software licensing terms.

### Hyphenation patterns

The following software may be included in this product: Serbian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Serbian hyphenation patterns (files hyph_sr.dic and hyph_sh.dic) are derived from the official TeX patterns
        for Serbocroatian language (Cyrillic and Latin) created by Dejan Muhamedagić <<EMAIL>>
        version 2.02 released on 22 June 2008. The format is adopted for usage with Hyphen hyphenation library and is
        released again as hyphen-sr under the compatible GNU LGPL version 2.1 or later.

Jump to LGPL Version 2.1

## Sinhala

### Spelling dictionary

The following software may be included in this product: Sinhala spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) <2010>  Laknath Semage <blaknath [at] gmail [dot] com>

Jump to GPL Version 3

## Slovak

### Spelling dictionary

The following software may be included in this product: Slovak spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Data are released under these licenses (you can select one):

The GNU General Public License (GPL) Version 2, June 1991 

GNU Lesser General Public License Version 2.1, February 1999 

Mozilla Public License 1.1 (MPL 1.1) 

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Slovak hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Dictionary is created by converting TeX hyphenation patterns for Slovak (Author: Jana Chlebíková) with
        lingucomponent-tools (http://cvs.sourceforge.net/cgi-bin/viewcvs.cgi/oo-cs/lingucomponent-tools/).

Original license: LPPL

Jump to LPPL

### Thesaurus

The following software may be included in this product: Slovak thesaurus. Use of any of this software is
        governed by the terms of the license below:

Copyright (c) 2004-2010 Tibor Bako, yorik (at) szm.sk, Zdenko Podobný, zposter (at) gmail.com

Permission is hereby granted, free of charge, to any person obtaining a copy of this data (the "Data"), to
        deal in the Data without restriction, including without limitation the rights to use, copy, modify, merge,
        publish, distribute, sublicense, and/or sell copies of the Data, and to permit persons to whom the Data is
        furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
        portions of the Data.

THE DATA ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
        TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
        THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
        CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE DATA OR THE USE OR OTHER DEALINGS
        IN THE DATA.

## Slovenian

### Spelling dictionary

The following software may be included in this product: Slovenian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL Version 2

Jump to LGPL Version 2.1

### Hyphenation patterns

The following software may be included in this product: Slovenian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Jump to GPL Version 2

Jump to LGPL Version 2.1

### Thesaurus

The following software may be included in this product: Slovenian thesaurus. Use of any of this software is
        governed by the terms of the license below:

Jump to LGPL Version 2.1

## Spanish

### Spelling dictionary

The following software may be included in this product: Spanish spelling dictionary. Use of any of this
        software is governed by the terms of the licenses (or any later versions) below:

Jump to GPL version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Spanish hyphenation patterns. Use of any of this
        software is governed by the terms of the licenses (or any later versions) below:

Jump to GPL version 3

Jump to LGPL Version 3

Jump to MPL Version 1.1

### Thesaurus

The following software may be included in this product: Spanish thesaurus. Use of any of this software is
        governed by the terms of the license below:

Jump to LGPL Version 2.1

## Swahili

### Spelling dictionary

The following software may be included in this product: Swahili spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2004 Jason M Githeko

Jump to LGPL Version 2.1

## Swahili

### Spelling dictionary

The following software may be included in this product: Swahili spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to LGPL Version 3

## Swedish

### Spelling dictionary

The following software may be included in this product: Swedish spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright © 2003-12 Göran Andersson <<EMAIL>>.

This dictionary is made available subject to the terms of GNU Lesser General Public License Version 3.

Jump to LGPL Version 3

### Thesaurus

The following software may be included in this product: Swedish thesaurus. Use of any of this software is
        governed by the terms of the license below:

This thesaurus was directly converted from Synlex. The synonyms will be displayed sorted by their voted
        synonym level.

The synlex file <synpairs.xml> license, You are free to use this file with the same license

Copyright (c) Viggo Kann KTH 2009

THIS MATERIAL IS PROVIDED AS IS, WITH ABSOLUTELY NO WARRANTY EXPRESSED OR IMPLIED.  ANY USE IS AT YOUR OWN
        RISK.

Permission is hereby granted to use or copy this program for any purpose, provided the above notices are
        retained on all copies. Permission to modify the code and to distribute modified code is granted, provided the
        above notices are retained, and a notice that the code was modified is included with the above copyright
        notice.

## Telugu

### Spelling dictionary

The following software may be included in this product: Telugu spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

GNU Hunspell Telugu  Word List Package adaptation

Copyright © 2010 adaptation to Hunspell, Arjuna Rao Chavala
        Copyright © 2005 Aspell Telugu word list released by IndLinux, Khadir

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version.

Jump to GPL Version 2

### Hyphenation patterns

The following software may be included in this product: Telugu hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright © 2009 Santhosh Thottingal

The Telugu Hyphenation Dictionary may be used under the terms of either the GNU General Public License
        Version 3 or later (the "GPL"), or the GNU Lesser General Public License Version 3 or later (the "LGPL")

Jump to GPL Version 3

Jump to LGPL Version 3

## Thai

### Spelling dictionary

The following software may be included in this product: Thai spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Jump to LGPL Version 2.1

## Tibetan

### Spelling dictionary

The following software may be included in this product: Classical Tibetan syllable spellchecker for
        Hunspell. Use of any of this software is governed by the terms of the license below:

This work and the derived files are under the Creative Commons CC0 license (Public Domain).

## Turkish

### Spelling dictionary

The following software may be included in this product: Turkish spellchecker dictionary. Use of any of this
        software is governed by the terms of the license below:

This dictionary is released under Mozilla Public License Version 2.0.

Jump to MPL Version 2

## Ukrainian

### Spelling dictionary

The following software may be included in this product: Ukrainian spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 1999 Vladimir Yakovchuk, Oleg Podgurniy
        Copyright (C) 2001 Dmytro Kovalyov, Maksym Polyakov, Andriy Rysin
        Copyright (C) 2002 Valentyn Solomko, Volodymyr M. Lisivka
        Copyright (C) 2005 Andriy Rysin, Eugeniy Meshcheryakov, Dmytro Kovalyov
        Copyright (C) 2006-2009 Andriy Rysin

This dictionary is licensed under GPL 2.0 or above, LGPL 2.1 or above and MPL (Mozilla Public License) 1.1
        licenses.

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

### Hyphenation patterns

The following software may be included in this product: Ukrainian hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright 1998-2002 Maksym Polyakov.

License: GNU General Public License version 2 or any later version

Jump to GPL Version 2

### Thesaurus

The following software may be included in this product: Ukrainian thesaurus. Use of any of this software is
        governed by the terms of the license below:

This thesaurus is based on: П. М. Деркач, Короткий словник синонімів
        української мови, Радянська школа, Київ, 1960 С. Караванський,
        Пошук українського слова

Copyright (C) 2009 Andriy Rysin

This thesaurus is licensed under GPL, LGPL and MPL (Mozilla Public License) licenses.

Jump to GPL Version 2

Jump to LGPL Version 2.1

Jump to MPL Version 1.1

## Vietnamese

### Spelling dictionary

The following software may be included in this product: Vietnamese spelling dictionary. Use of any of this
        software is governed by the terms of the license below:

Authors: <AUTHORS>
        Nguyễn Xuân Minh <<EMAIL>>

This spellchecker for OpenOffice was based on the Vietnamese Dictionary list from Professor Hồ Ngọc
        Đức

This spellchecker is released with GPLv2 license.

Jump to GPL Version 2

## Zulu

### Hyphenation patterns

The following software may be included in this product: Zulu hyphenation patterns. Use of any of this
        software is governed by the terms of the license below:

Copyright (C) 2005, 2007 Friedel Wolff

Jump to LGPL Version 2.1

# Artwork

## Breeze

Breeze icon theme from KDE to fit in with the Plasma 5 desktop.

Breeze Icon Theme has been developed by The KDE Visual Design Group. https://forum.kde.org/viewforum.php?f=285

Copyright 2014 Uri Herrera <<EMAIL>>

Copyright 2015 Andreas Kainz <<EMAIL>> and other contributors

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General
        Public License as published by the Free Software Foundation; either version 2 of the License, or (at your
        option) any later version.

This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the
        implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
        more details.

Jump to GPL Version 2

## Colibre

The Colibre Icon Theme in icons/

Original author: Andreas Kainz <<EMAIL>>

Icon theme is released under Creative Commons CC0 You are free to adapt and use them for commercial purposes
        without attributing the original author or source. Although not required, a link back to LibreOffice is
        appreciated.

For additional Information go to the Creative Commons webpage https://creativecommons.org/publicdomain/zero/1.0/

## Elementary

This theme is based on the Human icon theme for LibreOffice and uses resources from the elementary icon
        theme as well as original work from Shimmer Project. A summary of attributions are below, with further details
        available at https://github.com/rizmut/libreoffice-style-elementary/commits

Elementary theme for LibreOffice

libreoffice-style-elementary: Available under the terms of the GPL

Copyright 2018 by Rizal Muttaqin <<EMAIL>>

Copyright 2017 by Andreas Kainz <<EMAIL>>

Copyright 2015 by Simon Steinbeiss <<EMAIL>>, Pasi Lallinaho
                <<EMAIL>>

elementary(-xfce): Available under the terms of the GPL

Copyright 2008-2015 by Daniel Foré <<EMAIL>>

Copyright 2010-2015 by Simon Steinbeiss <<EMAIL>>

Human theme for LibreOffice (2015-05-31)

libreoffice-style-human: Available under the terms of the LGPL

Copyright 2006-2007 by Gabriel Hurley

humanity-icon-theme: Available under the terms of the GPL

Copyright 2009 by Daniel Foré <<EMAIL>>, Jonian Guveli
                <<EMAIL>>, K.Vishnoo Charan Reddy <<EMAIL>>

Copyright 2006, 2009 by Gabriel Hurley

Jump to GPL Version 3

## Karasa Jaga

Karasa Jaga Icon Theme for Sundara OS

Karasa Jaga icon theme is derived heavily from Oxygen icon theme, used for Sundara OS branding. LibreOffice
        Style Karasa Jaga is part of Karasa Jaga icon theme. For further information see at https://github.com/rizmut/libreoffice-style-karasa-jaga

The Oxygen Icon Theme

and others

This library is free software; you can redistribute it and/or modify it under the terms of the GNU
            Lesser General Public License as published by the Free Software Foundation; either version 3 of the
            License, or (at your option) any later version.

This library is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even
            the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General
            Public License for more details.

You should have received a copy of the GNU Lesser General Public License along with this library. If
            not, see <http://www.gnu.org/licenses/>.

Clarification:

The GNU Lesser General Public License or LGPL is written for software libraries in the first place. We
            expressly want the LGPL to be valid for this artwork library too.

KDE Oxygen theme icons is a special kind of software library, it is an artwork library, it's elements
            can be used in a Graphical User Interface, or GUI.

Source code, for this library means:

where they exist, SVG;

otherwise, if applicable, the multi-layered formats xcf or psd, or otherwise png.

The LGPL in some sections obliges you to make the files carry notices. With images this is in some cases
            impossible or hardly useful.

With this library a notice is placed at a prominent place in the directory containing the elements. You
            may follow this practice.

The exception in section 5 of the GNU Lesser General Public License covers the use of elements of this
            art library in a GUI.

kde-artists [at] kde.org

Jump to LGPL Version 3

## Sifr

This work is distributed under the terms of the Creative Commons BY-SA 3.0 license.

Jump to CC-BY-SA 3.0

## Sukapura

Sukapura Icon Theme

This work is distributed under the terms of Mozilla Public License version 2.0. To view a copy of this
            licence, visit https://www.mozilla.org/en-US/MPL/2.0/

Jump to MPL 2.0

# GNU LESSER GENERAL PUBLIC LICENSE

Version 3, 29 June 2007

Copyright © 2007 Free Software Foundation, Inc. <https://fsf.org/>

Everyone is permitted to copy and distribute verbatim copies of this license document, but changing it is
        not allowed.

This version of the GNU Lesser General Public License incorporates the terms and conditions of version 3 of
        the GNU General Public License, supplemented by the additional permissions listed below.

## 0. Additional Definitions.

As used herein, “this License” refers to version 3 of the GNU Lesser General Public License, and the
        “GNU GPL” refers to version 3 of the GNU General Public License.

“The Library” refers to a covered work governed by this License, other than an Application or a Combined
        Work as defined below.

An “Application” is any work that makes use of an interface provided by the Library, but which is not
        otherwise based on the Library. Defining a subclass of a class defined by the Library is deemed a mode of using
        an interface provided by the Library.

A “Combined Work” is a work produced by combining or linking an Application with the Library. The
        particular version of the Library with which the Combined Work was made is also called the “Linked
        Version”.

The “Minimal Corresponding Source” for a Combined Work means the Corresponding Source for the Combined
        Work, excluding any source code for portions of the Combined Work that, considered in isolation, are based on
        the Application, and not on the Linked Version.

The “Corresponding Application Code” for a Combined Work means the object code and/or source code for
        the Application, including any data and utility programs needed for reproducing the Combined Work from the
        Application, but excluding the System Libraries of the Combined Work.

## 1. Exception to Section 3 of the GNU GPL.

You may convey a covered work under sections 3 and 4 of this License without being bound by section 3 of the
        GNU GPL.

## 2. Conveying Modified Versions.

If you modify a copy of the Library, and, in your modifications, a facility refers to a function or data to
        be supplied by an Application that uses the facility (other than as an argument passed when the facility is
        invoked), then you may convey a copy of the modified version:

under this License, provided that you make a good faith effort to ensure that, in the event an
            Application does not supply the function or data, the facility still operates, and performs whatever part
            of its purpose remains meaningful, or

under the GNU GPL, with none of the additional permissions of this License applicable to that
            copy.

## 3. Object Code Incorporating Material from Library Header Files.

The object code form of an Application may incorporate material from a header file that is part of the
        Library. You may convey such object code under terms of your choice, provided that, if the incorporated
        material is not limited to numerical parameters, data structure layouts and accessors, or small macros, inline
        functions and templates (ten or fewer lines in length), you do both of the following:

Give prominent notice with each copy of the object code that the Library is used in it and that the
            Library and its use are covered by this License.

Accompany the object code with a copy of the GNU GPL and this license document.

## 4. Combined Works.

You may convey a Combined Work under terms of your choice that, taken together, effectively do not restrict
        modification of the portions of the Library contained in the Combined Work and reverse engineering for
        debugging such modifications, if you also do each of the following:

Give prominent notice with each copy of the Combined Work that the Library is used in it and that the
            Library and its use are covered by this License.

Accompany the Combined Work with a copy of the GNU GPL and this license document.

For a Combined Work that displays copyright notices during execution, include the copyright notice for
            the Library among these notices, as well as a reference directing the user to the copies of the GNU GPL and
            this license document.

Do one of the following:
                Convey the Minimal Corresponding Source under the terms of this License, and the Corresponding
                    Application Code in a form suitable for, and under terms that permit, the user to recombine or
                    relink the Application with a modified version of the Linked Version to produce a modified Combined
                    Work, in the manner specified by section 6 of the GNU GPL for conveying Corresponding Source.Use a suitable shared library mechanism for linking with the Library. A suitable mechanism is
                    one that (a) uses at run time a copy of the Library already present on the user's computer system,
                    and (b) will operate properly with a modified version of the Library that is interface-compatible
                    with the Linked Version.

Convey the Minimal Corresponding Source under the terms of this License, and the Corresponding
                    Application Code in a form suitable for, and under terms that permit, the user to recombine or
                    relink the Application with a modified version of the Linked Version to produce a modified Combined
                    Work, in the manner specified by section 6 of the GNU GPL for conveying Corresponding Source.

Use a suitable shared library mechanism for linking with the Library. A suitable mechanism is
                    one that (a) uses at run time a copy of the Library already present on the user's computer system,
                    and (b) will operate properly with a modified version of the Library that is interface-compatible
                    with the Linked Version.

Provide Installation Information, but only if you would otherwise be required to provide such
            information under section 6 of the GNU GPL, and only to the extent that such information is necessary to
            install and execute a modified version of the Combined Work produced by recombining or relinking the
            Application with a modified version of the Linked Version. (If you use option 4d0, the Installation
            Information must accompany the Minimal Corresponding Source and Corresponding Application Code. If you use
            option 4d1, you must provide the Installation Information in the manner specified by section 6 of the GNU
            GPL for conveying Corresponding Source.)

## 5. Combined Libraries.

You may place library facilities that are a work based on the Library side by side in a single library
        together with other library facilities that are not Applications and are not covered by this License, and
        convey such a combined library under terms of your choice, if you do both of the following:

Accompany the combined library with a copy of the same work based on the Library, uncombined with any
            other library facilities, conveyed under the terms of this License.

Give prominent notice with the combined library that part of it is a work based on the Library, and
            explaining where to find the accompanying uncombined form of the same work.

## 6. Revised Versions of the GNU Lesser General Public License.

The Free Software Foundation may publish revised and/or new versions of the GNU Lesser General Public
        License from time to time. Such new versions will be similar in spirit to the present version, but may differ
        in detail to address new problems or concerns.

Each version is given a distinguishing version number. If the Library as you received it specifies that a
        certain numbered version of the GNU Lesser General Public License “or any later version” applies to it, you
        have the option of following the terms and conditions either of that published version or of any later version
        published by the Free Software Foundation. If the Library as you received it does not specify a version number
        of the GNU Lesser General Public License, you may choose any version of the GNU Lesser General Public License
        ever published by the Free Software Foundation.

If the Library as you received it specifies that a proxy can decide whether future versions of the GNU
        Lesser General Public License shall apply, that proxy's public statement of acceptance of any version is
        permanent authorization for you to choose that version for the Library.

# GNU LESSER GENERAL PUBLIC LICENSE

Version 2.1, February 1999

## Preamble

The licenses for most software are designed to take away your freedom to share and change it. By contrast,
        the GNU General Public Licenses are intended to guarantee your freedom to share and change free software--to
        make sure the software is free for all its users.

This license, the Lesser General Public License, applies to some specially designated software
        packages--typically libraries--of the Free Software Foundation and other authors who decide to use it. You can
        use it too, but we suggest you first think carefully about whether this license or the ordinary General Public
        License is the better strategy to use in any particular case, based on the explanations below.

When we speak of free software, we are referring to freedom of use, not price. Our General Public Licenses
        are designed to make sure that you have the freedom to distribute copies of free software (and charge for this
        service if you wish); that you receive source code or can get it if you want it; that you can change the
        software and use pieces of it in new free programs; and that you are informed that you can do these things.

To protect your rights, we need to make restrictions that forbid distributors to deny you these rights or to
        ask you to surrender these rights. These restrictions translate to certain responsibilities for you if you
        distribute copies of the library or if you modify it.

For example, if you distribute copies of the library, whether gratis or for a fee, you must give the
        recipients all the rights that we gave you. You must make sure that they, too, receive or can get the source
        code. If you link other code with the library, you must provide complete object files to the recipients, so
        that they can relink them with the library after making changes to the library and recompiling it. And you must
        show them these terms so they know their rights.

We protect your rights with a two-step method: (1) we copyright the library, and (2) we offer you this
        license, which gives you legal permission to copy, distribute and/or modify the library.

To protect each distributor, we want to make it very clear that there is no warranty for the free library.
        Also, if the library is modified by someone else and passed on, the recipients should know that what they have
        is not the original version, so that the original author's reputation will not be affected by problems that
        might be introduced by others.

Finally, software patents pose a constant threat to the existence of any free program. We wish to make sure
        that a company cannot effectively restrict the users of a free program by obtaining a restrictive license from
        a patent holder. Therefore, we insist that any patent license obtained for a version of the library must be
        consistent with the full freedom of use specified in this license.

Most GNU software, including some libraries, is covered by the ordinary GNU General Public License. This
        license, the GNU Lesser General Public License, applies to certain designated libraries, and is quite different
        from the ordinary General Public License. We use this license for certain libraries in order to permit linking
        those libraries into non-free programs.

When a program is linked with a library, whether statically or using a shared library, the combination of
        the two is legally speaking a combined work, a derivative of the original library. The ordinary General Public
        License therefore permits such linking only if the entire combination fits its criteria of freedom. The Lesser
        General Public License permits more lax criteria for linking other code with the library.

We call this license the "Lesser" General Public License because it does Less to protect the user's freedom
        than the ordinary General Public License. It also provides other free software developers Less of an advantage
        over competing non-free programs. These disadvantages are the reason we use the ordinary General Public License
        for many libraries. However, the Lesser license provides advantages in certain special circumstances.

For example, on rare occasions, there may be a special need to encourage the widest possible use of a
        certain library, so that it becomes a de-facto standard. To achieve this, non-free programs must be allowed to
        use the library. A more frequent case is that a free library does the same job as widely used non-free
        libraries. In this case, there is little to gain by limiting the free library to free software only, so we use
        the Lesser General Public License.

In other cases, permission to use a particular library in non-free programs enables a greater number of
        people to use a large body of free software. For example, permission to use the GNU C Library in non-free
        programs enables many more people to use the whole GNU operating system, as well as its variant, the GNU/Linux
        operating system.

Although the Lesser General Public License is Less protective of the users' freedom, it does ensure that the
        user of a program that is linked with the Library has the freedom and the wherewithal to run that program using
        a modified version of the Library.

The precise terms and conditions for copying, distribution and modification follow. Pay close attention to
        the difference between a "work based on the library" and a "work that uses the library". The former contains
        code derived from the library, whereas the latter must be combined with the library in order to run.

## TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

0. This License Agreement applies to any software library or other program which contains a
        notice placed by the copyright holder or other authorized party saying it may be distributed under the terms of
        this Lesser General Public License (also called "this License"). Each licensee is addressed as "you".

A "library" means a collection of software functions and/or data prepared so as to be conveniently linked
        with application programs (which use some of those functions and data) to form executables.

The "Library", below, refers to any such software library or work which has been distributed under these
        terms. A "work based on the Library" means either the Library or any derivative work under copyright law: that
        is to say, a work containing the Library or a portion of it, either verbatim or with modifications and/or
        translated straightforwardly into another language. (Hereinafter, translation is included without limitation in
        the term "modification".)

"Source code" for a work means the preferred form of the work for making modifications to it. For a library,
        complete source code means all the source code for all modules it contains, plus any associated interface
        definition files, plus the scripts used to control compilation and installation of the library.

Activities other than copying, distribution and modification are not covered by this License; they are
        outside its scope. The act of running a program using the Library is not restricted, and output from such a
        program is covered only if its contents constitute a work based on the Library (independent of the use of the
        Library in a tool for writing it). Whether that is true depends on what the Library does and what the program
        that uses the Library does.

1. You may copy and distribute verbatim copies of the Library's complete source code as you
        receive it, in any medium, provided that you conspicuously and appropriately publish on each copy an
        appropriate copyright notice and disclaimer of warranty; keep intact all the notices that refer to this License
        and to the absence of any warranty; and distribute a copy of this License along with the Library.

You may charge a fee for the physical act of transferring a copy, and you may at your option offer warranty
        protection in exchange for a fee.

2. You may modify your copy or copies of the Library or any portion of it, thus forming a
        work based on the Library, and copy and distribute such modifications or work under the terms of Section 1
        above, provided that you also meet all of these conditions:

a) The modified work must itself be a software library.

b) You must cause the files modified to carry prominent notices stating that you
            changed the files and the date of any change.

c) You must cause the whole of the work to be licensed at no charge to all third
            parties under the terms of this License.

d) If a facility in the modified Library refers to a function or a table of data to be
                supplied by an application program that uses the facility, other than as an argument passed when the
                facility is invoked, then you must make a good faith effort to ensure that, in the event an application
                does not supply such function or table, the facility still operates, and performs whatever part of its
                purpose remains meaningful.
                (For example, a function in a library to compute square roots has a purpose that is entirely
                well-defined independent of the application. Therefore, Subsection 2d requires that any
                application-supplied function or table used by this function must be optional: if the application does
                not supply it, the square root function must still compute square roots.)

(For example, a function in a library to compute square roots has a purpose that is entirely
                well-defined independent of the application. Therefore, Subsection 2d requires that any
                application-supplied function or table used by this function must be optional: if the application does
                not supply it, the square root function must still compute square roots.)

These requirements apply to the modified work as a whole. If identifiable sections of that work are not
        derived from the Library, and can be reasonably considered independent and separate works in themselves, then
        this License, and its terms, do not apply to those sections when you distribute them as separate works. But
        when you distribute the same sections as part of a whole which is a work based on the Library, the distribution
        of the whole must be on the terms of this License, whose permissions for other licensees extend to the entire
        whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest your rights to work written entirely
        by you; rather, the intent is to exercise the right to control the distribution of derivative or collective
        works based on the Library.

In addition, mere aggregation of another work not based on the Library with the Library (or with a work
        based on the Library) on a volume of a storage or distribution medium does not bring the other work under the
        scope of this License.

3. You may opt to apply the terms of the ordinary GNU General Public License instead of
        this License to a given copy of the Library. To do this, you must alter all the notices that refer to this
        License, so that they refer to the ordinary GNU General Public License, version 2, instead of to this License.
        (If a newer version than version 2 of the ordinary GNU General Public License has appeared, then you can
        specify that version instead if you wish.) Do not make any other change in these notices.

Once this change is made in a given copy, it is irreversible for that copy, so the ordinary GNU General
        Public License applies to all subsequent copies and derivative works made from that copy.

This option is useful when you wish to copy part of the code of the Library into a program that is not a
        library.

4. You may copy and distribute the Library (or a portion or derivative of it, under Section
        2) in object code or executable form under the terms of Sections 1 and 2 above provided that you accompany it
        with the complete corresponding machine-readable source code, which must be distributed under the terms of
        Sections 1 and 2 above on a medium customarily used for software interchange.

If distribution of object code is made by offering access to copy from a designated place, then offering
        equivalent access to copy the source code from the same place satisfies the requirement to distribute the
        source code, even though third parties are not compelled to copy the source along with the object code.

5. A program that contains no derivative of any portion of the Library, but is designed to
        work with the Library by being compiled or linked with it, is called a "work that uses the Library". Such a
        work, in isolation, is not a derivative work of the Library, and therefore falls outside the scope of this
        License.

However, linking a "work that uses the Library" with the Library creates an executable that is a derivative
        of the Library (because it contains portions of the Library), rather than a "work that uses the library". The
        executable is therefore covered by this License. Section 6 states terms for distribution of such
        executables.

When a "work that uses the Library" uses material from a header file that is part of the Library, the object
        code for the work may be a derivative work of the Library even though the source code is not. Whether this is
        true is especially significant if the work can be linked without the Library, or if the work is itself a
        library. The threshold for this to be true is not precisely defined by law.

If such an object file uses only numerical parameters, data structure layouts and accessors, and small
        macros and small inline functions (ten lines or less in length), then the use of the object file is
        unrestricted, regardless of whether it is legally a derivative work. (Executables containing this object code
        plus portions of the Library will still fall under Section 6.)

Otherwise, if the work is a derivative of the Library, you may distribute the object code for the work under
        the terms of Section 6. Any executables containing that work also fall under Section 6, whether or not they are
        linked directly with the Library itself.

6. As an exception to the Sections above, you may also combine or link a "work that uses
        the Library" with the Library to produce a work containing portions of the Library, and distribute that work
        under terms of your choice, provided that the terms permit modification of the work for the customer's own use
        and reverse engineering for debugging such modifications.

You must give prominent notice with each copy of the work that the Library is used in it and that the
        Library and its use are covered by this License. You must supply a copy of this License. If the work during
        execution displays copyright notices, you must include the copyright notice for the Library among them, as well
        as a reference directing the user to the copy of this License. Also, you must do one of these things:

a) Accompany the work with the complete corresponding machine-readable source code for
            the Library including whatever changes were used in the work (which must be distributed under Sections 1
            and 2 above); and, if the work is an executable linked with the Library, with the complete machine-readable
            "work that uses the Library", as object code and/or source code, so that the user can modify the Library
            and then relink to produce a modified executable containing the modified Library. (It is understood that
            the user who changes the contents of definitions files in the Library will not necessarily be able to
            recompile the application to use the modified definitions.)

b) Use a suitable shared library mechanism for linking with the Library. A suitable
            mechanism is one that (1) uses at run time a copy of the library already present on the user's computer
            system, rather than copying library functions into the executable, and (2) will operate properly with a
            modified version of the library, if the user installs one, as long as the modified version is
            interface-compatible with the version that the work was made with.

c) Accompany the work with a written offer, valid for at least three years, to give
            the same user the materials specified in Subsection 6a, above, for a charge no more than the cost of
            performing this distribution.

d) If distribution of the work is made by offering access to copy from a designated
            place, offer equivalent access to copy the above specified materials from the same place.

e) Verify that the user has already received a copy of these materials or that you
            have already sent this user a copy.

For an executable, the required form of the "work that uses the Library" must include any data and utility
        programs needed for reproducing the executable from it. However, as a special exception, the materials to be
        distributed need not include anything that is normally distributed (in either source or binary form) with the
        major components (compiler, kernel, and so on) of the operating system on which the executable runs, unless
        that component itself accompanies the executable.

It may happen that this requirement contradicts the license restrictions of other proprietary libraries that
        do not normally accompany the operating system. Such a contradiction means you cannot use both them and the
        Library together in an executable that you distribute.

7. You may place library facilities that are a work based on the Library side-by-side in a
        single library together with other library facilities not covered by this License, and distribute such a
        combined library, provided that the separate distribution of the work based on the Library and of the other
        library facilities is otherwise permitted, and provided that you do these two things:

a) Accompany the combined library with a copy of the same work based on the Library,
            uncombined with any other library facilities. This must be distributed under the terms of the Sections
            above.

b) Give prominent notice with the combined library of the fact that part of it is a
            work based on the Library, and explaining where to find the accompanying uncombined form of the same
            work.

8. You may not copy, modify, sublicense, link with, or distribute the Library except as
        expressly provided under this License. Any attempt otherwise to copy, modify, sublicense, link with, or
        distribute the Library is void, and will automatically terminate your rights under this License. However,
        parties who have received copies, or rights, from you under this License will not have their licenses
        terminated so long as such parties remain in full compliance.

9. You are not required to accept this License, since you have not signed it. However,
        nothing else grants you permission to modify or distribute the Library or its derivative works. These actions
        are prohibited by law if you do not accept this License. Therefore, by modifying or distributing the Library
        (or any work based on the Library), you indicate your acceptance of this License to do so, and all its terms
        and conditions for copying, distributing or modifying the Library or works based on it.

10. Each time you redistribute the Library (or any work based on the Library), the
        recipient automatically receives a license from the original licensor to copy, distribute, link with or modify
        the Library subject to these terms and conditions. You may not impose any further restrictions on the
        recipients' exercise of the rights granted herein. You are not responsible for enforcing compliance by third
        parties with this License.

11. If, as a consequence of a court judgment or allegation of patent infringement or for
        any other reason (not limited to patent issues), conditions are imposed on you (whether by court order,
        agreement or otherwise) that contradict the conditions of this License, they do not excuse you from the
        conditions of this License. If you cannot distribute so as to satisfy simultaneously your obligations under
        this License and any other pertinent obligations, then as a consequence you may not distribute the Library at
        all. For example, if a patent license would not permit royalty-free redistribution of the Library by all those
        who receive copies directly or indirectly through you, then the only way you could satisfy both it and this
        License would be to refrain entirely from distribution of the Library.

If any portion of this section is held invalid or unenforceable under any particular circumstance, the
        balance of the section is intended to apply, and the section as a whole is intended to apply in other
        circumstances.

It is not the purpose of this section to induce you to infringe any patents or other property right claims
        or to contest validity of any such claims; this section has the sole purpose of protecting the integrity of the
        free software distribution system which is implemented by public license practices. Many people have made
        generous contributions to the wide range of software distributed through that system in reliance on consistent
        application of that system; it is up to the author/donor to decide if he or she is willing to distribute
        software through any other system and a licensee cannot impose that choice.

This section is intended to make thoroughly clear what is believed to be a consequence of the rest of this
        License.

12. If the distribution and/or use of the Library is restricted in certain countries either
        by patents or by copyrighted interfaces, the original copyright holder who places the Library under this
        License may add an explicit geographical distribution limitation excluding those countries, so that
        distribution is permitted only in or among countries not thus excluded. In such case, this License incorporates
        the limitation as if written in the body of this License.

13. The Free Software Foundation may publish revised and/or new versions of the Lesser
        General Public License from time to time. Such new versions will be similar in spirit to the present version,
        but may differ in detail to address new problems or concerns.

Each version is given a distinguishing version number. If the Library specifies a version number of this
        License which applies to it and "any later version", you have the option of following the terms and conditions
        either of that version or of any later version published by the Free Software Foundation. If the Library does
        not specify a license version number, you may choose any version ever published by the Free Software
        Foundation.

14. If you wish to incorporate parts of the Library into other free programs whose
        distribution conditions are incompatible with these, write to the author to ask for permission. For software
        which is copyrighted by the Free Software Foundation, write to the Free Software Foundation; we sometimes make
        exceptions for this. Our decision will be guided by the two goals of preserving the free status of all
        derivatives of our free software and of promoting the sharing and reuse of software generally.

NO WARRANTY

15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY FOR THE LIBRARY,
        TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
        OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING,
        BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE
        RISK AS TO THE QUALITY AND PERFORMANCE OF THE LIBRARY IS WITH YOU. SHOULD THE LIBRARY PROVE DEFECTIVE, YOU
        ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY
        COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE
        LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF
        THE USE OR INABILITY TO USE THE LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED
        INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER
        SOFTWARE), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

## END OF TERMS AND CONDITIONS

## How to Apply These Terms to Your New Libraries

If you develop a new library, and you want it to be of the greatest possible use to the public, we recommend
        making it free software that everyone can redistribute and change. You can do so by permitting redistribution
        under these terms (or, alternatively, under the terms of the ordinary General Public License).

To apply these terms, attach the following notices to the library. It is safest to attach them to the start
        of each source file to most effectively convey the exclusion of warranty; and each file should have at least
        the "copyright" line and a pointer to where the full notice is found.

Also add information on how to contact you by electronic and paper mail.

You should also get your employer (if you work as a programmer) or your school, if any, to sign a "copyright
        disclaimer" for the library, if necessary. Here is a sample; alter the names:

That's all there is to it!

# GNU LIBRARY GENERAL PUBLIC LICENSE

Version 2, June 1991

## Preamble

The licenses for most software are designed to take away your freedom to share and change it. By contrast,
        the GNU General Public Licenses are intended to guarantee your freedom to share and change free software--to
        make sure the software is free for all its users.

This license, the Library General Public License, applies to some specially designated Free Software
        Foundation software, and to any other libraries whose authors decide to use it. You can use it for your
        libraries, too.

When we speak of free software, we are referring to freedom, not price. Our General Public Licenses are
        designed to make sure that you have the freedom to distribute copies of free software (and charge for this
        service if you wish), that you receive source code or can get it if you want it, that you can change the
        software or use pieces of it in new free programs; and that you know you can do these things.

To protect your rights, we need to make restrictions that forbid anyone to deny you these rights or to ask
        you to surrender the rights. These restrictions translate to certain responsibilities for you if you distribute
        copies of the library, or if you modify it.

For example, if you distribute copies of the library, whether gratis or for a fee, you must give the
        recipients all the rights that we gave you. You must make sure that they, too, receive or can get the source
        code. If you link a program with the library, you must provide complete object files to the recipients so that
        they can relink them with the library, after making changes to the library and recompiling it. And you must
        show them these terms so they know their rights.

Our method of protecting your rights has two steps: (1) copyright the library, and (2) offer you this
        license which gives you legal permission to copy, distribute and/or modify the library.

Also, for each distributor's protection, we want to make certain that everyone understands that there is no
        warranty for this free library. If the library is modified by someone else and passed on, we want its
        recipients to know that what they have is not the original version, so that any problems introduced by others
        will not reflect on the original authors' reputations.

Finally, any free program is threatened constantly by software patents. We wish to avoid the danger that
        companies distributing free software will individually obtain patent licenses, thus in effect transforming the
        program into proprietary software. To prevent this, we have made it clear that any patent must be licensed for
        everyone's free use or not licensed at all.

Most GNU software, including some libraries, is covered by the ordinary GNU General Public License, which
        was designed for utility programs. This license, the GNU Library General Public License, applies to certain
        designated libraries. This license is quite different from the ordinary one; be sure to read it in full, and
        don't assume that anything in it is the same as in the ordinary license.

The reason we have a separate public license for some libraries is that they blur the distinction we usually
        make between modifying or adding to a program and simply using it. Linking a program with a library, without
        changing the library, is in some sense simply using the library, and is analogous to running a utility program
        or application program. However, in a textual and legal sense, the linked executable is a combined work, a
        derivative of the original library, and the ordinary General Public License treats it as such.

Because of this blurred distinction, using the ordinary General Public License for libraries did not
        effectively promote software sharing, because most developers did not use the libraries. We concluded that
        weaker conditions might promote sharing better.

However, unrestricted linking of non-free programs would deprive the users of those programs of all benefit
        from the free status of the libraries themselves. This Library General Public License is intended to permit
        developers of non-free programs to use free libraries, while preserving your freedom as a user of such programs
        to change the free libraries that are incorporated in them. (We have not seen how to achieve this as regards
        changes in header files, but we have achieved it as regards changes in the actual functions of the Library.)
        The hope is that this will lead to faster development of free libraries.

The precise terms and conditions for copying, distribution and modification follow. Pay close attention to
        the difference between a "work based on the library" and a "work that uses the library". The former contains
        code derived from the library, while the latter only works together with the library.

Note that it is possible for a library to be covered by the ordinary General Public License rather than by
        this special one.

## TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

0. This License Agreement applies to any software library which contains a notice placed by
        the copyright holder or other authorized party saying it may be distributed under the terms of this Library
        General Public License (also called "this License"). Each licensee is addressed as "you".

A "library" means a collection of software functions and/or data prepared so as to be conveniently linked
        with application programs (which use some of those functions and data) to form executables.

The "Library", below, refers to any such software library or work which has been distributed under these
        terms. A "work based on the Library" means either the Library or any derivative work under copyright law: that
        is to say, a work containing the Library or a portion of it, either verbatim or with modifications and/or
        translated straightforwardly into another language. (Hereinafter, translation is included without limitation in
        the term "modification".)

"Source code" for a work means the preferred form of the work for making modifications to it. For a library,
        complete source code means all the source code for all modules it contains, plus any associated interface
        definition files, plus the scripts used to control compilation and installation of the library.

Activities other than copying, distribution and modification are not covered by this License; they are
        outside its scope. The act of running a program using the Library is not restricted, and output from such a
        program is covered only if its contents constitute a work based on the Library (independent of the use of the
        Library in a tool for writing it). Whether that is true depends on what the Library does and what the program
        that uses the Library does.

1. You may copy and distribute verbatim copies of the Library's complete source code as you
        receive it, in any medium, provided that you conspicuously and appropriately publish on each copy an
        appropriate copyright notice and disclaimer of warranty; keep intact all the notices that refer to this License
        and to the absence of any warranty; and distribute a copy of this License along with the Library.

You may charge a fee for the physical act of transferring a copy, and you may at your option offer warranty
        protection in exchange for a fee.

2. You may modify your copy or copies of the Library or any portion of it, thus forming a
        work based on the Library, and copy and distribute such modifications or work under the terms of Section 1
        above, provided that you also meet all of these conditions:

a) The modified work must itself be a software library.

b) You must cause the files modified to carry prominent notices stating that you
            changed the files and the date of any change.

c) You must cause the whole of the work to be licensed at no charge to all third
            parties under the terms of this License.

d) If a facility in the modified Library refers to a function or a table of data to be
                supplied by an application program that uses the facility, other than as an argument passed when the
                facility is invoked, then you must make a good faith effort to ensure that, in the event an application
                does not supply such function or table, the facility still operates, and performs whatever part of its
                purpose remains meaningful.
                (For example, a function in a library to compute square roots has a purpose that is entirely
                well-defined independent of the application. Therefore, Subsection 2d requires that any
                application-supplied function or table used by this function must be optional: if the application does
                not supply it, the square root function must still compute square roots.)

(For example, a function in a library to compute square roots has a purpose that is entirely
                well-defined independent of the application. Therefore, Subsection 2d requires that any
                application-supplied function or table used by this function must be optional: if the application does
                not supply it, the square root function must still compute square roots.)

These requirements apply to the modified work as a whole. If identifiable sections of that work are not
        derived from the Library, and can be reasonably considered independent and separate works in themselves, then
        this License, and its terms, do not apply to those sections when you distribute them as separate works. But
        when you distribute the same sections as part of a whole which is a work based on the Library, the distribution
        of the whole must be on the terms of this License, whose permissions for other licensees extend to the entire
        whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest your rights to work written entirely
        by you; rather, the intent is to exercise the right to control the distribution of derivative or collective
        works based on the Library.

In addition, mere aggregation of another work not based on the Library with the Library (or with a work
        based on the Library) on a volume of a storage or distribution medium does not bring the other work under the
        scope of this License.

3. You may opt to apply the terms of the ordinary GNU General Public License instead of
        this License to a given copy of the Library. To do this, you must alter all the notices that refer to this
        License, so that they refer to the ordinary GNU General Public License, version 2, instead of to this License.
        (If a newer version than version 2 of the ordinary GNU General Public License has appeared, then you can
        specify that version instead if you wish.) Do not make any other change in these notices.

Once this change is made in a given copy, it is irreversible for that copy, so the ordinary GNU General
        Public License applies to all subsequent copies and derivative works made from that copy.

This option is useful when you wish to copy part of the code of the Library into a program that is not a
        library.

4. You may copy and distribute the Library (or a portion or derivative of it, under Section
        2) in object code or executable form under the terms of Sections 1 and 2 above provided that you accompany it
        with the complete corresponding machine-readable source code, which must be distributed under the terms of
        Sections 1 and 2 above on a medium customarily used for software interchange.

If distribution of object code is made by offering access to copy from a designated place, then offering
        equivalent access to copy the source code from the same place satisfies the requirement to distribute the
        source code, even though third parties are not compelled to copy the source along with the object code.

5. A program that contains no derivative of any portion of the Library, but is designed to
        work with the Library by being compiled or linked with it, is called a "work that uses the Library". Such a
        work, in isolation, is not a derivative work of the Library, and therefore falls outside the scope of this
        License.

However, linking a "work that uses the Library" with the Library creates an executable that is a derivative
        of the Library (because it contains portions of the Library), rather than a "work that uses the library". The
        executable is therefore covered by this License. Section 6 states terms for distribution of such
        executables.

When a "work that uses the Library" uses material from a header file that is part of the Library, the object
        code for the work may be a derivative work of the Library even though the source code is not. Whether this is
        true is especially significant if the work can be linked without the Library, or if the work is itself a
        library. The threshold for this to be true is not precisely defined by law.

If such an object file uses only numerical parameters, data structure layouts and accessors, and small
        macros and small inline functions (ten lines or less in length), then the use of the object file is
        unrestricted, regardless of whether it is legally a derivative work. (Executables containing this object code
        plus portions of the Library will still fall under Section 6.)

Otherwise, if the work is a derivative of the Library, you may distribute the object code for the work under
        the terms of Section 6. Any executables containing that work also fall under Section 6, whether or not they are
        linked directly with the Library itself.

6. As an exception to the Sections above, you may also compile or link a "work that uses
        the Library" with the Library to produce a work containing portions of the Library, and distribute that work
        under terms of your choice, provided that the terms permit modification of the work for the customer's own use
        and reverse engineering for debugging such modifications.

You must give prominent notice with each copy of the work that the Library is used in it and that the
        Library and its use are covered by this License. You must supply a copy of this License. If the work during
        execution displays copyright notices, you must include the copyright notice for the Library among them, as well
        as a reference directing the user to the copy of this License. Also, you must do one of these things:

a) Accompany the work with the complete corresponding machine-readable source code for
            the Library including whatever changes were used in the work (which must be distributed under Sections 1
            and 2 above); and, if the work is an executable linked with the Library, with the complete machine-readable
            "work that uses the Library", as object code and/or source code, so that the user can modify the Library
            and then relink to produce a modified executable containing the modified Library. (It is understood that
            the user who changes the contents of definitions files in the Library will not necessarily be able to
            recompile the application to use the modified definitions.)

b) Accompany the work with a written offer, valid for at least three years, to give
            the same user the materials specified in Subsection 6a, above, for a charge no more than the cost of
            performing this distribution.

c) If distribution of the work is made by offering access to copy from a designated
            place, offer equivalent access to copy the above specified materials from the same place.

d) Verify that the user has already received a copy of these materials or that you
            have already sent this user a copy.

For an executable, the required form of the "work that uses the Library" must include any data and utility
        programs needed for reproducing the executable from it. However, as a special exception, the source code
        distributed need not include anything that is normally distributed (in either source or binary form) with the
        major components (compiler, kernel, and so on) of the operating system on which the executable runs, unless
        that component itself accompanies the executable.

It may happen that this requirement contradicts the license restrictions of other proprietary libraries that
        do not normally accompany the operating system. Such a contradiction means you cannot use both them and the
        Library together in an executable that you distribute.

7. You may place library facilities that are a work based on the Library side-by-side in a
        single library together with other library facilities not covered by this License, and distribute such a
        combined library, provided that the separate distribution of the work based on the Library and of the other
        library facilities is otherwise permitted, and provided that you do these two things:

a) Accompany the combined library with a copy of the same work based on the Library,
            uncombined with any other library facilities. This must be distributed under the terms of the Sections
            above.

b) Give prominent notice with the combined library of the fact that part of it is a
            work based on the Library, and explaining where to find the accompanying uncombined form of the same
            work.

8. You may not copy, modify, sublicense, link with, or distribute the Library except as
        expressly provided under this License. Any attempt otherwise to copy, modify, sublicense, link with, or
        distribute the Library is void, and will automatically terminate your rights under this License. However,
        parties who have received copies, or rights, from you under this License will not have their licenses
        terminated so long as such parties remain in full compliance.

9. You are not required to accept this License, since you have not signed it. However,
        nothing else grants you permission to modify or distribute the Library or its derivative works. These actions
        are prohibited by law if you do not accept this License. Therefore, by modifying or distributing the Library
        (or any work based on the Library), you indicate your acceptance of this License to do so, and all its terms
        and conditions for copying, distributing or modifying the Library or works based on it.

10. Each time you redistribute the Library (or any work based on the Library), the
        recipient automatically receives a license from the original licensor to copy, distribute, link with or modify
        the Library subject to these terms and conditions. You may not impose any further restrictions on the
        recipients' exercise of the rights granted herein. You are not responsible for enforcing compliance by third
        parties to this License.

11. If, as a consequence of a court judgment or allegation of patent infringement or for
        any other reason (not limited to patent issues), conditions are imposed on you (whether by court order,
        agreement or otherwise) that contradict the conditions of this License, they do not excuse you from the
        conditions of this License. If you cannot distribute so as to satisfy simultaneously your obligations under
        this License and any other pertinent obligations, then as a consequence you may not distribute the Library at
        all. For example, if a patent license would not permit royalty-free redistribution of the Library by all those
        who receive copies directly or indirectly through you, then the only way you could satisfy both it and this
        License would be to refrain entirely from distribution of the Library.

If any portion of this section is held invalid or unenforceable under any particular circumstance, the
        balance of the section is intended to apply, and the section as a whole is intended to apply in other
        circumstances.

It is not the purpose of this section to induce you to infringe any patents or other property right claims
        or to contest validity of any such claims; this section has the sole purpose of protecting the integrity of the
        free software distribution system which is implemented by public license practices. Many people have made
        generous contributions to the wide range of software distributed through that system in reliance on consistent
        application of that system; it is up to the author/donor to decide if he or she is willing to distribute
        software through any other system and a licensee cannot impose that choice.

This section is intended to make thoroughly clear what is believed to be a consequence of the rest of this
        License.

12. If the distribution and/or use of the Library is restricted in certain countries either
        by patents or by copyrighted interfaces, the original copyright holder who places the Library under this
        License may add an explicit geographical distribution limitation excluding those countries, so that
        distribution is permitted only in or among countries not thus excluded. In such case, this License incorporates
        the limitation as if written in the body of this License.

13. The Free Software Foundation may publish revised and/or new versions of the Library
        General Public License from time to time. Such new versions will be similar in spirit to the present version,
        but may differ in detail to address new problems or concerns.

Each version is given a distinguishing version number. If the Library specifies a version number of this
        License which applies to it and "any later version", you have the option of following the terms and conditions
        either of that version or of any later version published by the Free Software Foundation. If the Library does
        not specify a license version number, you may choose any version ever published by the Free Software
        Foundation.

14. If you wish to incorporate parts of the Library into other free programs whose
        distribution conditions are incompatible with these, write to the author to ask for permission. For software
        which is copyrighted by the Free Software Foundation, write to the Free Software Foundation; we sometimes make
        exceptions for this. Our decision will be guided by the two goals of preserving the free status of all
        derivatives of our free software and of promoting the sharing and reuse of software generally.

NO WARRANTY

15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY FOR THE LIBRARY,
        TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
        OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING,
        BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE
        RISK AS TO THE QUALITY AND PERFORMANCE OF THE LIBRARY IS WITH YOU. SHOULD THE LIBRARY PROVE DEFECTIVE, YOU
        ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY
        COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE
        LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF
        THE USE OR INABILITY TO USE THE LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED
        INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER
        SOFTWARE), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

## END OF TERMS AND CONDITIONS

## How to Apply These Terms to Your New Libraries

If you develop a new library, and you want it to be of the greatest possible use to the public, we recommend
        making it free software that everyone can redistribute and change. You can do so by permitting redistribution
        under these terms (or, alternatively, under the terms of the ordinary General Public License).

To apply these terms, attach the following notices to the library. It is safest to attach them to the start
        of each source file to most effectively convey the exclusion of warranty; and each file should have at least
        the "copyright" line and a pointer to where the full notice is found.

Also add information on how to contact you by electronic and paper mail.

You should also get your employer (if you work as a programmer) or your school, if any, to sign a "copyright
        disclaimer" for the library, if necessary. Here is a sample; alter the names:

That's all there is to it!

# GNU GENERAL PUBLIC LICENSE

Version 3, 29 June 2007

Copyright © 2007 Free Software Foundation, Inc. <https://fsf.org/>

Everyone is permitted to copy and distribute verbatim copies of this license document, but changing it is
        not allowed.

### Preamble

The GNU General Public License is a free, copyleft license for software and other kinds of works.

The licenses for most software and other practical works are designed to take away your freedom to share and
        change the works. By contrast, the GNU General Public License is intended to guarantee your freedom to share
        and change all versions of a program--to make sure it remains free software for all its users. We, the Free
        Software Foundation, use the GNU General Public License for most of our software; it applies also to any other
        work released this way by its authors. You can apply it to your programs, too.

When we speak of free software, we are referring to freedom, not price. Our General Public Licenses are
        designed to make sure that you have the freedom to distribute copies of free software (and charge for them if
        you wish), that you receive source code or can get it if you want it, that you can change the software or use
        pieces of it in new free programs, and that you know you can do these things.

To protect your rights, we need to prevent others from denying you these rights or asking you to surrender
        the rights. Therefore, you have certain responsibilities if you distribute copies of the software, or if you
        modify it: responsibilities to respect the freedom of others.

For example, if you distribute copies of such a program, whether gratis or for a fee, you must pass on to
        the recipients the same freedoms that you received. You must make sure that they, too, receive or can get the
        source code. And you must show them these terms so they know their rights.

Developers that use the GNU GPL protect your rights with two steps: (1) assert copyright on the software,
        and (2) offer you this License giving you legal permission to copy, distribute and/or modify it.

For the developers' and authors' protection, the GPL clearly explains that there is no warranty for this
        free software. For both users' and authors' sake, the GPL requires that modified versions be marked as changed,
        so that their problems will not be attributed erroneously to authors of previous versions.

Some devices are designed to deny users access to install or run modified versions of the software inside
        them, although the manufacturer can do so. This is fundamentally incompatible with the aim of protecting users'
        freedom to change the software. The systematic pattern of such abuse occurs in the area of products for
        individuals to use, which is precisely where it is most unacceptable. Therefore, we have designed this version
        of the GPL to prohibit the practice for those products. If such problems arise substantially in other domains,
        we stand ready to extend this provision to those domains in future versions of the GPL, as needed to protect
        the freedom of users.

Finally, every program is threatened constantly by software patents. States should not allow patents to
        restrict development and use of software on general-purpose computers, but in those that do, we wish to avoid
        the special danger that patents applied to a free program could make it effectively proprietary. To prevent
        this, the GPL assures that patents cannot be used to render the program non-free.

The precise terms and conditions for copying, distribution and modification follow.

### TERMS AND CONDITIONS

“This License” refers to version 3 of the GNU General Public License.

“Copyright” also means copyright-like laws that apply to other kinds of works, such as semiconductor
        masks.

“The Program” refers to any copyrightable work licensed under this License. Each licensee is addressed
        as “you”. “Licensees” and “recipients” may be individuals or organizations.

To “modify” a work means to copy from or adapt all or part of the work in a fashion requiring copyright
        permission, other than the making of an exact copy. The resulting work is called a “modified version” of
        the earlier work or a work “based on” the earlier work.

A “covered work” means either the unmodified Program or a work based on the Program.

To “propagate” a work means to do anything with it that, without permission, would make you directly or
        secondarily liable for infringement under applicable copyright law, except executing it on a computer or
        modifying a private copy. Propagation includes copying, distribution (with or without modification), making
        available to the public, and in some countries other activities as well.

To “convey” a work means any kind of propagation that enables other parties to make or receive copies.
        Mere interaction with a user through a computer network, with no transfer of a copy, is not conveying.

An interactive user interface displays “Appropriate Legal Notices” to the extent that it includes a
        convenient and prominently visible feature that (1) displays an appropriate copyright notice, and (2) tells the
        user that there is no warranty for the work (except to the extent that warranties are provided), that licensees
        may convey the work under this License, and how to view a copy of this License. If the interface presents a
        list of user commands or options, such as a menu, a prominent item in the list meets this criterion.

The “source code” for a work means the preferred form of the work for making modifications to it.
        “Object code” means any non-source form of a work.

A “Standard Interface” means an interface that either is an official standard defined by a recognized
        standards body, or, in the case of interfaces specified for a particular programming language, one that is
        widely used among developers working in that language.

The “System Libraries” of an executable work include anything, other than the work as a whole, that (a)
        is included in the normal form of packaging a Major Component, but which is not part of that Major Component,
        and (b) serves only to enable use of the work with that Major Component, or to implement a Standard Interface
        for which an implementation is available to the public in source code form. A “Major Component”, in this
        context, means a major essential component (kernel, window system, and so on) of the specific operating system
        (if any) on which the executable work runs, or a compiler used to produce the work, or an object code
        interpreter used to run it.

The “Corresponding Source” for a work in object code form means all the source code needed to generate,
        install, and (for an executable work) run the object code and to modify the work, including scripts to control
        those activities. However, it does not include the work's System Libraries, or general-purpose tools or
        generally available free programs which are used unmodified in performing those activities but which are not
        part of the work. For example, Corresponding Source includes interface definition files associated with source
        files for the work, and the source code for shared libraries and dynamically linked subprograms that the work
        is specifically designed to require, such as by intimate data communication or control flow between those
        subprograms and other parts of the work.

The Corresponding Source need not include anything that users can regenerate automatically from other parts
        of the Corresponding Source.

The Corresponding Source for a work in source code form is that same work.

All rights granted under this License are granted for the term of copyright on the Program, and are
        irrevocable provided the stated conditions are met. This License explicitly affirms your unlimited permission
        to run the unmodified Program. The output from running a covered work is covered by this License only if the
        output, given its content, constitutes a covered work. This License acknowledges your rights of fair use or
        other equivalent, as provided by copyright law.

You may make, run and propagate covered works that you do not convey, without conditions so long as your
        license otherwise remains in force. You may convey covered works to others for the sole purpose of having them
        make modifications exclusively for you, or provide you with facilities for running those works, provided that
        you comply with the terms of this License in conveying all material for which you do not control copyright.
        Those thus making or running the covered works for you must do so exclusively on your behalf, under your
        direction and control, on terms that prohibit them from making any copies of your copyrighted material outside
        their relationship with you.

Conveying under any other circumstances is permitted solely under the conditions stated below. Sublicensing
        is not allowed; section 10 makes it unnecessary.

No covered work shall be deemed part of an effective technological measure under any applicable law
        fulfilling obligations under article 11 of the WIPO copyright treaty adopted on 20 December 1996, or similar
        laws prohibiting or restricting circumvention of such measures.

When you convey a covered work, you waive any legal power to forbid circumvention of technological measures
        to the extent such circumvention is effected by exercising rights under this License with respect to the
        covered work, and you disclaim any intention to limit operation or modification of the work as a means of
        enforcing, against the work's users, your or third parties' legal rights to forbid circumvention of
        technological measures.

You may convey verbatim copies of the Program's source code as you receive it, in any medium, provided that
        you conspicuously and appropriately publish on each copy an appropriate copyright notice; keep intact all
        notices stating that this License and any non-permissive terms added in accord with section 7 apply to the
        code; keep intact all notices of the absence of any warranty; and give all recipients a copy of this License
        along with the Program.

You may charge any price or no price for each copy that you convey, and you may offer support or warranty
        protection for a fee.

You may convey a work based on the Program, or the modifications to produce it from the Program, in the form
        of source code under the terms of section 4, provided that you also meet all of these conditions:

a) The work must carry prominent notices stating that you modified it, and giving a relevant date.

b) The work must carry prominent notices stating that it is released under this License and any
            conditions added under section 7. This requirement modifies the requirement in section 4 to “keep intact
            all notices”.

c) You must license the entire work, as a whole, under this License to anyone who comes into possession
            of a copy. This License will therefore apply, along with any applicable section 7 additional terms, to the
            whole of the work, and all its parts, regardless of how they are packaged. This License gives no permission
            to license the work in any other way, but it does not invalidate such permission if you have separately
            received it.

d) If the work has interactive user interfaces, each must display Appropriate Legal Notices; however,
            if the Program has interactive interfaces that do not display Appropriate Legal Notices, your work need not
            make them do so.

A compilation of a covered work with other separate and independent works, which are not by their nature
        extensions of the covered work, and which are not combined with it such as to form a larger program, in or on a
        volume of a storage or distribution medium, is called an “aggregate” if the compilation and its resulting
        copyright are not used to limit the access or legal rights of the compilation's users beyond what the
        individual works permit. Inclusion of a covered work in an aggregate does not cause this License to apply to
        the other parts of the aggregate.

You may convey a covered work in object code form under the terms of sections 4 and 5, provided that you
        also convey the machine-readable Corresponding Source under the terms of this License, in one of these
        ways:

a) Convey the object code in, or embodied in, a physical product (including a physical distribution
            medium), accompanied by the Corresponding Source fixed on a durable physical medium customarily used for
            software interchange.

b) Convey the object code in, or embodied in, a physical product (including a physical distribution
            medium), accompanied by a written offer, valid for at least three years and valid for as long as you offer
            spare parts or customer support for that product model, to give anyone who possesses the object code either
            (1) a copy of the Corresponding Source for all the software in the product that is covered by this License,
            on a durable physical medium customarily used for software interchange, for a price no more than your
            reasonable cost of physically performing this conveying of source, or (2) access to copy the Corresponding
            Source from a network server at no charge.

c) Convey individual copies of the object code with a copy of the written offer to provide the
            Corresponding Source. This alternative is allowed only occasionally and noncommercially, and only if you
            received the object code with such an offer, in accord with subsection 6b.

d) Convey the object code by offering access from a designated place (gratis or for a charge), and
            offer equivalent access to the Corresponding Source in the same way through the same place at no further
            charge. You need not require recipients to copy the Corresponding Source along with the object code. If the
            place to copy the object code is a network server, the Corresponding Source may be on a different server
            (operated by you or a third party) that supports equivalent copying facilities, provided you maintain clear
            directions next to the object code saying where to find the Corresponding Source. Regardless of what server
            hosts the Corresponding Source, you remain obligated to ensure that it is available for as long as needed
            to satisfy these requirements.

e) Convey the object code using peer-to-peer transmission, provided you inform other peers where the
            object code and Corresponding Source of the work are being offered to the general public at no charge under
            subsection 6d.

A separable portion of the object code, whose source code is excluded from the Corresponding Source as a
        System Library, need not be included in conveying the object code work.

A “User Product” is either (1) a “consumer product”, which means any tangible personal property
        which is normally used for personal, family, or household purposes, or (2) anything designed or sold for
        incorporation into a dwelling. In determining whether a product is a consumer product, doubtful cases shall be
        resolved in favor of coverage. For a particular product received by a particular user, “normally used”
        refers to a typical or common use of that class of product, regardless of the status of the particular user or
        of the way in which the particular user actually uses, or expects or is expected to use, the product. A product
        is a consumer product regardless of whether the product has substantial commercial, industrial or non-consumer
        uses, unless such uses represent the only significant mode of use of the product.

“Installation Information” for a User Product means any methods, procedures, authorization keys, or
        other information required to install and execute modified versions of a covered work in that User Product from
        a modified version of its Corresponding Source. The information must suffice to ensure that the continued
        functioning of the modified object code is in no case prevented or interfered with solely because modification
        has been made.

If you convey an object code work under this section in, or with, or specifically for use in, a User
        Product, and the conveying occurs as part of a transaction in which the right of possession and use of the User
        Product is transferred to the recipient in perpetuity or for a fixed term (regardless of how the transaction is
        characterized), the Corresponding Source conveyed under this section must be accompanied by the Installation
        Information. But this requirement does not apply if neither you nor any third party retains the ability to
        install modified object code on the User Product (for example, the work has been installed in ROM).

The requirement to provide Installation Information does not include a requirement to continue to provide
        support service, warranty, or updates for a work that has been modified or installed by the recipient, or for
        the User Product in which it has been modified or installed. Access to a network may be denied when the
        modification itself materially and adversely affects the operation of the network or violates the rules and
        protocols for communication across the network.

Corresponding Source conveyed, and Installation Information provided, in accord with this section must be in
        a format that is publicly documented (and with an implementation available to the public in source code form),
        and must require no special password or key for unpacking, reading or copying.

“Additional permissions” are terms that supplement the terms of this License by making exceptions from
        one or more of its conditions. Additional permissions that are applicable to the entire Program shall be
        treated as though they were included in this License, to the extent that they are valid under applicable law.
        If additional permissions apply only to part of the Program, that part may be used separately under those
        permissions, but the entire Program remains governed by this License without regard to the additional
        permissions.

When you convey a copy of a covered work, you may at your option remove any additional permissions from that
        copy, or from any part of it. (Additional permissions may be written to require their own removal in certain
        cases when you modify the work.) You may place additional permissions on material, added by you to a covered
        work, for which you have or can give appropriate copyright permission.

Notwithstanding any other provision of this License, for material you add to a covered work, you may (if
        authorized by the copyright holders of that material) supplement the terms of this License with terms:

a) Disclaiming warranty or limiting liability differently from the terms of sections 15 and 16 of this
            License; or

b) Requiring preservation of specified reasonable legal notices or author attributions in that material
            or in the Appropriate Legal Notices displayed by works containing it; or

c) Prohibiting misrepresentation of the origin of that material, or requiring that modified versions of
            such material be marked in reasonable ways as different from the original version; or

d) Limiting the use for publicity purposes of names of licensors or authors of the material; or

e) Declining to grant rights under trademark law for use of some trade names, trademarks, or service
            marks; or

f) Requiring indemnification of licensors and authors of that material by anyone who conveys the
            material (or modified versions of it) with contractual assumptions of liability to the recipient, for any
            liability that these contractual assumptions directly impose on those licensors and authors.

All other non-permissive additional terms are considered “further restrictions” within the meaning of
        section 10. If the Program as you received it, or any part of it, contains a notice stating that it is governed
        by this License along with a term that is a further restriction, you may remove that term. If a license
        document contains a further restriction but permits relicensing or conveying under this License, you may add to
        a covered work material governed by the terms of that license document, provided that the further restriction
        does not survive such relicensing or conveying.

If you add terms to a covered work in accord with this section, you must place, in the relevant source
        files, a statement of the additional terms that apply to those files, or a notice indicating where to find the
        applicable terms.

Additional terms, permissive or non-permissive, may be stated in the form of a separately written license,
        or stated as exceptions; the above requirements apply either way.

You may not propagate or modify a covered work except as expressly provided under this License. Any attempt
        otherwise to propagate or modify it is void, and will automatically terminate your rights under this License
        (including any patent licenses granted under the third paragraph of section 11).

However, if you cease all violation of this License, then your license from a particular copyright holder is
        reinstated (a) provisionally, unless and until the copyright holder explicitly and finally terminates your
        license, and (b) permanently, if the copyright holder fails to notify you of the violation by some reasonable
        means prior to 60 days after the cessation.

Moreover, your license from a particular copyright holder is reinstated permanently if the copyright holder
        notifies you of the violation by some reasonable means, this is the first time you have received notice of
        violation of this License (for any work) from that copyright holder, and you cure the violation prior to 30
        days after your receipt of the notice.

Termination of your rights under this section does not terminate the licenses of parties who have received
        copies or rights from you under this License. If your rights have been terminated and not permanently
        reinstated, you do not qualify to receive new licenses for the same material under section 10.

You are not required to accept this License in order to receive or run a copy of the Program. Ancillary
        propagation of a covered work occurring solely as a consequence of using peer-to-peer transmission to receive a
        copy likewise does not require acceptance. However, nothing other than this License grants you permission to
        propagate or modify any covered work. These actions infringe copyright if you do not accept this License.
        Therefore, by modifying or propagating a covered work, you indicate your acceptance of this License to do
        so.

Each time you convey a covered work, the recipient automatically receives a license from the original
        licensors, to run, modify and propagate that work, subject to this License. You are not responsible for
        enforcing compliance by third parties with this License.

An “entity transaction” is a transaction transferring control of an organization, or substantially all
        assets of one, or subdividing an organization, or merging organizations. If propagation of a covered work
        results from an entity transaction, each party to that transaction who receives a copy of the work also
        receives whatever licenses to the work the party's predecessor in interest had or could give under the previous
        paragraph, plus a right to possession of the Corresponding Source of the work from the predecessor in interest,
        if the predecessor has it or can get it with reasonable efforts.

You may not impose any further restrictions on the exercise of the rights granted or affirmed under this
        License. For example, you may not impose a license fee, royalty, or other charge for exercise of rights granted
        under this License, and you may not initiate litigation (including a cross-claim or counterclaim in a lawsuit)
        alleging that any patent claim is infringed by making, using, selling, offering for sale, or importing the
        Program or any portion of it.

A “contributor” is a copyright holder who authorizes use under this License of the Program or a work on
        which the Program is based. The work thus licensed is called the contributor's “contributor version”.

A contributor's “essential patent claims” are all patent claims owned or controlled by the contributor,
        whether already acquired or hereafter acquired, that would be infringed by some manner, permitted by this
        License, of making, using, or selling its contributor version, but do not include claims that would be
        infringed only as a consequence of further modification of the contributor version. For purposes of this
        definition, “control” includes the right to grant patent sublicenses in a manner consistent with the
        requirements of this License.

Each contributor grants you a non-exclusive, worldwide, royalty-free patent license under the contributor's
        essential patent claims, to make, use, sell, offer for sale, import and otherwise run, modify and propagate the
        contents of its contributor version.

In the following three paragraphs, a “patent license” is any express agreement or commitment, however
        denominated, not to enforce a patent (such as an express permission to practice a patent or covenant not to sue
        for patent infringement). To “grant” such a patent license to a party means to make such an agreement or
        commitment not to enforce a patent against the party.

If you convey a covered work, knowingly relying on a patent license, and the Corresponding Source of the
        work is not available for anyone to copy, free of charge and under the terms of this License, through a
        publicly available network server or other readily accessible means, then you must either (1) cause the
        Corresponding Source to be so available, or (2) arrange to deprive yourself of the benefit of the patent
        license for this particular work, or (3) arrange, in a manner consistent with the requirements of this License,
        to extend the patent license to downstream recipients. “Knowingly relying” means you have actual knowledge
        that, but for the patent license, your conveying the covered work in a country, or your recipient's use of the
        covered work in a country, would infringe one or more identifiable patents in that country that you have reason
        to believe are valid.

If, pursuant to or in connection with a single transaction or arrangement, you convey, or propagate by
        procuring conveyance of, a covered work, and grant a patent license to some of the parties receiving the
        covered work authorizing them to use, propagate, modify or convey a specific copy of the covered work, then the
        patent license you grant is automatically extended to all recipients of the covered work and works based on
        it.

A patent license is “discriminatory” if it does not include within the scope of its coverage, prohibits
        the exercise of, or is conditioned on the non-exercise of one or more of the rights that are specifically
        granted under this License. You may not convey a covered work if you are a party to an arrangement with a third
        party that is in the business of distributing software, under which you make payment to the third party based
        on the extent of your activity of conveying the work, and under which the third party grants, to any of the
        parties who would receive the covered work from you, a discriminatory patent license (a) in connection with
        copies of the covered work conveyed by you (or copies made from those copies), or (b) primarily for and in
        connection with specific products or compilations that contain the covered work, unless you entered into that
        arrangement, or that patent license was granted, prior to 28 March 2007.

Nothing in this License shall be construed as excluding or limiting any implied license or other defenses to
        infringement that may otherwise be available to you under applicable patent law.

If conditions are imposed on you (whether by court order, agreement or otherwise) that contradict the
        conditions of this License, they do not excuse you from the conditions of this License. If you cannot convey a
        covered work so as to satisfy simultaneously your obligations under this License and any other pertinent
        obligations, then as a consequence you may not convey it at all. For example, if you agree to terms that
        obligate you to collect a royalty for further conveying from those to whom you convey the Program, the only way
        you could satisfy both those terms and this License would be to refrain entirely from conveying the
        Program.

Notwithstanding any other provision of this License, you have permission to link or combine any covered work
        with a work licensed under version 3 of the GNU Affero General Public License into a single combined work, and
        to convey the resulting work. The terms of this License will continue to apply to the part which is the covered
        work, but the special requirements of the GNU Affero General Public License, section 13, concerning interaction
        through a network will apply to the combination as such.

The Free Software Foundation may publish revised and/or new versions of the GNU General Public License from
        time to time. Such new versions will be similar in spirit to the present version, but may differ in detail to
        address new problems or concerns.

Each version is given a distinguishing version number. If the Program specifies that a certain numbered
        version of the GNU General Public License “or any later version” applies to it, you have the option of
        following the terms and conditions either of that numbered version or of any later version published by the
        Free Software Foundation. If the Program does not specify a version number of the GNU General Public License,
        you may choose any version ever published by the Free Software Foundation.

If the Program specifies that a proxy can decide which future versions of the GNU General Public License can
        be used, that proxy's public statement of acceptance of a version permanently authorizes you to choose that
        version for the Program.

Later license versions may give you additional or different permissions. However, no additional obligations
        are imposed on any author or copyright holder as a result of your choosing to follow a later version.

THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE
        STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM “AS IS” WITHOUT WARRANTY
        OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
        MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
        PROGRAM IS WITH YOU. SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR
        OR CORRECTION.

IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY COPYRIGHT HOLDER, OR ANY
        OTHER PARTY WHO MODIFIES AND/OR CONVEYS THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING
        ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
        PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU
        OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS), EVEN IF SUCH HOLDER OR OTHER
        PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

If the disclaimer of warranty and limitation of liability provided above cannot be given local legal effect
        according to their terms, reviewing courts shall apply local law that most closely approximates an absolute
        waiver of all civil liability in connection with the Program, unless a warranty or assumption of liability
        accompanies a copy of the Program in return for a fee.

END OF TERMS AND CONDITIONS

### How to Apply These Terms to Your New Programs

If you develop a new program, and you want it to be of the greatest possible use to the public, the best way
        to achieve this is to make it free software which everyone can redistribute and change under these terms.

To do so, attach the following notices to the program. It is safest to attach them to the start of each
        source file to most effectively state the exclusion of warranty; and each file should have at least the
        “copyright” line and a pointer to where the full notice is found.

Also add information on how to contact you by electronic and paper mail.

If the program does terminal interaction, make it output a short notice like this when it starts in an
        interactive mode:

The hypothetical commands `show w' and `show c' should show the appropriate parts of the General Public
        License. Of course, your program's commands might be different; for a GUI interface, you would use an “about
        box”.

You should also get your employer (if you work as a programmer) or school, if any, to sign a “copyright
        disclaimer” for the program, if necessary. For more information on this, and how to apply and follow the GNU
        GPL, see <https://www.gnu.org/licenses/>.

The GNU General Public License does not permit incorporating your program into proprietary programs. If your
        program is a subroutine library, you may consider it more useful to permit linking proprietary applications
        with the library. If this is what you want to do, use the GNU Lesser General Public License instead of this
        License. But first, please read <https://www.gnu.org/licenses/why-not-lgpl.html>.

# GNU GENERAL PUBLIC LICENSE

Version 2, June 1991

### Preamble

The licenses for most software are designed to take away your freedom to share and change it. By contrast,
        the GNU General Public License is intended to guarantee your freedom to share and change free software--to make
        sure the software is free for all its users. This General Public License applies to most of the Free Software
        Foundation's software and to any other program whose authors commit to using it. (Some other Free Software
        Foundation software is covered by the GNU Lesser General Public License instead.) You can apply it to your
        programs, too.

When we speak of free software, we are referring to freedom, not price. Our General Public Licenses are
        designed to make sure that you have the freedom to distribute copies of free software (and charge for this
        service if you wish), that you receive source code or can get it if you want it, that you can change the
        software or use pieces of it in new free programs; and that you know you can do these things.

To protect your rights, we need to make restrictions that forbid anyone to deny you these rights or to ask
        you to surrender the rights. These restrictions translate to certain responsibilities for you if you distribute
        copies of the software, or if you modify it.

For example, if you distribute copies of such a program, whether gratis or for a fee, you must give the
        recipients all the rights that you have. You must make sure that they, too, receive or can get the source code.
        And you must show them these terms so they know their rights.

We protect your rights with two steps: (1) copyright the software, and (2) offer you this license which
        gives you legal permission to copy, distribute and/or modify the software.

Also, for each author's protection and ours, we want to make certain that everyone understands that there is
        no warranty for this free software. If the software is modified by someone else and passed on, we want its
        recipients to know that what they have is not the original, so that any problems introduced by others will not
        reflect on the original authors' reputations.

Finally, any free program is threatened constantly by software patents. We wish to avoid the danger that
        redistributors of a free program will individually obtain patent licenses, in effect making the program
        proprietary. To prevent this, we have made it clear that any patent must be licensed for everyone's free use or
        not licensed at all.

The precise terms and conditions for copying, distribution and modification follow.

### TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

0. This License applies to any program or other work which contains a notice placed by the
        copyright holder saying it may be distributed under the terms of this General Public License. The "Program",
        below, refers to any such program or work, and a "work based on the Program" means either the Program or any
        derivative work under copyright law: that is to say, a work containing the Program or a portion of it, either
        verbatim or with modifications and/or translated into another language. (Hereinafter, translation is included
        without limitation in the term "modification".) Each licensee is addressed as "you".

Activities other than copying, distribution and modification are not covered by this License; they are
        outside its scope. The act of running the Program is not restricted, and the output from the Program is covered
        only if its contents constitute a work based on the Program (independent of having been made by running the
        Program). Whether that is true depends on what the Program does.

1. You may copy and distribute verbatim copies of the Program's source code as you receive
        it, in any medium, provided that you conspicuously and appropriately publish on each copy an appropriate
        copyright notice and disclaimer of warranty; keep intact all the notices that refer to this License and to the
        absence of any warranty; and give any other recipients of the Program a copy of this License along with the
        Program.

You may charge a fee for the physical act of transferring a copy, and you may at your option offer warranty
        protection in exchange for a fee.

2. You may modify your copy or copies of the Program or any portion of it, thus forming a
        work based on the Program, and copy and distribute such modifications or work under the terms of Section 1
        above, provided that you also meet all of these conditions:

These requirements apply to the modified work as a whole. If identifiable sections of that work are not
        derived from the Program, and can be reasonably considered independent and separate works in themselves, then
        this License, and its terms, do not apply to those sections when you distribute them as separate works. But
        when you distribute the same sections as part of a whole which is a work based on the Program, the distribution
        of the whole must be on the terms of this License, whose permissions for other licensees extend to the entire
        whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest your rights to work written entirely
        by you; rather, the intent is to exercise the right to control the distribution of derivative or collective
        works based on the Program.

In addition, mere aggregation of another work not based on the Program with the Program (or with a work
        based on the Program) on a volume of a storage or distribution medium does not bring the other work under the
        scope of this License.

3. You may copy and distribute the Program (or a work based on it, under Section 2) in
        object code or executable form under the terms of Sections 1 and 2 above provided that you also do one of the
        following:

The source code for a work means the preferred form of the work for making modifications to it. For an
        executable work, complete source code means all the source code for all modules it contains, plus any
        associated interface definition files, plus the scripts used to control compilation and installation of the
        executable. However, as a special exception, the source code distributed need not include anything that is
        normally distributed (in either source or binary form) with the major components (compiler, kernel, and so on)
        of the operating system on which the executable runs, unless that component itself accompanies the
        executable.

If distribution of executable or object code is made by offering access to copy from a designated place,
        then offering equivalent access to copy the source code from the same place counts as distribution of the
        source code, even though third parties are not compelled to copy the source along with the object code.

4. You may not copy, modify, sublicense, or distribute the Program except as expressly
        provided under this License. Any attempt otherwise to copy, modify, sublicense or distribute the Program is
        void, and will automatically terminate your rights under this License. However, parties who have received
        copies, or rights, from you under this License will not have their licenses terminated so long as such parties
        remain in full compliance.

5. You are not required to accept this License, since you have not signed it. However,
        nothing else grants you permission to modify or distribute the Program or its derivative works. These actions
        are prohibited by law if you do not accept this License. Therefore, by modifying or distributing the Program
        (or any work based on the Program), you indicate your acceptance of this License to do so, and all its terms
        and conditions for copying, distributing or modifying the Program or works based on it.

6. Each time you redistribute the Program (or any work based on the Program), the recipient
        automatically receives a license from the original licensor to copy, distribute or modify the Program subject
        to these terms and conditions. You may not impose any further restrictions on the recipients' exercise of the
        rights granted herein. You are not responsible for enforcing compliance by third parties to this License.

7. If, as a consequence of a court judgment or allegation of patent infringement or for any
        other reason (not limited to patent issues), conditions are imposed on you (whether by court order, agreement
        or otherwise) that contradict the conditions of this License, they do not excuse you from the conditions of
        this License. If you cannot distribute so as to satisfy simultaneously your obligations under this License and
        any other pertinent obligations, then as a consequence you may not distribute the Program at all. For example,
        if a patent license would not permit royalty-free redistribution of the Program by all those who receive copies
        directly or indirectly through you, then the only way you could satisfy both it and this License would be to
        refrain entirely from distribution of the Program.

If any portion of this section is held invalid or unenforceable under any particular circumstance, the
        balance of the section is intended to apply and the section as a whole is intended to apply in other
        circumstances.

It is not the purpose of this section to induce you to infringe any patents or other property right claims
        or to contest validity of any such claims; this section has the sole purpose of protecting the integrity of the
        free software distribution system, which is implemented by public license practices. Many people have made
        generous contributions to the wide range of software distributed through that system in reliance on consistent
        application of that system; it is up to the author/donor to decide if he or she is willing to distribute
        software through any other system and a licensee cannot impose that choice.

This section is intended to make thoroughly clear what is believed to be a consequence of the rest of this
        License.

8. If the distribution and/or use of the Program is restricted in certain countries either
        by patents or by copyrighted interfaces, the original copyright holder who places the Program under this
        License may add an explicit geographical distribution limitation excluding those countries, so that
        distribution is permitted only in or among countries not thus excluded. In such case, this License incorporates
        the limitation as if written in the body of this License.

9. The Free Software Foundation may publish revised and/or new versions of the General
        Public License from time to time. Such new versions will be similar in spirit to the present version, but may
        differ in detail to address new problems or concerns.

Each version is given a distinguishing version number. If the Program specifies a version number of this
        License which applies to it and "any later version", you have the option of following the terms and conditions
        either of that version or of any later version published by the Free Software Foundation. If the Program does
        not specify a version number of this License, you may choose any version ever published by the Free Software
        Foundation.

10. If you wish to incorporate parts of the Program into other free programs whose
        distribution conditions are different, write to the author to ask for permission. For software which is
        copyrighted by the Free Software Foundation, write to the Free Software Foundation; we sometimes make
        exceptions for this. Our decision will be guided by the two goals of preserving the free status of all
        derivatives of our free software and of promoting the sharing and reuse of software generally.

NO WARRANTY

11. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY FOR THE PROGRAM,
        TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
        OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING,
        BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE
        RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH YOU. SHOULD THE PROGRAM PROVE DEFECTIVE, YOU
        ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

12. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY
        COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE
        LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF
        THE USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED
        INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER
        PROGRAMS), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

### END OF TERMS AND CONDITIONS

### How to Apply These Terms to Your New Programs

If you develop a new program, and you want it to be of the greatest possible use to the public, the best way
        to achieve this is to make it free software which everyone can redistribute and change under these terms.

To do so, attach the following notices to the program. It is safest to attach them to the start of each
        source file to most effectively convey the exclusion of warranty; and each file should have at least the
        "copyright" line and a pointer to where the full notice is found.

Also add information on how to contact you by electronic and paper mail.

If the program is interactive, make it output a short notice like this when it starts in an interactive
        mode:

The hypothetical commands `show w' and `show c' should show the appropriate parts
        of the General Public License. Of course, the commands you use may be called something other than `show
        w' and `show c'; they could even be mouse-clicks or menu items--whatever suits your
        program.

You should also get your employer (if you work as a programmer) or your school, if any, to sign a "copyright
        disclaimer" for the program, if necessary. Here is a sample; alter the names:

This General Public License does not permit incorporating your program into proprietary programs. If your
        program is a subroutine library, you may consider it more useful to permit linking proprietary applications
        with the library. If this is what you want to do, use the GNU
        Lesser General Public License instead of this License.

# Common Public License Version 1.0 (CPL)

THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS COMMON PUBLIC LICENSE ("AGREEMENT"). ANY
        USE, REPRODUCTION OR DISTRIBUTION OF THE PROGRAM CONSTITUTES RECIPIENT'S ACCEPTANCE OF THIS AGREEMENT.

1. DEFINITIONS

"Contribution" means:

a) in the case of the initial Contributor, the initial code and documentation distributed under this
            Agreement, and

b) in the case of each subsequent Contributor:

i) changes to the Program, and

ii) additions to the Program;

where such changes and/or additions to the Program originate from and are distributed by that
            particular Contributor. A Contribution 'originates' from a Contributor if it was added to the Program by
            such Contributor itself or anyone acting on such Contributor's behalf. Contributions do not include
            additions to the Program which: (i) are separate modules of software distributed in conjunction with the
            Program under their own license agreement, and (ii) are not derivative works of the Program.

"Contributor" means any person or entity that distributes the Program.

"Licensed Patents " mean patent claims licensable by a Contributor which are necessarily infringed by
        the use or sale of its Contribution alone or when combined with the Program.

"Program" means the Contributions distributed in accordance with this Agreement.

"Recipient" means anyone who receives the Program under this Agreement, including all
        Contributors.

2. GRANT OF RIGHTS

a) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive,
            worldwide, royalty-free copyright license to reproduce, prepare derivative works of, publicly display,
            publicly perform, distribute and sublicense the Contribution of such Contributor, if any, and such
            derivative works, in source code and object code form.

b) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive,
            worldwide, royalty-free patent license under Licensed Patents to make, use, sell, offer to sell, import and
            otherwise transfer the Contribution of such Contributor, if any, in source code and object code form. This
            patent license shall apply to the combination of the Contribution and the Program if, at the time the
            Contribution is added by the Contributor, such addition of the Contribution causes such combination to be
            covered by the Licensed Patents. The patent license shall not apply to any other combinations which include
            the Contribution. No hardware per se is licensed hereunder.

c) Recipient understands that although each Contributor grants the licenses to its Contributions set
            forth herein, no assurances are provided by any Contributor that the Program does not infringe the patent
            or other intellectual property rights of any other entity. Each Contributor disclaims any liability to
            Recipient for claims brought by any other entity based on infringement of intellectual property rights or
            otherwise. As a condition to exercising the rights and licenses granted hereunder, each Recipient hereby
            assumes sole responsibility to secure any other intellectual property rights needed, if any. For example,
            if a third party patent license is required to allow Recipient to distribute the Program, it is Recipient's
            responsibility to acquire that license before distributing the Program.

d) Each Contributor represents that to its knowledge it has sufficient copyright rights in its
            Contribution, if any, to grant the copyright license set forth in this Agreement.

3. REQUIREMENTS

A Contributor may choose to distribute the Program in object code form under its own license agreement,
        provided that:

a) it complies with the terms and conditions of this Agreement; and

b) its license agreement:

i) effectively disclaims on behalf of all Contributors all warranties and conditions, express and
            implied, including warranties or conditions of title and non-infringement, and implied warranties or
            conditions of merchantability and fitness for a particular purpose;

ii) effectively excludes on behalf of all Contributors all liability for damages, including direct,
            indirect, special, incidental and consequential damages, such as lost profits;

iii) states that any provisions which differ from this Agreement are offered by that Contributor
            alone and not by any other party; and

iv) states that source code for the Program is available from such Contributor, and informs
            licensees how to obtain it in a reasonable manner on or through a medium customarily used for software
            exchange.

When the Program is made available in source code form:

a) it must be made available under this Agreement; and

b) a copy of this Agreement must be included with each copy of the Program.

Contributors may not remove or alter any copyright notices contained within the Program.

Each Contributor must identify itself as the originator of its Contribution, if any, in a manner that
        reasonably allows subsequent Recipients to identify the originator of the Contribution.

4. COMMERCIAL DISTRIBUTION

Commercial distributors of software may accept certain responsibilities with respect to end users,
        business partners and the like. While this license is intended to facilitate the commercial use of the Program,
        the Contributor who includes the Program in a commercial product offering should do so in a manner which does
        not create potential liability for other Contributors. Therefore, if a Contributor includes the Program in a
        commercial product offering, such Contributor ("Commercial Contributor") hereby agrees to defend and indemnify
        every other Contributor ("Indemnified Contributor") against any losses, damages and costs (collectively
        "Losses") arising from claims, lawsuits and other legal actions brought by a third party against the
        Indemnified Contributor to the extent caused by the acts or omissions of such Commercial Contributor in
        connection with its distribution of the Program in a commercial product offering. The obligations in this
        section do not apply to any claims or Losses relating to any actual or alleged intellectual property
        infringement. In order to qualify, an Indemnified Contributor must: a) promptly notify the Commercial
        Contributor in writing of such claim, and b) allow the Commercial Contributor to control, and cooperate with
        the Commercial Contributor in, the defense and any related settlement negotiations. The Indemnified Contributor
        may participate in any such claim at its own expense.

For example, a Contributor might include the Program in a commercial product offering, Product X. That
        Contributor is then a Commercial Contributor. If that Commercial Contributor then makes performance claims, or
        offers warranties related to Product X, those performance claims and warranties are such Commercial
        Contributor's responsibility alone. Under this section, the Commercial Contributor would have to defend claims
        against the other Contributors related to those performance claims and warranties, and if a court requires any
        other Contributor to pay any damages as a result, the Commercial Contributor must pay those damages.

5. NO WARRANTY

EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM IS PROVIDED ON AN "AS IS" BASIS, WITHOUT
        WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES
        OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. Each Recipient
        is solely responsible for determining the appropriateness of using and distributing the Program and assumes all
        risks associated with its exercise of rights under this Agreement, including but not limited to the risks and
        costs of program errors, compliance with applicable laws, damage to or loss of data, programs or equipment, and
        unavailability or interruption of operations.

6. DISCLAIMER OF LIABILITY

EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT NOR ANY CONTRIBUTORS SHALL HAVE ANY
        LIABILITY FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING WITHOUT
        LIMITATION LOST PROFITS), HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR
        THE EXERCISE OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

7. GENERAL

If any provision of this Agreement is invalid or unenforceable under applicable law, it shall not affect
        the validity or enforceability of the remainder of the terms of this Agreement, and without further action by
        the parties hereto, such provision shall be reformed to the minimum extent necessary to make such provision
        valid and enforceable.

If Recipient institutes patent litigation against a Contributor with respect to a patent applicable to
        software (including a cross-claim or counterclaim in a lawsuit), then any patent licenses granted by that
        Contributor to such Recipient under this Agreement shall terminate as of the date such litigation is filed. In
        addition, if Recipient institutes patent litigation against any entity (including a cross-claim or counterclaim
        in a lawsuit) alleging that the Program itself (excluding combinations of the Program with other software or
        hardware) infringes such Recipient's patent(s), then such Recipient's rights granted under Section 2(b) shall
        terminate as of the date such litigation is filed.

All Recipient's rights under this Agreement shall terminate if it fails to comply with any of the
        material terms or conditions of this Agreement and does not cure such failure in a reasonable period of time
        after becoming aware of such noncompliance. If all Recipient's rights under this Agreement terminate, Recipient
        agrees to cease use and distribution of the Program as soon as reasonably practicable. However, Recipient's
        obligations under this Agreement and any licenses granted by Recipient relating to the Program shall continue
        and survive.

Everyone is permitted to copy and distribute copies of this Agreement, but in order to avoid
        inconsistency the Agreement is copyrighted and may only be modified in the following manner. The Agreement
        Steward reserves the right to publish new versions (including revisions) of this Agreement from time to time.
        No one other than the Agreement Steward has the right to modify this Agreement. IBM is the initial Agreement
        Steward. IBM may assign the responsibility to serve as the Agreement Steward to a suitable separate entity.
        Each new version of the Agreement will be given a distinguishing version number. The Program (including
        Contributions) may always be distributed subject to the version of the Agreement under which it was received.
        In addition, after a new version of the Agreement is published, Contributor may elect to distribute the Program
        (including its Contributions) under the new version. Except as expressly stated in Sections 2(a) and 2(b)
        above, Recipient receives no rights or licenses to the intellectual property of any Contributor under this
        Agreement, whether expressly, by implication, estoppel or otherwise. All rights in the Program not expressly
        granted under this Agreement are reserved.

This Agreement is governed by the laws of the State of New York and the intellectual property laws of
        the United States of America. No party to this Agreement will bring a legal action under this Agreement more
        than one year after the cause of action arose. Each party waives its rights to a jury trial in any resulting
        litigation.

# Mozilla Public License Version 1.1

## 1. Definitions.

means any addition to or deletion from the substance or structure of either the Original Code or any
            previous Modifications. When Covered Code is released as a series of files, a Modification is:

Any addition to or deletion from the contents of a file containing
                Original Code or previous Modifications.

Any new file that contains any part of the Original Code or previous
                Modifications.

## 2. Source Code License.

### 2.1. The Initial Developer Grant.

The Initial Developer hereby grants You a world-wide, royalty-free, non-exclusive license, subject to third
    party intellectual property claims:

under intellectual property rights (other than patent or trademark) Licensable
        by Initial Developer to use, reproduce, modify, display, perform, sublicense and distribute the Original Code
        (or portions thereof) with or without Modifications, and/or as part of a Larger Work; and

under Patents Claims infringed by the making, using or selling of Original
        Code, to make, have made, use, practice, sell, and offer for sale, and/or otherwise dispose of the Original
        Code (or portions thereof).

the licenses granted in this Section 2.1 (a) and (b) are effective on the date
        Initial Developer first distributes Original Code under the terms of this License.

Notwithstanding Section 2.1 (b) above,
        no patent license is granted: 1) for code that You delete from the Original Code; 2) separate from the Original
        Code; or 3) for infringements caused by: i) the modification of the Original Code or ii) the combination of the
        Original Code with other software or devices.

### 2.2. Contributor Grant.

Subject to third party intellectual property claims, each Contributor hereby grants You a world-wide,
    royalty-free, non-exclusive license

under intellectual property rights (other than patent or trademark) Licensable
        by Contributor, to use, reproduce, modify, display, perform, sublicense and distribute the Modifications
        created by such Contributor (or portions thereof) either on an unmodified basis, with other Modifications, as
        Covered Code and/or as part of a Larger Work; and

under Patent Claims infringed by the making, using, or selling of
        Modifications made by that Contributor either alone and/or in combination with its Contributor Version (or
        portions of such combination), to make, use, sell, offer for sale, have made, and/or otherwise dispose of: 1)
        Modifications made by that Contributor (or portions thereof); and 2) the combination of Modifications made by
        that Contributor with its Contributor Version (or portions of such combination).

the licenses granted in Sections 2.2 (a) and 2.2 (b) are effective on the date
        Contributor first makes Commercial Use of the Covered Code.

Notwithstanding Section 2.2 (b) above,
        no patent license is granted: 1) for any code that Contributor has deleted from the Contributor Version; 2)
        separate from the Contributor Version; 3) for infringements caused by: i) third party modifications of
        Contributor Version or ii) the combination of Modifications made by that Contributor with other software
        (except as part of the Contributor Version) or other devices; or 4) under Patent Claims infringed by Covered
        Code in the absence of Modifications made by that Contributor.

## 3. Distribution Obligations.

### 3.1. Application of License.

The Modifications which You create or to which You contribute are governed by the terms of this License,
    including without limitation Section 2.2. The Source Code version of Covered
    Code may be distributed only under the terms of this License or a future version of this License released under
    Section 6.1, and You must include a copy of this License with every copy of
    the Source Code You distribute. You may not offer or impose any terms on any Source Code version that alters or
    restricts the applicable version of this License or the recipients' rights hereunder. However, You may include an
    additional document offering the additional rights described in Section 3.5.

### 3.2. Availability of Source Code.

Any Modification which You create or to which You contribute must be made available in Source Code form under
    the terms of this License either on the same media as an Executable version or via an accepted Electronic
    Distribution Mechanism to anyone to whom you made an Executable version available; and if made available via
    Electronic Distribution Mechanism, must remain available for at least twelve (12) months after the date it
    initially became available, or at least six (6) months after a subsequent version of that particular Modification
    has been made available to such recipients. You are responsible for ensuring that the Source Code version remains
    available even if the Electronic Distribution Mechanism is maintained by a third party.

### 3.3. Description of Modifications.

You must cause all Covered Code to which You contribute to contain a file documenting the changes You made to
    create that Covered Code and the date of any change. You must include a prominent statement that the Modification
    is derived, directly or indirectly, from Original Code provided by the Initial Developer and including the name of
    the Initial Developer in (a) the Source Code, and (b) in any notice in an Executable version or related
    documentation in which You describe the origin or ownership of the Covered Code.

### 3.4. Intellectual Property Matters

If Contributor has knowledge that a license under a third party's intellectual property rights is required to
    exercise the rights granted by such Contributor under Sections 2.1 or 2.2, Contributor must include a text file with the Source Code distribution titled
    "LEGAL" which describes the claim and the party making the claim in sufficient detail that a recipient will know
    whom to contact. If Contributor obtains such knowledge after the Modification is made available as described in
    Section 3.2, Contributor shall promptly modify the LEGAL file in all copies
    Contributor makes available thereafter and shall take other steps (such as notifying appropriate mailing lists or
    newsgroups) reasonably calculated to inform those who received the Covered Code that new knowledge has been
    obtained.

If Contributor's Modifications include an application programming interface and Contributor has knowledge of
    patent licenses which are reasonably necessary to implement that API, Contributor must also include
    this information in the legal file.

Contributor represents that, except as disclosed pursuant to Section 3.4 (a) above, Contributor believes that Contributor's Modifications are Contributor's
    original creation(s) and/or Contributor has sufficient rights to grant the rights conveyed by this License.

### 3.5. Required Notices.

You must duplicate the notice in Exhibit A in each file of the Source Code.
    If it is not possible to put such notice in a particular Source Code file due to its structure, then You must
    include such notice in a location (such as a relevant directory) where a user would be likely to look for such a
    notice. If You created one or more Modification(s) You may add your name as a Contributor to the notice described
    in Exhibit A. You must also duplicate this License in any documentation for the
    Source Code where You describe recipients' rights or ownership rights relating to Covered Code. You may choose to
    offer, and to charge a fee for, warranty, support, indemnity or liability obligations to one or more recipients of
    Covered Code. However, You may do so only on Your own behalf, and not on behalf of the Initial Developer or any
    Contributor. You must make it absolutely clear than any such warranty, support, indemnity or liability obligation
    is offered by You alone, and You hereby agree to indemnify the Initial Developer and every Contributor for any
    liability incurred by the Initial Developer or such Contributor as a result of warranty, support, indemnity or
    liability terms You offer.

### 3.6. Distribution of Executable Versions.

You may distribute Covered Code in Executable form only if the requirements of Sections 3.1, 3.2, 3.3, 3.4 and 3.5 have been met for that Covered Code, and if You include a notice stating that the
    Source Code version of the Covered Code is available under the terms of this License, including a description of
    how and where You have fulfilled the obligations of Section 3.2. The notice
    must be conspicuously included in any notice in an Executable version, related documentation or collateral in which
    You describe recipients' rights relating to the Covered Code. You may distribute the Executable version of Covered
    Code or ownership rights under a license of Your choice, which may contain terms different from this License,
    provided that You are in compliance with the terms of this License and that the license for the Executable version
    does not attempt to limit or alter the recipient's rights in the Source Code version from the rights set forth in
    this License. If You distribute the Executable version under a different license You must make it absolutely clear
    that any terms which differ from this License are offered by You alone, not by the Initial Developer or any
    Contributor. You hereby agree to indemnify the Initial Developer and every Contributor for any liability incurred
    by the Initial Developer or such Contributor as a result of any such terms You offer.

### 3.7. Larger Works.

You may create a Larger Work by combining Covered Code with other code not governed by the terms of this License
    and distribute the Larger Work as a single product. In such a case, You must make sure the requirements of this
    License are fulfilled for the Covered Code.

## 4. Inability to Comply Due to Statute or Regulation.

If it is impossible for You to comply with any of the terms of this License with respect to some or all of the
    Covered Code due to statute, judicial order, or regulation then You must: (a) comply with the terms of this License
    to the maximum extent possible; and (b) describe the limitations and the code they affect. Such description must be
    included in the legal file described in Section 3.4 and must be included with all distributions of the Source Code. Except to the
    extent prohibited by statute or regulation, such description must be sufficiently detailed for a recipient of
    ordinary skill to be able to understand it.

## 5. Application of this License.

This License applies to code to which the Initial Developer has attached the notice in Exhibit A and to related Covered Code.

## 6. Versions of the License.

### 6.1. New Versions

Netscape Communications Corporation ("Netscape") may publish revised and/or new versions of the License from
    time to time. Each version will be given a distinguishing version number.

### 6.2. Effect of New Versions

Once Covered Code has been published under a particular version of the License, You may always continue to use
    it under the terms of that version. You may also choose to use such Covered Code under the terms of any subsequent
    version of the License published by Netscape. No one other than Netscape has the right to modify the terms
    applicable to Covered Code created under this License.

### 6.3. Derivative Works

If You create or use a modified version of this License (which you may only do in order to apply it to code
    which is not already Covered Code governed by this License), You must (a) rename Your license so that the phrases
    "Mozilla", "MOZILLAPL", "MOZPL", "Netscape", "MPL", "NPL" or any confusingly similar phrase do not appear in your
    license (except to note that your license differs from this License) and (b) otherwise make it clear that Your
    version of the license contains terms which differ from the Mozilla Public License and Netscape Public License.
    (Filling in the name of the Initial Developer, Original Code or Contributor in the notice described in Exhibit A shall not of themselves be deemed to be modifications of this License.)

## 7. Disclaimer of warranty

Covered code is provided under this license on an "as is" basis, without warranty of
    any kind, either expressed or implied, including, without limitation, warranties that the covered code is free of
    defects, merchantable, fit for a particular purpose or non-infringing. The entire risk as to the quality and
    performance of the covered code is with you. Should any covered code prove defective in any respect, you (not the
    initial developer or any other contributor) assume the cost of any necessary servicing, repair or correction. This
    disclaimer of warranty constitutes an essential part of this license. No use of any covered code is authorized
    hereunder except under this disclaimer.

## 8. Termination

8.1. This License and the rights granted hereunder will terminate automatically if
    You fail to comply with terms herein and fail to cure such breach within 30 days of becoming aware of the breach.
    All sublicenses to the Covered Code which are properly granted shall survive any termination of this License.
    Provisions which, by their nature, must remain in effect beyond the termination of this License shall survive.

8.2. If You initiate litigation by asserting a patent infringement claim (excluding
    declatory judgment actions) against Initial Developer or a Contributor (the Initial Developer or Contributor
    against whom You file such action is referred to as "Participant") alleging that:

such Participant's Contributor Version directly or indirectly infringes any
        patent, then any and all rights granted by such Participant to You under Sections 2.1 and/or 2.2 of this License shall, upon 60
        days notice from Participant terminate prospectively, unless if within 60 days after receipt of notice You
        either: (i) agree in writing to pay Participant a mutually agreeable reasonable royalty for Your past and
        future use of Modifications made by such Participant, or (ii) withdraw Your litigation claim with respect to
        the Contributor Version against such Participant. If within 60 days of notice, a reasonable royalty and payment
        arrangement are not mutually agreed upon in writing by the parties or the litigation claim is not withdrawn,
        the rights granted by Participant to You under Sections 2.1 and/or
        2.2 automatically terminate at the expiration of the 60 day notice period
        specified above.

any software, hardware, or device, other than such Participant's Contributor
        Version, directly or indirectly infringes any patent, then any rights granted to You by such Participant under
        Sections 2.1(b) and 2.2(b) are
        revoked effective as of the date You first made, used, sold, distributed, or had made, Modifications made by
        that Participant.

8.3. If You assert a patent infringement claim against Participant alleging that such
    Participant's Contributor Version directly or indirectly infringes any patent where such claim is resolved (such as
    by license or settlement) prior to the initiation of patent infringement litigation, then the reasonable value of
    the licenses granted by such Participant under Sections 2.1 or 2.2 shall be taken into account in determining the amount or value of any payment or
    license.

8.4. In the event of termination under Sections 8.1 or 8.2 above, all end user license agreements
    (excluding distributors and resellers) which have been validly granted by You or any distributor hereunder prior to
    termination shall survive termination.

## 9. Limitation of liability

Under no circumstances and under no legal theory, whether tort (including
    negligence), contract, or otherwise, shall you, the initial developer, any other contributor, or any distributor of
    covered code, or any supplier of any of such parties, be liable to any person for any indirect, special,
    incidental, or consequential damages of any character including, without limitation, damages for loss of goodwill,
    work stoppage, computer failure or malfunction, or any and all other commercial damages or losses, even if such
    party shall have been informed of the possibility of such damages. This limitation of liability shall not apply to
    liability for death or personal injury resulting from such party's negligence to the extent applicable law
    prohibits such limitation. Some jurisdictions do not allow the exclusion or limitation of incidental or
    consequential damages, so this exclusion and limitation may not apply to you.

## 10. U.S. government end users

The Covered Code is a "commercial item," as that term is defined in 48 C.F.R. 2.101 (Oct. 1995), consisting of "commercial computer software" and "commercial computer software
    documentation," as such terms are used in 48 C.F.R. 12.212 (Sept.
    1995). Consistent with 48 C.F.R. 12.212 and 48 C.F.R. 227.7202-1 through 227.7202-4 (June
    1995), all U.S. Government End Users acquire Covered Code with only those rights set forth herein.

## 11. Miscellaneous

This License represents the complete agreement concerning subject matter hereof. If any provision of this
    License is held to be unenforceable, such provision shall be reformed only to the extent necessary to make it
    enforceable. This License shall be governed by California law provisions (except to the extent applicable law, if
    any, provides otherwise), excluding its conflict-of-law provisions. With respect to disputes in which at least one
    party is a citizen of, or an entity chartered or registered to do business in the United States of America, any
    litigation relating to this License shall be subject to the jurisdiction of the Federal Courts of the Northern
    District of California, with venue lying in Santa Clara County, California, with the losing party responsible for
    costs, including without limitation, court costs and reasonable attorneys' fees and expenses. The application of
    the United Nations Convention on Contracts for the International Sale of Goods is expressly excluded. Any law or
    regulation which provides that the language of a contract shall be construed against the drafter shall not apply to
    this License.

## 12. Responsibility for claims

As between Initial Developer and the Contributors, each party is responsible for claims and damages arising,
    directly or indirectly, out of its utilization of rights under this License and You agree to work with Initial
    Developer and Contributors to distribute such responsibility on an equitable basis. Nothing herein is intended or
    shall be deemed to constitute any admission of liability.

## 13. Multiple-licensed code

Initial Developer may designate portions of the Covered Code as "Multiple-Licensed". "Multiple-Licensed" means
    that the Initial Developer permits you to utilize portions of the Covered Code under Your choice of the
    MPL or the alternative licenses, if any, specified by the Initial Developer in the file described in
    Exhibit A.

## Exhibit A - Mozilla Public License.

NOTE: The text of this Exhibit A may differ slightly from the text of the notices in the Source Code files of
    the Original Code. You should use the text of this Exhibit A rather than the text found in the Original Code Source
    Code for Your Modifications.

# Mozilla Public License Version 2.0

## 1. Definitions

means each individual or legal entity that creates, contributes to the creation of, or owns Covered
            Software.

means the combination of the Contributions of others (if any) used by a Contributor and that particular
            Contributor´s Contribution.

means Covered Software of a particular Contributor.

means Source Code Form to which the initial Contributor has attached the notice in Exhibit A, the
            Executable Form of such Source Code Form, and Modifications of such Source Code Form, in each case
            including portions thereof.

means

that the initial Contributor has attached the notice described in Exhibit B to the Covered
                    Software; or

that the initial Contributor has attached the notice described in Exhibit B to the Covered
                    Software; or

that the Covered Software was made available under the terms of version 1.1 or earlier of the
                    License, but not also under the terms of a Secondary License.

that the Covered Software was made available under the terms of version 1.1 or earlier of the
                    License, but not also under the terms of a Secondary License.

means any form of the work other than Source Code Form.

means a work that combines Covered Software with other material, in a separate file or files, that is
            not Covered Software.

means this document.

means having the right to grant, to the maximum extent possible, whether at the time of the initial
            grant or subsequently, any and all of the rights conveyed by this License.

means any of the following:

any file in Source Code Form that results from an addition to, deletion from, or modification of
                    the contents of Covered Software; or

any file in Source Code Form that results from an addition to, deletion from, or modification of
                    the contents of Covered Software; or

any new file in Source Code Form that contains any Covered Software.

any new file in Source Code Form that contains any Covered Software.

means any patent claim(s), including without limitation, method, process, and apparatus claims, in any
            patent Licensable by such Contributor that would be infringed, but for the grant of the License, by the
            making, using, selling, offering for sale, having made, import, or transfer of either its Contributions or
            its Contributor Version.

means either the GNU General Public License, Version 2.0, the GNU Lesser General Public License, Version
            2.1, the GNU Affero General Public License, Version 3.0, or any later versions of those licenses.

means the form of the work preferred for making modifications.

means an individual or a legal entity exercising rights under this License. For legal entities,
            “You” includes any entity that controls, is controlled by, or is under common control with You. For
            purposes of this definition, “control” means (a) the power, direct or indirect, to cause the direction
            or management of such entity, whether by contract or otherwise, or (b) ownership of more than fifty percent
            (50%) of the outstanding shares or beneficial ownership of such entity.

## 2. License Grants and Conditions

### 2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free, non-exclusive license:

under intellectual property rights (other than patent or trademark) Licensable by such Contributor to
            use, reproduce, make available, modify, display, perform, distribute, and otherwise exploit its
            Contributions, either on an unmodified basis, with Modifications, or as part of a Larger Work; and

under intellectual property rights (other than patent or trademark) Licensable by such Contributor to
            use, reproduce, make available, modify, display, perform, distribute, and otherwise exploit its
            Contributions, either on an unmodified basis, with Modifications, or as part of a Larger Work; and

under Patent Claims of such Contributor to make, use, sell, offer for sale, have made, import, and
            otherwise transfer either its Contributions or its Contributor Version.

under Patent Claims of such Contributor to make, use, sell, offer for sale, have made, import, and
            otherwise transfer either its Contributions or its Contributor Version.

### 2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution become effective for each Contribution on
    the date the Contributor first distributes such Contribution.

### 2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under this License. No additional rights or
    licenses will be implied from the distribution or licensing of Covered Software under this License. Notwithstanding
    Section 2.1(b) above, no patent license is granted by a Contributor:

for any code that a Contributor has removed from Covered Software; or

for any code that a Contributor has removed from Covered Software; or

for infringements caused by: (i) Your and any other third party´s modifications of Covered Software, or
            (ii) the combination of its Contributions with other software (except as part of its Contributor Version);
            or

for infringements caused by: (i) Your and any other third party´s modifications of Covered Software, or
            (ii) the combination of its Contributions with other software (except as part of its Contributor Version);
            or

under Patent Claims infringed by Covered Software in the absence of its Contributions.

under Patent Claims infringed by Covered Software in the absence of its Contributions.

This License does not grant any rights in the trademarks, service marks, or logos of any Contributor (except as
    may be necessary to comply with the notice requirements in Section 3.4).

### 2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to distribute the Covered Software under a
    subsequent version of this License (see Section 10.2) or under the terms of a Secondary License (if permitted under
    the terms of Section 3.3).

### 2.5. Representation

Each Contributor represents that the Contributor believes its Contributions are its original creation(s) or it
    has sufficient rights to grant the rights to its Contributions conveyed by this License.

### 2.6. Fair Use

This License is not intended to limit any rights You have under applicable copyright doctrines of fair use, fair
    dealing, or other equivalents.

### 2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted in Section 2.1.

## 3. Responsibilities

### 3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any Modifications that You create or to
    which You contribute, must be under the terms of this License. You must inform recipients that the Source Code Form
    of the Covered Software is governed by the terms of this License, and how they can obtain a copy of this License.
    You may not attempt to alter or restrict the recipients´ rights in the Source Code Form.

### 3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

such Covered Software must also be made available in Source Code Form, as described in Section 3.1, and
            You must inform recipients of the Executable Form how they can obtain a copy of such Source Code Form by
            reasonable means in a timely manner, at a charge no more than the cost of distribution to the recipient;
            and

such Covered Software must also be made available in Source Code Form, as described in Section 3.1, and
            You must inform recipients of the Executable Form how they can obtain a copy of such Source Code Form by
            reasonable means in a timely manner, at a charge no more than the cost of distribution to the recipient;
            and

You may distribute such Executable Form under the terms of this License, or sublicense it under
            different terms, provided that the license for the Executable Form does not attempt to limit or alter the
            recipients´ rights in the Source Code Form under this License.

You may distribute such Executable Form under the terms of this License, or sublicense it under
            different terms, provided that the license for the Executable Form does not attempt to limit or alter the
            recipients´ rights in the Source Code Form under this License.

### 3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice, provided that You also comply with the
    requirements of this License for the Covered Software. If the Larger Work is a combination of Covered Software with
    a work governed by one or more Secondary Licenses, and the Covered Software is not Incompatible With Secondary
    Licenses, this License permits You to additionally distribute such Covered Software under the terms of such
    Secondary License(s), so that the recipient of the Larger Work may, at their option, further distribute the Covered
    Software under the terms of either this License or such Secondary License(s).

### 3.4. Notices

You may not remove or alter the substance of any license notices (including copyright notices, patent notices,
    disclaimers of warranty, or limitations of liability) contained within the Source Code Form of the Covered
    Software, except that You may alter any license notices to the extent required to remedy known factual
    inaccuracies.

### 3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support, indemnity or liability obligations to one
    or more recipients of Covered Software. However, You may do so only on Your own behalf, and not on behalf of any
    Contributor. You must make it absolutely clear that any such warranty, support, indemnity, or liability obligation
    is offered by You alone, and You hereby agree to indemnify every Contributor for any liability incurred by such
    Contributor as a result of warranty, support, indemnity or liability terms You offer. You may include additional
    disclaimers of warranty and limitations of liability specific to any jurisdiction.

## 4. Inability to Comply Due to Statute or Regulation

If it is impossible for You to comply with any of the terms of this License with respect to some or all of the
    Covered Software due to statute, judicial order, or regulation then You must: (a) comply with the terms of this
    License to the maximum extent possible; and (b) describe the limitations and the code they affect. Such description
    must be placed in a text file included with all distributions of the Covered Software under this License. Except to
    the extent prohibited by statute or regulation, such description must be sufficiently detailed for a recipient of
    ordinary skill to be able to understand it.

## 5. Termination

5.1. The rights granted under this License will terminate automatically if You fail to comply with any of its
    terms. However, if You become compliant, then the rights granted under this License from a particular Contributor
    are reinstated (a) provisionally, unless and until such Contributor explicitly and finally terminates Your grants,
    and (b) on an ongoing basis, if such Contributor fails to notify You of the non-compliance by some reasonable means
    prior to 60 days after You have come back into compliance. Moreover, Your grants from a particular Contributor are
    reinstated on an ongoing basis if such Contributor notifies You of the non-compliance by some reasonable means,
    this is the first time You have received notice of non-compliance with this License from such Contributor, and You
    become compliant prior to 30 days after Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent infringement claim (excluding
    declaratory judgment actions, counter-claims, and cross-claims) alleging that a Contributor Version directly or
    indirectly infringes any patent, then the rights granted to You by any and all Contributors for the Covered
    Software under Section 2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all end user license agreements (excluding
    distributors and resellers) which have been validly granted by You or Your distributors under this License prior to
    termination shall survive termination.

## 6. Disclaimer of Warranty

Covered Software is provided under this License on an “as is” basis, without warranty of any kind,
    either expressed, implied, or statutory, including, without limitation, warranties that the Covered Software is
    free of defects, merchantable, fit for a particular purpose or non-infringing. The entire risk as to the quality
    and performance of the Covered Software is with You. Should any Covered Software prove defective in any respect,
    You (not any Contributor) assume the cost of any necessary servicing, repair, or correction. This disclaimer of
    warranty constitutes an essential part of this License. No use of any Covered Software is authorized under this
    License except under this disclaimer.

## 7. Limitation of Liability

Under no circumstances and under no legal theory, whether tort (including negligence), contract, or
    otherwise, shall any Contributor, or anyone who distributes Covered Software as permitted above, be liable to You
    for any direct, indirect, special, incidental, or consequential damages of any character including, without
    limitation, damages for lost profits, loss of goodwill, work stoppage, computer failure or malfunction, or any and
    all other commercial damages or losses, even if such party shall have been informed of the possibility of such
    damages. This limitation of liability shall not apply to liability for death or personal injury resulting from such
    party´s negligence to the extent applicable law prohibits such limitation. Some jurisdictions do not allow the
    exclusion or limitation of incidental or consequential damages, so this exclusion and limitation may not apply to
    You.

## 8. Litigation

Any litigation relating to this License may be brought only in the courts of a jurisdiction where the defendant
    maintains its principal place of business and such litigation shall be governed by laws of that jurisdiction,
    without reference to its conflict-of-law provisions. Nothing in this Section shall prevent a party´s ability to
    bring cross-claims or counter-claims.

## 9. Miscellaneous

This License represents the complete agreement concerning the subject matter hereof. If any provision of this
    License is held to be unenforceable, such provision shall be reformed only to the extent necessary to make it
    enforceable. Any law or regulation which provides that the language of a contract shall be construed against the
    drafter shall not be used to construe this License against a Contributor.

## 10. Versions of the License

### 10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section 10.3, no one other than the license
    steward has the right to modify or publish new versions of this License. Each version will be given a
    distinguishing version number.

### 10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version of the License under which You originally
    received the Covered Software, or under the terms of any subsequent version published by the license steward.

### 10.3. Modified Versions

If you create software not governed by this License, and you want to create a new license for such software, you
    may create and use a modified version of this License if you rename the license and remove any references to the
    name of the license steward (except to note that such modified license differs from this License).

### 10.4. Distributing Source Code Form that is Incompatible With Secondary Licenses

If You choose to distribute Source Code Form that is Incompatible With Secondary Licenses under the terms of
    this version of the License, the notice described in Exhibit B of this License must be attached.

## Exhibit A - Source Code Form License Notice

This Source Code Form is subject to the terms of the Mozilla Public License, v. 2.0. If a copy of the MPL
        was not distributed with this file, You can obtain one at https://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular file, then You may include the notice in a
    location (such as a LICENSE file in a relevant directory) where a recipient would be likely to look for such a
    notice.

You may add additional accurate notices of copyright ownership.

## Exhibit B - “Incompatible With Secondary Licenses” Notice

This Source Code Form is “Incompatible With Secondary Licenses”, as defined by the Mozilla Public
        License, v. 2.0.

# SIL Open Font
    License Version 1.1 – 26 February 2007

PREAMBLE The goals of the Open Font License (OFL) are to stimulate worldwide development of collaborative font
    projects, to support the font creation efforts of academic and linguistic communities, and to provide a free and
    open framework in which fonts may be shared and improved in partnership with others.

The OFL allows the licensed fonts to be used, studied, modified and redistributed freely as long as they are not
    sold by themselves. The fonts, including any derivative works, can be bundled, embedded, redistributed and/or sold
    with any software provided that the font names of derivative works are changed. The fonts and derivatives, however,
    cannot be released under any other type of license. The requirement for fonts to remain under this license does not
    apply to any document created using the fonts or their derivatives.

DEFINITIONS "Font Software" refers to the set of files released by the Copyright Holder(s) under this license
    and clearly marked as such. This may include source files, build scripts and documentation.

"Reserved Font Name" refers to any names specified as such after the copyright statement(s).

"Original Version" refers to the collection of Font Software components as distributed by the Copyright
    Holder(s).

"Modified Version" refers to any derivative made by adding to, deleting, or substituting – in part or in whole
    -- any of the components of the Original Version, by changing formats or by porting the Font Software to a new
    environment.

"Author" refers to any designer, engineer, programmer, technical writer or other person who contributed to the
    Font Software.

PERMISSION & CONDITIONS Permission is hereby granted, free of charge, to any person obtaining a copy of the
    Font Software, to use, study, copy, merge, embed, modify, redistribute, and sell modified and unmodified copies of
    the Font Software, subject to the following conditions:

1) Neither the Font Software nor any of its individual components, in Original or Modified Versions, may be sold
    by itself.

2) Original or Modified Versions of the Font Software may be bundled, redistributed and/or sold with any
    software, provided that each copy contains the above copyright notice and this license. These can be included
    either as stand-alone text files, human-readable headers or in the appropriate machine-readable metadata fields
    within text or binary files as long as those fields can be easily viewed by the user.

3) No Modified Version of the Font Software may use the Reserved Font Name(s) unless explicit written permission
    is granted by the corresponding Copyright Holder. This restriction only applies to the primary font name as
    presented to the users.

4) The name(s) of the Copyright Holder(s) or the Author(s) of the Font Software shall not be used to promote,
    endorse or advertise any Modified Version, except to acknowledge the contribution(s) of the Copyright Holder(s) and
    the Author(s) or with their explicit written permission.

5) The Font Software, modified or unmodified, in part or in whole, must be distributed entirely under this
    license, and must not be distributed under any other license. The requirement for fonts to remain under this
    license does not apply to any document created using the Font Software.

TERMINATION This license becomes null and void if any of the above conditions are not met.

DISCLAIMER THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
    BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF
    COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE COPYRIGHT HOLDER BE LIABLE FOR ANY CLAIM,
    DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER
    IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR
    FROM OTHER DEALINGS IN THE FONT SOFTWARE.

# Apache License

Version 2.0, January 2004

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

"License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1
    through 9 of this document.

"Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the
    License.

"Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by,
    or are under common control with that entity. For the purposes of this definition, "control" means (i) the power,
    direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii)
    ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such
    entity.

"You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

"Source" form shall mean the preferred form for making modifications, including but not limited to software
    source code, documentation source, and configuration files.

"Object" form shall mean any form resulting from mechanical transformation or translation of a Source form,
    including but not limited to compiled object code, generated documentation, and conversions to other media
    types.

"Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as
    indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix
    below).

"Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the
    Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a
    whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works
    that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works
    thereof.

"Contribution" shall mean any work of authorship, including the original version of the Work and any
    modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor
    for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf
    of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or
    written communication sent to the Licensor or its representatives, including but not limited to communication on
    electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf
    of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is
    conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

"Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been
    received by Licensor and subsequently incorporated within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of this License, each
    Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable
    copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and
    distribute the Work and such Derivative Works in Source or Object form.

3. Grant of Patent License. Subject to the terms and conditions of this License, each
    Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable
    (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and
    otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor
    that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the
    Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including
    a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work
    constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License
    for that Work shall terminate as of the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works
    thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the
    following conditions:

You must give any other recipients of the Work or Derivative Works a copy of this License; and

You must cause any modified files to carry prominent notices stating that You changed the files; and

You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent,
        trademark, and attribution notices from the Source form of the Work, excluding those notices that do not
        pertain to any part of the Derivative Works; and

If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You
        distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding
        those notices that do not pertain to any part of the Derivative Works, in at least one of the following places:
        within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation,
        if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and
        wherever such third-party notices normally appear. The contents of the NOTICE file are for informational
        purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works
        that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such
        additional attribution notices cannot be construed as modifying the License. You may add Your own copyright
        statement to Your modifications and may provide additional or different license terms and conditions for use,
        reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your
        use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this
        License.

5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution
    intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of
    this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede
    or modify the terms of any separate license agreement you may have executed with Licensor regarding such
    Contributions.

6. Trademarks. This License does not grant permission to use the trade names, trademarks,
    service marks, or product names of the Licensor, except as required for reasonable and customary use in describing
    the origin of the Work and reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor
    provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR
    CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of
    TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for
    determining the appropriateness of using or redistributing the Work and assume any risks associated with Your
    exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory, whether in tort (including
    negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent
    acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect,
    special, incidental, or consequential damages of any character arising as a result of this License or out of the
    use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage,
    computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has
    been advised of the possibility of such damages.

9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative
    Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other
    liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may
    act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if
    You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims
    asserted against, such Contributor by reason of your accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

## APPENDIX: How to apply the Apache License to your work

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by
    brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be
    enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and
    description of purpose be included on the same "printed page" as the copyright notice for easier identification
    within third-party archives.

   Copyright [yyyy] [name of copyright owner]

 

   Licensed under the Apache License, Version 2.0 (the "License");

   you may not use this file except in compliance with the License.

   You may obtain a copy of the License at

 

       http://www.apache.org/licenses/LICENSE-2.0

 

   Unless required by applicable law or agreed to in writing, software

   distributed under the License is distributed on an "AS IS" BASIS,

   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

   See the License for the specific language governing permissions and

   limitations under the License.

# The LaTeX Project Public License

LPPL Version 1.3c 2008-05-04

Copyright 1999 2002-2008 LaTeX3 Project

Everyone is allowed to distribute verbatim copies of this license document, but modification of it is not
        allowed.

## PREAMBLE

The LaTeX Project Public License (LPPL) is the primary license under which the LaTeX kernel and the base
        LaTeX packages are distributed.

You may use this license for any work of which you hold the copyright and which you wish to distribute. This
        license may be particularly suitable if your work is TeX-related (such as a LaTeX package), but it is written
        in such a way that you can use it even if your work is unrelated to TeX.

The section `WHETHER AND HOW TO DISTRIBUTE WORKS UNDER THIS LICENSE', below, gives instructions, examples,
        and recommendations for authors who are considering distributing their works under this license.

This license gives conditions under which a work may be distributed and modified, as well as conditions
        under which modified versions of that work may be distributed.

We, the LaTeX3 Project, believe that the conditions below give you the freedom to make and distribute
        modified versions of your work that conform with whatever technical specifications you wish while maintaining
        the availability, integrity, and reliability of that work. If you do not see how to achieve your goal while
        meeting these conditions, then read the document `cfgguide.tex' and `modguide.tex' in the base LaTeX
        distribution for suggestions.

## DEFINITIONS

In this license document the following terms are used:

## CONDITIONS ON DISTRIBUTION AND MODIFICATION

Activities other than distribution and/or modification of the Work are not covered by this license;
            they are outside its scope. In particular, the act of running the Work is not restricted and no
            requirements are made concerning any offers of support for the Work.

You may distribute a complete, unmodified copy of the Work as you received it. Distribution of only
            part of the Work is considered modification of the Work, and no right to distribute such a Derived Work may
            be assumed under the terms of this clause.

You may distribute a Compiled Work that has been generated from a complete, unmodified copy of the Work
            as distributed under Clause 2 above, as long as that Compiled Work is distributed in such a way that the
            recipients may install the Compiled Work on their system exactly as it would have been installed if they
            generated a Compiled Work directly from the Work.

If you are the Current Maintainer of the Work, you may, without restriction, modify the Work, thus
            creating a Derived Work. You may also distribute the Derived Work without restriction, including Compiled
            Works generated from the Derived Work. Derived Works distributed in this manner by the Current Maintainer
            are considered to be updated versions of the Work.

If you are not the Current Maintainer of the Work, you may modify your copy of the Work, thus creating
            a Derived Work based on the Work, and compile this Derived Work, thus creating a Compiled Work based on the
            Derived Work.

If you are not the Current Maintainer of the Work, you may distribute a Derived Work provided the
            following conditions are met for every component of the Work unless that component clearly states in the
            copyright notice that it is exempt from that condition. Only the Current Maintainer is allowed to add such
            statements of exemption to a component of the Work.
                If a component of this Derived Work can be a direct replacement for a component of the Work
                    when that component is used with the Base Interpreter, then, wherever this component of the Work
                    identifies itself to the user when used interactively with that Base Interpreter, the replacement
                    component of this Derived Work clearly and unambiguously identifies itself as a modified version of
                    this component to the user when used interactively with that Base Interpreter.Every component of the Derived Work contains prominent notices detailing the nature of the
                    changes to that component, or a prominent reference to another file that is distributed as part of
                    the Derived Work and that contains a complete and accurate log of the changes.No information in the Derived Work implies that any persons, including (but not limited to) the
                    authors of the original version of the Work, provide any support, including (but not limited to)
                    the reporting and handling of errors, to recipients of the Derived Work unless those persons have
                    stated explicitly that they do provide such support for the Derived Work.You distribute at least one of the following with the Derived Work:
                        A complete, unmodified copy of the Work; if your distribution of a modified component
                            is made by offering access to copy the modified component from a designated place, then
                            offering equivalent access to copy the Work from the same or some similar place meets this
                            condition, even though third parties are not compelled to copy the Work along with the
                            modified component;Information that is sufficient to obtain a complete, unmodified copy of the Work.

If a component of this Derived Work can be a direct replacement for a component of the Work
                    when that component is used with the Base Interpreter, then, wherever this component of the Work
                    identifies itself to the user when used interactively with that Base Interpreter, the replacement
                    component of this Derived Work clearly and unambiguously identifies itself as a modified version of
                    this component to the user when used interactively with that Base Interpreter.

Every component of the Derived Work contains prominent notices detailing the nature of the
                    changes to that component, or a prominent reference to another file that is distributed as part of
                    the Derived Work and that contains a complete and accurate log of the changes.

No information in the Derived Work implies that any persons, including (but not limited to) the
                    authors of the original version of the Work, provide any support, including (but not limited to)
                    the reporting and handling of errors, to recipients of the Derived Work unless those persons have
                    stated explicitly that they do provide such support for the Derived Work.

You distribute at least one of the following with the Derived Work:
                        A complete, unmodified copy of the Work; if your distribution of a modified component
                            is made by offering access to copy the modified component from a designated place, then
                            offering equivalent access to copy the Work from the same or some similar place meets this
                            condition, even though third parties are not compelled to copy the Work along with the
                            modified component;Information that is sufficient to obtain a complete, unmodified copy of the Work.

A complete, unmodified copy of the Work; if your distribution of a modified component
                            is made by offering access to copy the modified component from a designated place, then
                            offering equivalent access to copy the Work from the same or some similar place meets this
                            condition, even though third parties are not compelled to copy the Work along with the
                            modified component;

Information that is sufficient to obtain a complete, unmodified copy of the Work.

If you are not the Current Maintainer of the Work, you may distribute a Compiled Work generated from a
            Derived Work, as long as the Derived Work is distributed to all recipients of the Compiled Work, and as
            long as the conditions of Clause 6, above, are met with regard to the Derived Work.

The conditions above are not intended to prohibit, and hence do not apply to, the modification, by any
            method, of any component so that it becomes identical to an updated version of that component of the Work
            as it is distributed by the Current Maintainer under Clause 4, above.

Distribution of the Work or any Derived Work in an alternative format, where the Work or that Derived
            Work (in whole or in part) is then produced by applying some process to that format, does not relax or
            nullify any sections of this license as they pertain to the results of applying that process.

A Derived Work may be distributed under a different license provided that license itself honors
                    the conditions listed in Clause 6 above, in regard to the Work, though it does not have to honor
                    the rest of the conditions in this license.If a Derived Work is distributed under a different license, that Derived Work must provide
                    sufficient documentation as part of itself to allow each recipient of that Derived Work to honor
                    the restrictions in Clause 6 above, concerning changes from the Work.

A Derived Work may be distributed under a different license provided that license itself honors
                    the conditions listed in Clause 6 above, in regard to the Work, though it does not have to honor
                    the rest of the conditions in this license.

If a Derived Work is distributed under a different license, that Derived Work must provide
                    sufficient documentation as part of itself to allow each recipient of that Derived Work to honor
                    the restrictions in Clause 6 above, concerning changes from the Work.

This license places no restrictions on works that are unrelated to the Work, nor does this license
            place any restrictions on aggregating such works with the Work by any means.

Nothing in this license is intended to, or may be used to, prevent complete compliance by all parties
            with all applicable laws.

## NO WARRANTY

There is no warranty for the Work. Except when otherwise stated in writing, the Copyright Holder provides
        the Work `as is', without warranty of any kind, either expressed or implied, including, but not limited to, the
        implied warranties of merchantability and fitness for a particular purpose. The entire risk as to the quality
        and performance of the Work is with you. Should the Work prove defective, you assume the cost of all necessary
        servicing, repair, or correction.

In no event unless required by applicable law or agreed to in writing will The Copyright Holder, or any
        author named in the components of the Work, or any other party who may distribute and/or modify the Work as
        permitted above, be liable to you for damages, including any general, special, incidental or consequential
        damages arising out of any use of the Work or out of inability to use the Work (including, but not limited to,
        loss of data, data being rendered inaccurate, or losses sustained by anyone as a result of any failure of the
        Work to operate with any other programs), even if the Copyright Holder or said author or said other party has
        been advised of the possibility of such damages.

## MAINTENANCE OF THE WORK

The Work has the status `author-maintained' if the Copyright Holder explicitly and prominently states near
        the primary copyright notice in the Work that the Work can only be maintained by the Copyright Holder or simply
        that it is `author-maintained'.

The Work has the status `maintained' if there is a Current Maintainer who has indicated in the Work that
        they are willing to receive error reports for the Work (for example, by supplying a valid e-mail address). It
        is not required for the Current Maintainer to acknowledge or act upon these error reports.

The Work changes from status `maintained' to `unmaintained' if there is no Current Maintainer, or the person
        stated to be Current Maintainer of the work cannot be reached through the indicated means of communication for
        a period of six months, and there are no other significant signs of active maintenance.

You can become the Current Maintainer of the Work by agreement with any existing Current Maintainer to take
        over this role.

If the Work is unmaintained, you can become the Current Maintainer of the Work through the following
        steps:

Make a reasonable attempt to trace the Current Maintainer (and the Copyright Holder, if the two differ)
            through the means of an Internet or similar search.

If this search is successful, then enquire whether the Work is still maintained.
                If it is being maintained, then ask the Current Maintainer to update their communication data
                    within one month.If the search is unsuccessful or no action to resume active maintenance is taken by the Current
                    Maintainer, then announce within the pertinent community your intention to take over maintenance.
                    (If the Work is a LaTeX work, this could be done, for example, by posting to comp.text.tex.)

If it is being maintained, then ask the Current Maintainer to update their communication data
                    within one month.

If the search is unsuccessful or no action to resume active maintenance is taken by the Current
                    Maintainer, then announce within the pertinent community your intention to take over maintenance.
                    (If the Work is a LaTeX work, this could be done, for example, by posting to comp.text.tex.)

If the Current Maintainer is reachable and agrees to pass maintenance of the Work to you, then
                    this takes effect immediately upon announcement.If the Current Maintainer is not reachable and the Copyright Holder agrees that maintenance of
                    the Work be passed to you, then this takes effect immediately upon announcement.

If the Current Maintainer is reachable and agrees to pass maintenance of the Work to you, then
                    this takes effect immediately upon announcement.

If the Current Maintainer is not reachable and the Copyright Holder agrees that maintenance of
                    the Work be passed to you, then this takes effect immediately upon announcement.

If you make an `intention announcement' as described in 2b. above and after three months your intention
            is challenged neither by the Current Maintainer nor by the Copyright Holder nor by other people, then you
            may arrange for the Work to be changed so as to name you as the (new) Current Maintainer.

If the previously unreachable Current Maintainer becomes reachable once more within three months of a
            change completed under the terms of 3b) or 4), then that Current Maintainer must become or remain the
            Current Maintainer upon request provided they then update their communication data within one month.

A change in the Current Maintainer does not, of itself, alter the fact that the Work is distributed under
        the LPPL license.

If you become the Current Maintainer of the Work, you should immediately provide, within the Work, a
        prominent and unambiguous statement of your status as Current Maintainer. You should also announce your new
        status to the same pertinent community as in 2b) above.

## WHETHER AND HOW TO DISTRIBUTE WORKS UNDER THIS LICENSE

This section contains important instructions, examples, and recommendations for authors who are considering
        distributing their works under this license. These authors are addressed as `you' in this section.

### Choosing This License or Another License

If for any part of your work you want or need to use *distribution* conditions that differ significantly
        from those in this license, then do not refer to this license anywhere in your work but, instead, distribute
        your work under a different license. You may use the text of this license as a model for your own license, but
        your license should not refer to the LPPL or otherwise give the impression that your work is distributed under
        the LPPL.

The document `modguide.tex' in the base LaTeX distribution explains the motivation behind the conditions of
        this license. It explains, for example, why distributing LaTeX under the GNU General Public License (GPL) was
        considered inappropriate. Even if your work is unrelated to LaTeX, the discussion in `modguide.tex' may still
        be relevant, and authors intending to distribute their works under any license are encouraged to read it.

### A Recommendation on Modification Without Distribution

It is wise never to modify a component of the Work, even for your own personal use, without also meeting the
        above conditions for distributing the modified component. While you might intend that such modifications will
        never be distributed, often this will happen by accident — you may forget that you have modified that
        component; or it may not occur to you when allowing others to access the modified version that you are thus
        distributing it and violating the conditions of this license in ways that could have legal implications and,
        worse, cause problems for the community. It is therefore usually in your best interest to keep your copy of the
        Work identical with the public one. Many works provide ways to control the behavior of that work without
        altering any of its licensed components.

### How to Use This License

To use this license, place in each of the components of your work both an explicit copyright notice
        including your name and the year the work was authored and/or last substantially modified. Include also a
        statement that the distribution and/or modification of that component is constrained by the conditions in this
        license.

Here is an example of such a notice and statement:

Given such a notice and statement in a file, the conditions given in this license document would apply, with
        the `Work' referring to the three files `pig.dtx', `pig.ins', and `pig.sty' (the last being generated from
        `pig.dtx' using `pig.ins'), the `Base Interpreter' referring to any `LaTeX-Format', and both `Copyright Holder'
        and `Current Maintainer' referring to the person `M. Y. Name'.

If you do not want the Maintenance section of LPPL to apply to your Work, change `maintained' above into
        `author-maintained'. However, we recommend that you use `maintained', as the Maintenance section was added in
        order to ensure that your Work remains useful to the community even when you can no longer maintain and support
        it yourself.

### Derived Works That Are Not Replacements

Several clauses of the LPPL specify means to provide reliability and stability for the user community. They
        therefore concern themselves with the case that a Derived Work is intended to be used as a (compatible or
        incompatible) replacement of the original Work. If this is not the case (e.g., if a few lines of code are
        reused for a completely different task), then clauses 6b and 6d shall not apply.

### Important Recommendations

The LPPL requires that distributions of the Work contain all the files of the Work. It is therefore
        important that you provide a way for the licensee to determine which files constitute the Work. This could, for
        example, be achieved by explicitly listing all the files of the Work near the copyright notice of each file or
        by using a line such as:

in that place. In the absence of an unequivocal list it might be impossible for the licensee to determine
        what is considered by you to comprise the Work and, in such a case, the licensee would be entitled to make
        reasonable conjectures as to which files comprise the Work.

# Creative Commons Attribution-ShareAlike 3.0 Unported

### License

THE WORK (AS DEFINED BELOW) IS PROVIDED UNDER THE TERMS OF THIS CREATIVE COMMONS PUBLIC LICENSE ("CCPL" OR
    "LICENSE"). THE WORK IS PROTECTED BY COPYRIGHT AND/OR OTHER APPLICABLE LAW. ANY USE OF THE WORK OTHER THAN AS
    AUTHORIZED UNDER THIS LICENSE OR COPYRIGHT LAW IS PROHIBITED.

BY EXERCISING ANY RIGHTS TO THE WORK PROVIDED HERE, YOU ACCEPT AND AGREE TO BE BOUND BY THE TERMS OF THIS
    LICENSE. TO THE EXTENT THIS LICENSE MAY BE CONSIDERED TO BE A CONTRACT, THE LICENSOR GRANTS YOU THE RIGHTS
    CONTAINED HERE IN CONSIDERATION OF YOUR ACCEPTANCE OF SUCH TERMS AND CONDITIONS.

1. Definitions

"Adaptation" means a work based upon the Work, or upon the Work and other pre-existing
        works, such as a translation, adaptation, derivative work, arrangement of music or other alterations of a
        literary or artistic work, or phonogram or performance and includes cinematographic adaptations or any other
        form in which the Work may be recast, transformed, or adapted including in any form recognizably derived from
        the original, except that a work that constitutes a Collection will not be considered an Adaptation for the
        purpose of this License. For the avoidance of doubt, where the Work is a musical work, performance or
        phonogram, the synchronization of the Work in timed-relation with a moving image ("synching") will be
        considered an Adaptation for the purpose of this License.

"Collection" means a collection of literary or artistic works, such as encyclopedias and
        anthologies, or performances, phonograms or broadcasts, or other works or subject matter other than works
        listed in Section 1(f) below, which, by reason of the selection and arrangement of their contents, constitute
        intellectual creations, in which the Work is included in its entirety in unmodified form along with one or more
        other contributions, each constituting separate and independent works in themselves, which together are
        assembled into a collective whole. A work that constitutes a Collection will not be considered an Adaptation
        (as defined below) for the purposes of this License.

"Creative Commons Compatible License" means a license that is listed at
        https://creativecommons.org/compatiblelicenses that has been approved by Creative Commons as being essentially
        equivalent to this License, including, at a minimum, because that license: (i) contains terms that have the
        same purpose, meaning and effect as the License Elements of this License; and, (ii) explicitly permits the
        relicensing of adaptations of works made available under that license under this License or a Creative Commons
        jurisdiction license with the same License Elements as this License.

"Distribute" means to make available to the public the original and copies of the Work or
        Adaptation, as appropriate, through sale or other transfer of ownership.

"License Elements" means the following high-level license attributes as selected by
        Licensor and indicated in the title of this License: Attribution, ShareAlike.

"Licensor" means the individual, individuals, entity or entities that offer(s) the Work
        under the terms of this License.

"Original Author" means, in the case of a literary or artistic work, the individual,
        individuals, entity or entities who created the Work or if no individual or entity can be identified, the
        publisher; and in addition (i) in the case of a performance the actors, singers, musicians, dancers, and other
        persons who act, sing, deliver, declaim, play in, interpret or otherwise perform literary or artistic works or
        expressions of folklore; (ii) in the case of a phonogram the producer being the person or legal entity who
        first fixes the sounds of a performance or other sounds; and, (iii) in the case of broadcasts, the organization
        that transmits the broadcast.

"Work" means the literary and/or artistic work offered under the terms of this License
        including without limitation any production in the literary, scientific and artistic domain, whatever may be
        the mode or form of its expression including digital form, such as a book, pamphlet and other writing; a
        lecture, address, sermon or other work of the same nature; a dramatic or dramatico-musical work; a
        choreographic work or entertainment in dumb show; a musical composition with or without words; a
        cinematographic work to which are assimilated works expressed by a process analogous to cinematography; a work
        of drawing, painting, architecture, sculpture, engraving or lithography; a photographic work to which are
        assimilated works expressed by a process analogous to photography; a work of applied art; an illustration, map,
        plan, sketch or three-dimensional work relative to geography, topography, architecture or science; a
        performance; a broadcast; a phonogram; a compilation of data to the extent it is protected as a copyrightable
        work; or a work performed by a variety or circus performer to the extent it is not otherwise considered a
        literary or artistic work.

"You" means an individual or entity exercising rights under this License who has not
        previously violated the terms of this License with respect to the Work, or who has received express permission
        from the Licensor to exercise rights under this License despite a previous violation.

"Publicly Perform" means to perform public recitations of the Work and to communicate to
        the public those public recitations, by any means or process, including by wire or wireless means or public
        digital performances; to make available to the public Works in such a way that members of the public may access
        these Works from a place and at a place individually chosen by them; to perform the Work to the public by any
        means or process and the communication to the public of the performances of the Work, including by public
        digital performance; to broadcast and rebroadcast the Work by any means including signs, sounds or images.

"Reproduce" means to make copies of the Work by any means including without limitation by
        sound or visual recordings and the right of fixation and reproducing fixations of the Work, including storage
        of a protected performance or phonogram in digital form or other electronic medium.

2. Fair Dealing Rights. Nothing in this License is intended to reduce, limit, or restrict any
    uses free from copyright or rights arising from limitations or exceptions that are provided for in connection with
    the copyright protection under copyright law or other applicable laws.

3. License Grant. Subject to the terms and conditions of this License, Licensor hereby grants
    You a worldwide, royalty-free, non-exclusive, perpetual (for the duration of the applicable copyright) license to
    exercise the rights in the Work as stated below:

to Reproduce the Work, to incorporate the Work into one or more Collections, and to Reproduce the Work as
        incorporated in the Collections;

to create and Reproduce Adaptations provided that any such Adaptation, including any translation in any
        medium, takes reasonable steps to clearly label, demarcate or otherwise identify that changes were made to the
        original Work. For example, a translation could be marked "The original work was translated from English to
        Spanish," or a modification could indicate "The original work has been modified.";

to Distribute and Publicly Perform the Work including as incorporated in Collections; and,

to Distribute and Publicly Perform Adaptations.

For the avoidance of doubt:Non-waivable Compulsory License Schemes. In those jurisdictions in which the right
                to collect royalties through any statutory or compulsory licensing scheme cannot be waived, the
                Licensor reserves the exclusive right to collect such royalties for any exercise by You of the rights
                granted under this License;Waivable Compulsory License Schemes. In those jurisdictions in which the right to
                collect royalties through any statutory or compulsory licensing scheme can be waived, the Licensor
                waives the exclusive right to collect such royalties for any exercise by You of the rights granted
                under this License; and,Voluntary License Schemes. The Licensor waives the right to collect royalties,
                whether individually or, in the event that the Licensor is a member of a collecting society that
                administers voluntary licensing schemes, via that society, from any exercise by You of the rights
                granted under this License.

For the avoidance of doubt:

Non-waivable Compulsory License Schemes. In those jurisdictions in which the right
                to collect royalties through any statutory or compulsory licensing scheme cannot be waived, the
                Licensor reserves the exclusive right to collect such royalties for any exercise by You of the rights
                granted under this License;

Waivable Compulsory License Schemes. In those jurisdictions in which the right to
                collect royalties through any statutory or compulsory licensing scheme can be waived, the Licensor
                waives the exclusive right to collect such royalties for any exercise by You of the rights granted
                under this License; and,

Voluntary License Schemes. The Licensor waives the right to collect royalties,
                whether individually or, in the event that the Licensor is a member of a collecting society that
                administers voluntary licensing schemes, via that society, from any exercise by You of the rights
                granted under this License.

The above rights may be exercised in all media and formats whether now known or hereafter devised. The above
    rights include the right to make such modifications as are technically necessary to exercise the rights in other
    media and formats. Subject to Section 8(f), all rights not expressly granted by Licensor are hereby reserved.

4. Restrictions. The license granted in Section 3 above is expressly made subject to and
    limited by the following restrictions:

You may Distribute or Publicly Perform the Work only under the terms of this License. You must include a
        copy of, or the Uniform Resource Identifier (URI) for, this License with every copy of the Work You Distribute
        or Publicly Perform. You may not offer or impose any terms on the Work that restrict the terms of this License
        or the ability of the recipient of the Work to exercise the rights granted to that recipient under the terms of
        the License. You may not sublicense the Work. You must keep intact all notices that refer to this License and
        to the disclaimer of warranties with every copy of the Work You Distribute or Publicly Perform. When You
        Distribute or Publicly Perform the Work, You may not impose any effective technological measures on the Work
        that restrict the ability of a recipient of the Work from You to exercise the rights granted to that recipient
        under the terms of the License. This Section 4(a) applies to the Work as incorporated in a Collection, but this
        does not require the Collection apart from the Work itself to be made subject to the terms of this License. If
        You create a Collection, upon notice from any Licensor You must, to the extent practicable, remove from the
        Collection any credit as required by Section 4(c), as requested. If You create an Adaptation, upon notice from
        any Licensor You must, to the extent practicable, remove from the Adaptation any credit as required by Section
        4(c), as requested.

You may Distribute or Publicly Perform an Adaptation only under the terms of: (i) this License; (ii) a
        later version of this License with the same License Elements as this License; (iii) a Creative Commons
        jurisdiction license (either this or a later license version) that contains the same License Elements as this
        License (e.g., Attribution-ShareAlike 3.0 US)); (iv) a Creative Commons Compatible License. If you license the
        Adaptation under one of the licenses mentioned in (iv), you must comply with the terms of that license. If you
        license the Adaptation under the terms of any of the licenses mentioned in (i), (ii) or (iii) (the "Applicable
        License"), you must comply with the terms of the Applicable License generally and the following provisions: (I)
        You must include a copy of, or the URI for, the Applicable License with every copy of each Adaptation You
        Distribute or Publicly Perform; (II) You may not offer or impose any terms on the Adaptation that restrict the
        terms of the Applicable License or the ability of the recipient of the Adaptation to exercise the rights
        granted to that recipient under the terms of the Applicable License; (III) You must keep intact all notices
        that refer to the Applicable License and to the disclaimer of warranties with every copy of the Work as
        included in the Adaptation You Distribute or Publicly Perform; (IV) when You Distribute or Publicly Perform the
        Adaptation, You may not impose any effective technological measures on the Adaptation that restrict the ability
        of a recipient of the Adaptation from You to exercise the rights granted to that recipient under the terms of
        the Applicable License. This Section 4(b) applies to the Adaptation as incorporated in a Collection, but this
        does not require the Collection apart from the Adaptation itself to be made subject to the terms of the
        Applicable License.

If You Distribute, or Publicly Perform the Work or any Adaptations or Collections, You must, unless a
        request has been made pursuant to Section 4(a), keep intact all copyright notices for the Work and provide,
        reasonable to the medium or means You are utilizing: (i) the name of the Original Author (or pseudonym, if
        applicable) if supplied, and/or if the Original Author and/or Licensor designate another party or parties
        (e.g., a sponsor institute, publishing entity, journal) for attribution ("Attribution Parties") in Licensor's
        copyright notice, terms of service or by other reasonable means, the name of such party or parties; (ii) the
        title of the Work if supplied; (iii) to the extent reasonably practicable, the URI, if any, that Licensor
        specifies to be associated with the Work, unless such URI does not refer to the copyright notice or licensing
        information for the Work; and (iv) , consistent with Ssection 3(b), in the case of an Adaptation, a credit
        identifying the use of the Work in the Adaptation (e.g., "French translation of the Work by Original Author,"
        or "Screenplay based on original Work by Original Author"). The credit required by this Section 4(c) may be
        implemented in any reasonable manner; provided, however, that in the case of a Adaptation or Collection, at a
        minimum such credit will appear, if a credit for all contributing authors of the Adaptation or Collection
        appears, then as part of these credits and in a manner at least as prominent as the credits for the other
        contributing authors. For the avoidance of doubt, You may only use the credit required by this Section for the
        purpose of attribution in the manner set out above and, by exercising Your rights under this License, You may
        not implicitly or explicitly assert or imply any connection with, sponsorship or endorsement by the Original
        Author, Licensor and/or Attribution Parties, as appropriate, of You or Your use of the Work, without the
        separate, express prior written permission of the Original Author, Licensor and/or Attribution Parties.

Except as otherwise agreed in writing by the Licensor or as may be otherwise permitted by applicable law,
        if You Reproduce, Distribute or Publicly Perform the Work either by itself or as part of any Adaptations or
        Collections, You must not distort, mutilate, modify or take other derogatory action in relation to the Work
        which would be prejudicial to the Original Author's honor or reputation. Licensor agrees that in those
        jurisdictions (e.g. Japan), in which any exercise of the right granted in Section 3(b) of this License (the
        right to make Adaptations) would be deemed to be a distortion, mutilation, modification or other derogatory
        action prejudicial to the Original Author's honor and reputation, the Licensor will waive or not assert, as
        appropriate, this Section, to the fullest extent permitted by the applicable national law, to enable You to
        reasonably exercise Your right under Section 3(b) of this License (right to make Adaptations) but not
        otherwise.

5. Representations, Warranties and Disclaimer

UNLESS OTHERWISE MUTUALLY AGREED TO BY THE PARTIES IN WRITING, LICENSOR OFFERS THE WORK AS-IS AND MAKES NO
    REPRESENTATIONS OR WARRANTIES OF ANY KIND CONCERNING THE WORK, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING,
    WITHOUT LIMITATION, WARRANTIES OF TITLE, MERCHANTIBILITY, FITNESS FOR A PARTICULAR PURPOSE, NONINFRINGEMENT, OR THE
    ABSENCE OF LATENT OR OTHER DEFECTS, ACCURACY, OR THE PRESENCE OF ABSENCE OF ERRORS, WHETHER OR NOT DISCOVERABLE.
    SOME JURISDICTIONS DO NOT ALLOW THE EXCLUSION OF IMPLIED WARRANTIES, SO SUCH EXCLUSION MAY NOT APPLY TO YOU.

6. Limitation on Liability. EXCEPT TO THE EXTENT REQUIRED BY APPLICABLE LAW, IN NO EVENT WILL
    LICENSOR BE LIABLE TO YOU ON ANY LEGAL THEORY FOR ANY SPECIAL, INCIDENTAL, CONSEQUENTIAL, PUNITIVE OR EXEMPLARY
    DAMAGES ARISING OUT OF THIS LICENSE OR THE USE OF THE WORK, EVEN IF LICENSOR HAS BEEN ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGES.

7. Termination

This License and the rights granted hereunder will terminate automatically upon any breach by You of the
        terms of this License. Individuals or entities who have received Adaptations or Collections from You under this
        License, however, will not have their licenses terminated provided such individuals or entities remain in full
        compliance with those licenses. Sections 1, 2, 5, 6, 7, and 8 will survive any termination of this
        License.

Subject to the above terms and conditions, the license granted here is perpetual (for the duration of the
        applicable copyright in the Work). Notwithstanding the above, Licensor reserves the right to release the Work
        under different license terms or to stop distributing the Work at any time; provided, however that any such
        election will not serve to withdraw this License (or any other license that has been, or is required to be,
        granted under the terms of this License), and this License will continue in full force and effect unless
        terminated as stated above.

8. Miscellaneous

Each time You Distribute or Publicly Perform the Work or a Collection, the Licensor offers to the recipient
        a license to the Work on the same terms and conditions as the license granted to You under this License.

Each time You Distribute or Publicly Perform an Adaptation, Licensor offers to the recipient a license to
        the original Work on the same terms and conditions as the license granted to You under this License.

If any provision of this License is invalid or unenforceable under applicable law, it shall not affect the
        validity or enforceability of the remainder of the terms of this License, and without further action by the
        parties to this agreement, such provision shall be reformed to the minimum extent necessary to make such
        provision valid and enforceable.

No term or provision of this License shall be deemed waived and no breach consented to unless such waiver
        or consent shall be in writing and signed by the party to be charged with such waiver or consent.

This License constitutes the entire agreement between the parties with respect to the Work licensed here.
        There are no understandings, agreements or representations with respect to the Work not specified here.
        Licensor shall not be bound by any additional provisions that may appear in any communication from You. This
        License may not be modified without the mutual written agreement of the Licensor and You.

The rights granted under, and the subject matter referenced, in this License were drafted utilizing the
        terminology of the Berne Convention for the Protection of Literary and Artistic Works (as amended on September
        28, 1979), the Rome Convention of 1961, the WIPO Copyright Treaty of 1996, the WIPO Performances and Phonograms
        Treaty of 1996 and the Universal Copyright Convention (as revised on July 24, 1971). These rights and subject
        matter take effect in the relevant jurisdiction in which the License terms are sought to be enforced according
        to the corresponding provisions of the implementation of those treaty provisions in the applicable national
        law. If the standard suite of rights granted under applicable copyright law includes additional rights not
        granted under this License, such additional rights are deemed to be included in the License; this License is
        not intended to restrict the license of any rights under applicable law.

### Creative Commons Notice

Creative Commons is not a party to this License, and makes no warranty whatsoever in connection with the
        Work. Creative Commons will not be liable to You or any party on any legal theory for any damages whatsoever,
        including without limitation any general, special, incidental or consequential damages arising in connection to
        this license. Notwithstanding the foregoing two (2) sentences, if Creative Commons has expressly identified
        itself as the Licensor hereunder, it shall have all rights and obligations of Licensor.

Except for the limited purpose of indicating to the public that the Work is licensed under the CCPL,
        Creative Commons does not authorize the use by either party of the trademark "Creative Commons" or any related
        trademark or logo of Creative Commons without the prior written consent of Creative Commons. Any permitted use
        will be in compliance with Creative Commons' then-current trademark usage guidelines, as may be published on
        its website or otherwise made available upon request from time to time. For the avoidance of doubt, this
        trademark restriction does not form part of the License.

Creative Commons may be contacted at https://creativecommons.org/.

# Creative Commons Attribution-ShareAlike 4.0 International

Creative Commons Corporation (“Creative Commons”) is not a law firm and does not provide legal services or
            legal advice. Distribution of Creative Commons public licenses does not create a lawyer-client or other
            relationship. Creative Commons makes its licenses and related information available on an “as-is” basis.
            Creative Commons gives no warranties regarding its licenses, any material licensed under their terms and
            conditions, or any related information. Creative Commons disclaims all liability for damages resulting from
            their use to the fullest extent possible.

Using Creative Commons Public Licenses

Creative Commons public licenses provide a standard set of terms and conditions that creators and other rights
        holders may use to share original works of authorship and other material subject to copyright and certain other
        rights specified in the public license below. The following considerations are for informational purposes only,
        are not exhaustive, and do not form part of our licenses.

Considerations for licensors: Our public licenses are intended for
        use by those authorized to give the public permission to use material in ways otherwise restricted by copyright
        and certain other rights. Our licenses are irrevocable. Licensors should read and understand the terms and
        conditions of the license they choose before applying it. Licensors should also secure all rights necessary
        before applying our licenses so that the public can reuse the material as expected. Licensors should clearly
        mark any material not subject to the license. This includes other CC-licensed material, or material used under
        an exception or limitation to copyright. More
            considerations for licensors.

Considerations for the public: By using one of our public
        licenses, a licensor grants the public permission to use the licensed material under specified terms and
        conditions. If the licensor’s permission is not necessary for any reason–for example, because of any applicable
        exception or limitation to copyright–then that use is not regulated by the license. Our licenses grant only
        permissions under copyright and certain other rights that a licensor has authority to grant. Use of the
        licensed material may still be restricted for other reasons, including because others have copyright or other
        rights in the material. A licensor may make special requests, such as asking that all changes be marked or
        described. Although not required by our licenses, you are encouraged to respect those requests where
        reasonable. More
            considerations for the public.

### Creative Commons Attribution-ShareAlike 4.0 International Public License

By exercising the Licensed Rights (defined below), You accept and agree to be bound by the terms and conditions
        of this Creative Commons Attribution-ShareAlike 4.0 International Public License ("Public License"). To the
        extent this Public License may be interpreted as a contract, You are granted the Licensed Rights in
        consideration of Your acceptance of these terms and conditions, and the Licensor grants You such rights in
        consideration of benefits the Licensor receives from making the Licensed Material available under these terms
        and conditions.

Section 1 – Definitions.

Adapted Material means material subject to Copyright and Similar Rights that is
            derived from or based upon the Licensed Material and in which the Licensed Material is translated, altered,
            arranged, transformed, or otherwise modified in a manner requiring permission under the Copyright and
            Similar Rights held by the Licensor. For purposes of this Public License, where the Licensed Material is a
            musical work, performance, or sound recording, Adapted Material is always produced where the Licensed
            Material is synched in timed relation with a moving image.

Adapter's License means the license You apply to Your Copyright and Similar
            Rights in Your contributions to Adapted Material in accordance with the terms and conditions of this Public
            License.

BY-SA Compatible License means a license listed at 
                creativecommons.org/compatiblelicenses, approved by Creative Commons as essentially the equivalent
            of this Public License.

Copyright and Similar Rights means copyright and/or similar rights closely
            related to copyright including, without limitation, performance, broadcast, sound recording, and Sui
            Generis Database Rights, without regard to how the rights are labeled or categorized. For purposes of this
            Public License, the rights specified in Section 2(b)(1)-(2) are not Copyright and
            Similar Rights.

Effective Technological Measures means those measures that, in the absence of
            proper authority, may not be circumvented under laws fulfilling obligations under Article 11 of the WIPO
            Copyright Treaty adopted on December 20, 1996, and/or similar international agreements.

Exceptions and Limitations means fair use, fair dealing, and/or any other
            exception or limitation to Copyright and Similar Rights that applies to Your use of the Licensed Material.

License Elements means the license attributes listed in the name of a Creative
            Commons Public License. The License Elements of this Public License are Attribution and ShareAlike.

Licensed Material means the artistic or literary work, database, or other
            material to which the Licensor applied this Public License.

Licensed Rights means the rights granted to You subject to the terms and
            conditions of this Public License, which are limited to all Copyright and Similar Rights that apply to Your
            use of the Licensed Material and that the Licensor has authority to license.

Licensor means the individual(s) or entity(ies) granting rights under this Public
            License.

Share means to provide material to the public by any means or process that
            requires permission under the Licensed Rights, such as reproduction, public display, public performance,
            distribution, dissemination, communication, or importation, and to make material available to the public
            including in ways that members of the public may access the material from a place and at a time
            individually chosen by them.

Sui Generis Database Rights means rights other than copyright resulting from
            Directive 96/9/EC of the European Parliament and of the Council of 11 March 1996 on the legal protection of
            databases, as amended and/or succeeded, as well as other essentially equivalent rights anywhere in the
            world.

You means the individual or entity exercising the Licensed Rights under this
            Public License. Your has a corresponding meaning.

Section 2 – Scope.

License grant.
            Subject to the terms and conditions of this Public License, the Licensor hereby grants
                    You a worldwide, royalty-free, non-sublicensable, non-exclusive, irrevocable license to exercise
                    the Licensed Rights in the Licensed Material to:
                    reproduce and Share the Licensed Material, in whole or in part; andproduce, reproduce, and Share Adapted Material.Exceptions and Limitations. For the
                    avoidance of doubt, where Exceptions and Limitations apply to Your use, this Public License does
                    not apply, and You do not need to comply with its terms and conditions.Term. The term of this Public License is
                    specified in Section 6(a).Media and formats; technical modifications
                        allowed. The Licensor authorizes You to exercise the Licensed Rights in all media and
                    formats whether now known or hereafter created, and to make technical modifications necessary to do
                    so. The Licensor waives and/or agrees not to assert any right or authority to forbid You from
                    making technical modifications necessary to exercise the Licensed Rights, including technical
                    modifications necessary to circumvent Effective Technological Measures. For purposes of this Public
                    License, simply making modifications authorized by this Section 2(a)(4) never
                    produces Adapted Material.Downstream recipients.
                    Offer from the Licensor – Licensed
                                Material. Every recipient of the Licensed Material automatically receives an
                            offer from the Licensor to exercise the Licensed Rights under the terms and conditions of
                            this Public License.Additional offer from the Licensor –
                                Adapted Material. Every recipient of Adapted Material from You automatically
                            receives an offer from the Licensor to exercise the Licensed Rights in the Adapted Material
                            under the conditions of the Adapter’s License You apply.No downstream restrictions. You
                            may not offer or impose any additional or different terms or conditions on, or apply any
                            Effective Technological Measures to, the Licensed Material if doing so restricts exercise
                            of the Licensed Rights by any recipient of the Licensed Material.No endorsement. Nothing in this Public
                    License constitutes or may be construed as permission to assert or imply that You are, or that Your
                    use of the Licensed Material is, connected with, or sponsored, endorsed, or granted official status
                    by, the Licensor or others designated to receive attribution as provided in Section 3(a)(1)(A)(i).

Subject to the terms and conditions of this Public License, the Licensor hereby grants
                    You a worldwide, royalty-free, non-sublicensable, non-exclusive, irrevocable license to exercise
                    the Licensed Rights in the Licensed Material to:
                    reproduce and Share the Licensed Material, in whole or in part; andproduce, reproduce, and Share Adapted Material.

reproduce and Share the Licensed Material, in whole or in part; and

produce, reproduce, and Share Adapted Material.

Exceptions and Limitations. For the
                    avoidance of doubt, where Exceptions and Limitations apply to Your use, this Public License does
                    not apply, and You do not need to comply with its terms and conditions.

Term. The term of this Public License is
                    specified in Section 6(a).

Media and formats; technical modifications
                        allowed. The Licensor authorizes You to exercise the Licensed Rights in all media and
                    formats whether now known or hereafter created, and to make technical modifications necessary to do
                    so. The Licensor waives and/or agrees not to assert any right or authority to forbid You from
                    making technical modifications necessary to exercise the Licensed Rights, including technical
                    modifications necessary to circumvent Effective Technological Measures. For purposes of this Public
                    License, simply making modifications authorized by this Section 2(a)(4) never
                    produces Adapted Material.

Downstream recipients.
                    Offer from the Licensor – Licensed
                                Material. Every recipient of the Licensed Material automatically receives an
                            offer from the Licensor to exercise the Licensed Rights under the terms and conditions of
                            this Public License.Additional offer from the Licensor –
                                Adapted Material. Every recipient of Adapted Material from You automatically
                            receives an offer from the Licensor to exercise the Licensed Rights in the Adapted Material
                            under the conditions of the Adapter’s License You apply.No downstream restrictions. You
                            may not offer or impose any additional or different terms or conditions on, or apply any
                            Effective Technological Measures to, the Licensed Material if doing so restricts exercise
                            of the Licensed Rights by any recipient of the Licensed Material.

Offer from the Licensor – Licensed
                                Material. Every recipient of the Licensed Material automatically receives an
                            offer from the Licensor to exercise the Licensed Rights under the terms and conditions of
                            this Public License.

Additional offer from the Licensor –
                                Adapted Material. Every recipient of Adapted Material from You automatically
                            receives an offer from the Licensor to exercise the Licensed Rights in the Adapted Material
                            under the conditions of the Adapter’s License You apply.

No downstream restrictions. You
                            may not offer or impose any additional or different terms or conditions on, or apply any
                            Effective Technological Measures to, the Licensed Material if doing so restricts exercise
                            of the Licensed Rights by any recipient of the Licensed Material.

No endorsement. Nothing in this Public
                    License constitutes or may be construed as permission to assert or imply that You are, or that Your
                    use of the Licensed Material is, connected with, or sponsored, endorsed, or granted official status
                    by, the Licensor or others designated to receive attribution as provided in Section 3(a)(1)(A)(i).

Other rights.Moral rights, such as the right of integrity, are not licensed under this Public License,
                    nor are publicity, privacy, and/or other similar personality rights; however, to the extent
                    possible, the Licensor waives and/or agrees not to assert any such rights held by the Licensor to
                    the limited extent necessary to allow You to exercise the Licensed Rights, but not otherwise.Patent and trademark rights are not licensed under this Public License.To the extent possible, the Licensor waives any right to collect royalties from You for
                    the exercise of the Licensed Rights, whether directly or through a collecting society under any
                    voluntary or waivable statutory or compulsory licensing scheme. In all other cases the Licensor
                    expressly reserves any right to collect such royalties.

Other rights.

Moral rights, such as the right of integrity, are not licensed under this Public License,
                    nor are publicity, privacy, and/or other similar personality rights; however, to the extent
                    possible, the Licensor waives and/or agrees not to assert any such rights held by the Licensor to
                    the limited extent necessary to allow You to exercise the Licensed Rights, but not otherwise.

Patent and trademark rights are not licensed under this Public License.

To the extent possible, the Licensor waives any right to collect royalties from You for
                    the exercise of the Licensed Rights, whether directly or through a collecting society under any
                    voluntary or waivable statutory or compulsory licensing scheme. In all other cases the Licensor
                    expressly reserves any right to collect such royalties.

Section 3 – License Conditions.

Your exercise of the Licensed Rights is expressly made subject to the following conditions.

Attribution.If You Share the Licensed Material (including in modified form), You must:retain the following if it is supplied by the Licensor with the Licensed
                            Material:
                            identification of the creator(s) of the Licensed Material and any
                                    others designated to receive attribution, in any reasonable manner requested by the
                                    Licensor (including by pseudonym if designated);a copyright notice;a notice that refers to this Public License; a notice that refers to the disclaimer of warranties;a URI or hyperlink to the Licensed Material to the extent reasonably
                                    practicable;indicate if You modified the Licensed Material and retain an indication of any
                            previous modifications; andindicate the Licensed Material is licensed under this Public License, and
                            include the text of, or the URI or hyperlink to, this Public License.You may satisfy the conditions in Section 3(a)(1) in any reasonable
                    manner based on the medium, means, and context in which You Share the Licensed Material. For
                    example, it may be reasonable to satisfy the conditions by providing a URI or hyperlink to a
                    resource that includes the required information.If requested by the Licensor, You must remove any of the information required by Section
                    3(a)(1)(A) to the extent reasonably practicable.

Attribution.

If You Share the Licensed Material (including in modified form), You must:retain the following if it is supplied by the Licensor with the Licensed
                            Material:
                            identification of the creator(s) of the Licensed Material and any
                                    others designated to receive attribution, in any reasonable manner requested by the
                                    Licensor (including by pseudonym if designated);a copyright notice;a notice that refers to this Public License; a notice that refers to the disclaimer of warranties;a URI or hyperlink to the Licensed Material to the extent reasonably
                                    practicable;indicate if You modified the Licensed Material and retain an indication of any
                            previous modifications; andindicate the Licensed Material is licensed under this Public License, and
                            include the text of, or the URI or hyperlink to, this Public License.

If You Share the Licensed Material (including in modified form), You must:

retain the following if it is supplied by the Licensor with the Licensed
                            Material:
                            identification of the creator(s) of the Licensed Material and any
                                    others designated to receive attribution, in any reasonable manner requested by the
                                    Licensor (including by pseudonym if designated);a copyright notice;a notice that refers to this Public License; a notice that refers to the disclaimer of warranties;a URI or hyperlink to the Licensed Material to the extent reasonably
                                    practicable;

identification of the creator(s) of the Licensed Material and any
                                    others designated to receive attribution, in any reasonable manner requested by the
                                    Licensor (including by pseudonym if designated);

a copyright notice;

a notice that refers to this Public License; 

a notice that refers to the disclaimer of warranties;

a URI or hyperlink to the Licensed Material to the extent reasonably
                                    practicable;

indicate if You modified the Licensed Material and retain an indication of any
                            previous modifications; and

indicate the Licensed Material is licensed under this Public License, and
                            include the text of, or the URI or hyperlink to, this Public License.

You may satisfy the conditions in Section 3(a)(1) in any reasonable
                    manner based on the medium, means, and context in which You Share the Licensed Material. For
                    example, it may be reasonable to satisfy the conditions by providing a URI or hyperlink to a
                    resource that includes the required information.

If requested by the Licensor, You must remove any of the information required by Section
                    3(a)(1)(A) to the extent reasonably practicable.

ShareAlike. In addition to the conditions in Section 3(a),
                if You Share Adapted Material You produce, the following conditions also apply.The Adapter’s License You apply must be a Creative Commons license with the same License
                    Elements, this version or later, or a BY-SA Compatible License.You must include the text of, or the URI or hyperlink to, the Adapter's License You
                    apply. You may satisfy this condition in any reasonable manner based on the medium, means, and
                    context in which You Share Adapted Material.You may not offer or impose any additional or different terms or conditions on, or apply
                    any Effective Technological Measures to, Adapted Material that restrict exercise of the rights
                    granted under the Adapter's License You apply.

In addition to the conditions in Section 3(a),
                if You Share Adapted Material You produce, the following conditions also apply.

The Adapter’s License You apply must be a Creative Commons license with the same License
                    Elements, this version or later, or a BY-SA Compatible License.

You must include the text of, or the URI or hyperlink to, the Adapter's License You
                    apply. You may satisfy this condition in any reasonable manner based on the medium, means, and
                    context in which You Share Adapted Material.

You may not offer or impose any additional or different terms or conditions on, or apply
                    any Effective Technological Measures to, Adapted Material that restrict exercise of the rights
                    granted under the Adapter's License You apply.

Section 4 – Sui Generis Database Rights.

Where the Licensed Rights include Sui Generis Database Rights that apply to Your use of the Licensed Material:

for the avoidance of doubt, Section 2(a)(1) grants You the right to extract,
            reuse, reproduce, and Share all or a substantial portion of the contents of the database;

if You include all or a substantial portion of the database contents in a database in which You
            have Sui Generis Database Rights, then the database in which You have Sui Generis Database Rights (but not
            its individual contents) is Adapted Material, including for purposes of Section 3(b);
            and

You must comply with the conditions in Section 3(a) if You Share all or a
            substantial portion of the contents of the database.

Section
            5 – Disclaimer of Warranties and Limitation of Liability.

Unless otherwise separately undertaken by the Licensor, to the extent possible, the
                Licensor offers the Licensed Material as-is and as-available, and makes no representations or
                warranties of any kind concerning the Licensed Material, whether express, implied, statutory, or other.
                This includes, without limitation, warranties of title, merchantability, fitness for a particular
                purpose, non-infringement, absence of latent or other defects, accuracy, or the presence or absence of
                errors, whether or not known or discoverable. Where disclaimers of warranties are not allowed in full
                or in part, this disclaimer may not apply to You.

To the extent possible, in no event will the Licensor be liable to You on any legal theory
                (including, without limitation, negligence) or otherwise for any direct, special, indirect, incidental,
                consequential, punitive, exemplary, or other losses, costs, expenses, or damages arising out of this
                Public License or use of the Licensed Material, even if the Licensor has been advised of the
                possibility of such losses, costs, expenses, or damages. Where a limitation of liability is not allowed
                in full or in part, this limitation may not apply to You.

The disclaimer of warranties and limitation of liability provided above shall be interpreted in a
            manner that, to the extent possible, most closely approximates an absolute disclaimer and waiver of all
            liability.

Section 6 – Term and Termination.

This Public License applies for the term of the Copyright and Similar Rights licensed here.
            However, if You fail to comply with this Public License, then Your rights under this Public License
            terminate automatically.

Where Your right to use the Licensed Material has terminated under Section 6(a), it
                reinstates:automatically as of the date the violation is cured, provided it is cured within 30 days
                    of Your discovery of the violation; orupon express reinstatement by the Licensor. For the avoidance of doubt, this Section 6(b) does not affect any right the
            Licensor may have to seek remedies for Your violations of this Public License.
        

Where Your right to use the Licensed Material has terminated under Section 6(a), it
                reinstates:

automatically as of the date the violation is cured, provided it is cured within 30 days
                    of Your discovery of the violation; or

upon express reinstatement by the Licensor.

For the avoidance of doubt, the Licensor may also offer the Licensed Material under separate terms
            or conditions or stop distributing the Licensed Material at any time; however, doing so will not terminate
            this Public License.

Sections 1, 5, 6, 7, and
            8 survive termination of this Public License.

Section 7 – Other Terms and Conditions.

The Licensor shall not be bound by any additional or different terms or conditions communicated by
            You unless expressly agreed.

Any arrangements, understandings, or agreements regarding the Licensed Material not stated herein
            are separate from and independent of the terms and conditions of this Public License.

Section 8 – Interpretation.

For the avoidance of doubt, this Public License does not, and shall not be interpreted to, reduce,
            limit, restrict, or impose conditions on any use of the Licensed Material that could lawfully be made
            without permission under this Public License.

To the extent possible, if any provision of this Public License is deemed unenforceable, it shall
            be automatically reformed to the minimum extent necessary to make it enforceable. If the provision cannot
            be reformed, it shall be severed from this Public License without affecting the enforceability of the
            remaining terms and conditions.

No term or condition of this Public License will be waived and no failure to comply consented to
            unless expressly agreed to by the Licensor.

Nothing in this Public License constitutes or may be interpreted as a limitation upon, or waiver
            of, any privileges and immunities that apply to the Licensor or You, including from the legal processes of
            any jurisdiction or authority.

Creative Commons is not a party to its public licenses. Notwithstanding, Creative Commons may
        elect to apply one of its public licenses to material it publishes and in those instances will be considered
        the “Licensor.” The text of the Creative Commons public licenses is dedicated to the public domain under the CC0 Public Domain Dedication. Except
        for the limited purpose of indicating that material is shared under a Creative Commons public license or as
        otherwise permitted by the Creative Commons policies published at creativecommons.org/policies,
        Creative Commons does not authorize the use of the trademark “Creative Commons” or any other trademark or logo
        of Creative Commons without its prior written consent including, without limitation, in connection with any
        unauthorized modifications to any of its public licenses or any other arrangements, understandings, or
        agreements concerning use of licensed material. For the avoidance of doubt, this paragraph does not form part
        of the public licenses.Creative Commons may be contacted at creativecommons.org.

