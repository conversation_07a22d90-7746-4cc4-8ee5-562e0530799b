package com.linkus.sysuser.ctrl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.sys.model.*;
import com.linkus.sys.model.SysDefQuerySortKey.Key;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.model.vo.SysDefDbcPortalVo;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.sysuser.util.ObjectConversionFactory;
import com.linkus.sysuser.vo.TeSysDefVo;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/sysDefCtrl")
public class SysDefCtrl extends BaseCtrl {

	@Resource
	private ISysDefService sysDefService;

	@Autowired
	private ISysUserService sysUserService;

	@Resource
	private ISysDefRoleUserService sysDefRoleUserService;

	/**
	 * 查询所有任务标识
	 */
	@RequestMapping("/queryTaskTag.action")
	@ResponseBody
	public void queryTaskTag() {
		List<SysDef> sysDefs = sysDefService.getSysDefsBySrc(new ObjectId(SysDefConstants.TASKTAG_DEF_ID),
				SysDefTypeCodeName.OPTION);
		List<TeSysDefVo> teSysDefVos = ObjectConversionFactory.sysDefListConversionToTeSysDefVoList(sysDefs);
		this.returnResult(teSysDefVos);
	}

	/**
	 * 查询所有表格字段 defType.defTypeCodeName 为 field srcDef.srcDefCodeName 为
	 * prjPlanTaskTb
	 */
	@RequestMapping("/queryAllPlanTaskTb.action")
	@ResponseBody
	public void queryPlanTaskTb() {
		List<SysDefQuerySortKey> querySortKeys = new ArrayList<>();
		SysDefQuerySortKey sysDefQuerySortKey = new SysDefQuerySortKey(Key.FIELD_NO, true);
		querySortKeys.add(sysDefQuerySortKey);
		List<SysDef> sysDefs = sysDefService.getSysDefsBySrc(new ObjectId(SysDefConstants.PRJPLANTASKTB_DEF_ID),
				SysDefTypeCodeName.FIELD, querySortKeys);
		List<TeSysDefVo> teSysDefVos = ObjectConversionFactory.sysDefListConversionToTeSysDefVoList(sysDefs);
		this.returnResult(teSysDefVos);
	}

	/**
	 * 查询所有Bu
	 */
	@RequestMapping("/queryAiBu.action")
	@ResponseBody
	public void queryAiBu() {
		List<SysDef> sysDefs = sysDefService.getSysDefsByDefType(SysDefTypeCodeName.AI_BU);
		this.returnResult(sysDefs);
	}

	/**
	 * 查询属于我的Bu
	 */
	@RequestMapping("/queryMyBu.action")
	@ResponseBody
	public void queryMyBu() {
		TeSysUser loginUser = getTeSysUser();
		if (loginUser == null){
			throw new BaseException("当前登陆人为空");
		}
		List<SysDef> sysDefs = sysDefRoleUserService.getBuByUserId(loginUser.getId());
		this.returnResult(sysDefs);
	}

	/**
	 * 根据Bu查询Cc
	 */
	@RequestMapping("/queryAiCcByAiBu.action")
	@ResponseBody
	public void queryAiCcByAiBu(@RequestBody SysDef sysDef) {
		sysDef = sysDefService.getSysDefByCodeName(sysDef.getCodeName(), SysDefTypeCodeName.AI_BU);
		List<SysDef> sysDefs = sysDefService.getSysDefsBySrc(sysDef.getId(), SysDefTypeCodeName.AI_CC);
		this.returnResult(sysDefs);
	}

	/**
	 * 根据根据定义类型查询定义
	 */
	@RequestMapping("/queryByDefTypeCodeName.action")
	@ResponseBody
	public void queryByDefTypeCodeName(HttpServletRequest request) {
		String defTypeName = request.getParameter("defTypeName");
		String sortField = request.getParameter("sortField");
		String sortAsc = request.getParameter("sortAsc");
		if (StringUtil.isNotNull(defTypeName)) {
			SysDefTypeCodeName sysDefTypeCodeName = SysDefTypeCodeName.valueOf(defTypeName);
			if (StringUtil.isNotNull(sortField) && StringUtil.isNotNull(sortAsc)) {
				Key key = null;
				Boolean asc = Boolean.TRUE;
				try {
					key = Key.valueOf(sortField);
					asc = StringUtil.toBoolean(sortAsc);
				} catch (Exception e) {
					key = null;
					asc = Boolean.TRUE;
				}

				if (key != null) {
					List<SysDefQuerySortKey> keys = new ArrayList<>();
					keys.add(new SysDefQuerySortKey(key, asc.booleanValue()));
					returnResult(sysDefService.getSysDefsByDefType(sysDefTypeCodeName, keys));
					return;
				}
			}
			List<SysDef> sysDefList = sysDefService.getSysDefsByDefType(sysDefTypeCodeName);
			returnResult(sysDefList);
		}
	}
	
	@RequestMapping("/removeSysDef.action")
	@ResponseBody
	public void removeSysDef(@RequestBody SysDef sysDef) {
	    sysDefService.removeSysDef(sysDef.getId());
	    this.returnResult("success");
	}
	
	@RequestMapping("/updateSysDef.action")
	@ResponseBody
	public void updateSysDef(@RequestBody SysDef sysDef) {
		Boolean isNotExist=true;
		String message="success";
		SysDefTypeCodeName sysDefTypeCodeName=SysDefTypeCodeName.valueOf(sysDef.getDefType().getCodeName()) ;
		List<SysDef> sysDefList = sysDefService.getSysDefsByDefTypeAndName(sysDefTypeCodeName,sysDef.getDefName());
		if( null != sysDefList && sysDefList.size() > 0){
			for(SysDef sysdef : sysDefList){
				if( !sysdef.getId().equals(sysDef.getId()) && sysdef.getDefName().equals(sysDef.getDefName()) ){
					isNotExist=false;
					message="defNameExist";
					break;
				}
			}
		}
		if(!"defNameExist".equals(message)){
			SysDef sysDefByCodeName = sysDefService.getSysDefByCodeName(sysDef.getCodeName(),sysDefTypeCodeName); 
			if( null != sysDefByCodeName && !sysDefByCodeName.getId().equals(sysDef.getId()) ){
				isNotExist=false;
				message="codeNameExist";
			}
		}
		if(isNotExist){
			TeSysDef updateSysDef = sysDefService.getTeSysDefById(sysDef.getId());
			updateSysDef.setDefName(sysDef.getDefName());
			updateSysDef.setCodeName(sysDef.getCodeName());
			sysDefService.updateSysDef(updateSysDef);
			
		}
		this.returnResult(message);
	}

	@RequestMapping("/updateValue.action")
	@ResponseBody
	public void updateSysDefValue(
			@RequestParam(value = "id", required = true) ObjectId id,
			@RequestParam(value = "value", required = true) String value,
			@RequestParam(value = "defName", required = false) String defName
	) {
		TeSysDef def = sysDefService.getTeSysDefById(id);
		if( null == def ){
			throw new BaseException("定义的ID不存在");
		}
		if (StringUtil.isNotNull(defName)){
			def.setDefName(defName);
		}
		def.setValue(value);
		sysDefService.updateSysDef(def);
	}
	
	@RequestMapping("/createSysDef.action")
	@ResponseBody
	public void createSysDef(@RequestBody SysDef sysDef) {
		Boolean isNotExist=true;
		String message="success";
		SysDefTypeCodeName sysDefTypeCodeName=SysDefTypeCodeName.valueOf(sysDef.getDefType().getCodeName()) ;
		List<SysDef> sysDefList = sysDefService.getSysDefsByDefTypeAndName(sysDefTypeCodeName,sysDef.getDefName());
		if( null != sysDefList && sysDefList.size() > 0){
					isNotExist=false;	
					message="defNameExist";
		}else{
			SysDef sysDefByCodeName = sysDefService.getSysDefByCodeName(sysDef.getCodeName(),sysDefTypeCodeName); 
			if( null != sysDefByCodeName ){
				isNotExist=false;
				message="codeNameExist";
			}
		}
		if(isNotExist){
			TeSysUser loginUser = geTeSysUser();
			TeUser addUser = new TeUser(loginUser.getId(),loginUser.getLoginName(),loginUser.getUserName(),loginUser.getJobCode());
			sysDef.setAddUser(addUser);
			sysDef.setAddTime(new Date());
			sysDef.setIsStd(false);
			sysDef.setIsValid(true);
			sysDefService.createSysDef(sysDefTypeCodeName, sysDef);
		}
		this.returnResult(message);
		
	}
	
	/**
	 * 查询所有业务类型
	 */
	@RequestMapping("/queryPrjTrcType.action")
	@ResponseBody
	public void queryPrjTrcType() {
		List<SysDef> sysDefs = sysDefService.getSysDefsByDefType(SysDefTypeCodeName.BIZ_TYPE);
		List<TeSysDefVo> teSysDefVos = ObjectConversionFactory.sysDefListConversionToTeSysDefVoList(sysDefs);
		this.returnResult(teSysDefVos);
	}
	
	//获取当前用户登录信息
	private TeSysUser geTeSysUser(){
		String casLoginUserName = getCasLoginUser();
		TeSysUser loginUser = sysUserService
				.queryByLoginName(casLoginUserName);
		return loginUser;
	}
	
	@RequestMapping("/getSysDefsForRpt.action")
	@ResponseBody
	public void getSysDefsForRpt(){
		List<SysDefVo> sysDefs = sysDefService.getSysDefsForRpt();
		this.returnResult(sysDefs);
	}

	/**
	 * 查询省份信息
	 */
	@RequestMapping("/queryProvince.action")
	@ResponseBody
	public void queryProvince(String provCodeName) {
		ObjectId id= null;
		if (!isBu()) {
			TeSysUser teSysUser = geTeSysUser();
			id = teSysUser.getId();
		}
		List<SysDef> userList = sysDefService.queryProvince(id,provCodeName);
		// 按照addTime排序
		Collections.sort(userList, new Comparator<SysDef>() {
			@Override
			public int compare(SysDef o1, SysDef o2) {
				if(null == o1 && null == o2 ) {
					return 0;
				}
				if(null == o1 && null != o2) {
					return -1;
				}
				if(null != o1 && null == o2) {
					return 1;
				}
				Date o1addTime = o1.getAddTime();
				Date o2addTime = o2.getAddTime();
				if (o1addTime == null && o2addTime == null) {
					return 0;
				}
				if (o1addTime == null && o2addTime != null) {
					return -1;
				}
				if (o1addTime != null && o2addTime == null) {
					return 1;
				}
				return o1addTime.compareTo(o2addTime);
			}
		});
		this.returnResult(userList);
	}

	private boolean isBu() {
		TeSysUser loginUser = geTeSysUser();
		String loginName = loginUser.getLoginName();
		List<TeSysDefRoleUser> list = sysDefRoleUserService.queryBuRoleUser(null, "omResp", loginName);
		return list.size() > 0 ? true : false;
	}
	/**
	 * 查询AS
	 */
	@RequestMapping("/queryBigRegion.action")
	@ResponseBody
	public void queryBigRegion(String provIds) {
		List<ObjectId> provIdList = StringUtil.transIds2List(provIds, ",", ObjectId.class);
		List<SysDef> userList = sysDefService.queryBigRegion(provIdList);
		this.returnResult(userList);
	}

	/**
	 * 查询工程部
	 */
	@RequestMapping("/queryRegion.action")
	@ResponseBody
	public void queryRegion(String provIds) {
		List<ObjectId> provIdList = StringUtil.transIds2List(provIds, ",", ObjectId.class);
		List<SysDef> userList = sysDefService.queryRegion(provIdList);
		this.returnResult(userList);
	}
	
	@RequestMapping("/getRptTopics.action")
	@ResponseBody
	public void getRptTopics() {
		List<SysDefVo> sysDefs = sysDefService.getRptTopics();
		this.returnResult(sysDefs);
	}
	@RequestMapping("/getSysDefs.action")
	@ResponseBody
	public void getSysDefs(@RequestParam String defIds) {
		List<ObjectId> defIdList = StringUtil.transIds2List(defIds, ",", ObjectId.class);
		List<TeSysDef> teSysDefs = new ArrayList<>();
		if(null != defIdList && defIdList.size() >0 ){
		     teSysDefs = sysDefService.getTeSysDefsByIds(defIdList);
		}
		this.returnResult(teSysDefs);
	}
	
	@RequestMapping("/getSysDefsByDefTypeAndSrc.action")
	@ResponseBody
	public void getSysDefsByDefTypeAndSrc(String defTypeCodeName, String srcDefCodeName) {
		this.returnResult(sysDefService.getSysByDefTypeCodeNameAndSrcDefCodeName(defTypeCodeName, srcDefCodeName));
	}
	
	/**
	 * 查询所有任务状态
	 */
	@RequestMapping("/getTaskStatus.action")
	@ResponseBody
	public void getTaskStatus(@RequestParam String defId) {
		List<TeSysDef> teSysDefs = new ArrayList<>();
		if(null != defId ){
		     teSysDefs = sysDefService.getAllTeSysDefsByIds(StringUtil.toObjectId(defId));
		}
		this.returnResult(teSysDefs);
	}
	
	@RequestMapping("/getSysDefsByParentId.action")
	@ResponseBody
	public void getSysDefsByParentId(String parentId) {
		this.returnResult(sysDefService.getSysDefByParentDefId(StringUtil.toObjectId(parentId)));
	}
	
	@RequestMapping("/getSysDefBySrcAndCode.action")
	@ResponseBody
	public void getSysDefBySrcAndCode(String srcDefId, String codeName) {
		List<ObjectId> srcDefIdList = new ArrayList<>();
		List<SysDef> sysDefsBySrc = new ArrayList<>();
		if (srcDefId != null && codeName != null) {
			srcDefIdList.add(new ObjectId(srcDefId));
			sysDefsBySrc = sysDefService.getSysDefsBySrc(srcDefIdList, SysDefTypeCodeName.valueOf(codeName));
		}
		this.returnResult(sysDefsBySrc);
	}

	@RequestMapping("/querySysDefById.action")
	@ResponseBody
	public void querySysDefById(@RequestParam ObjectId id) {
		this.returnResult(sysDefService.getSysDefById(id));
	}

	@RequestMapping("/getSysDefsByIds.action")
	@ResponseBody
	public void getSysDefsByIds(@RequestParam(value="id[]") List<ObjectId> id) {
		this.returnResult(sysDefService.getSysDefsByIds(id));
	}

	@RequestMapping("/getSysDefByFuzzy.action")
	@ResponseBody
	public void getSysDefByFuzzy(@RequestParam String defName, @RequestParam String defTypeCodeName) {
		SysDefTypeCodeName sysDefTypeCodeName = SysDefTypeCodeName.valueOf(defTypeCodeName);
		this.returnResult(sysDefService.getSysDefByFuzzy(defName, sysDefTypeCodeName));
	}

	/**
	 * 获取定义树
	 * @param rootId 子树的根Id，它可能并不是整颗树的根，是子树的根
	 * @param defTypeCodeName 定义类型
	 * @param level 层级
	 */
	@RequestMapping("/getSysDefTreeByLevel.action")
	@ResponseBody
	public void getSysDefTreeByLevel(@RequestParam(value = "rootId", required = true) ObjectId rootId,
									 @RequestParam(value = "defTypeCodeName", required = true) String defTypeCodeName,
									 @RequestParam(value = "level", required = false) Integer level) {

		Set<ObjectId> authedNodeIdSet = new HashSet<>();
		authedNodeIdSet.add(rootId);
		SysDefTree sysDefTree = sysDefService.getSubTree(rootId, level, authedNodeIdSet, defTypeCodeName);
		returnResult(sysDefTree);
	}

	/**
	 * 移动改变父节点
	 *
	 * @param nodeId
	 * @param parentId
	 * @param targetNodeId
	 * @param action
	 */
	@RequestMapping("/moveDef.action")
	@ResponseBody
	public void moveKnowledgeBase(@RequestParam(value = "nodeId") ObjectId nodeId, @RequestParam(value = "parentId") ObjectId parentId,
								  @RequestParam(value = "targetNodeId") ObjectId targetNodeId, @RequestParam(value = "action") String action) {
		sysDefService.moveSysDef(nodeId, parentId, targetNodeId, action, null);
	}
	
	@RequestMapping("/getSysDefByCndtItemCodes.action")
	@ResponseBody
	public void getSysDefByCndtItemCodes(
			@RequestParam(value = "defTypeCodeName",required = true) String defTypeCodeName,
			@RequestParam(value = "srcDefCodeName",required = true) String srcDefCodeName,
			@RequestParam(value = "cndtItemCode[]",required = false) List<String> cndtItemCodes
	) {

		this.returnResult(sysDefService.getSysDefByCndtItemCodes(defTypeCodeName, srcDefCodeName, cndtItemCodes));
	}

	/*
	* 根据CndtItemCode查询数据，CndtItemCode为null或传递的参数
	*/
	@RequestMapping("/getSysDefByCndtItemCodeAndNull.action")
	@ResponseBody
	public void getSysDefByCndtItemCodeAndNull(
			@RequestParam(value = "defTypeCodeName",required = true) String defTypeCodeName,
			@RequestParam(value = "srcDefCodeName",required = true) String srcDefCodeName,
			@RequestParam(value = "cndtItemCode",required = false) String cndtItemCode
	) {
		this.returnResult(sysDefService.getSysDefByCndtItemCodeAndNull(defTypeCodeName, srcDefCodeName,cndtItemCode));
	}

	/*
	 * 校验bu是否使用了abp
	 */
	@RequestMapping("/checkBuHasUseAbp.action")
	@ResponseBody
	public void checkBuHasUseAbp(@RequestParam(value = "sbuId",required = true)String sbuId) {
		this.returnResult(sysDefService.getSysDefByCodeName(sbuId, SysDefTypeCodeName.AI_BU));
	}

    /**
     * DBC门户配置创建
     * @param sysDef
     */
    @RequestMapping("/createDbcPortal.action")
    @ResponseBody
    public CommonResult<Void> createDbcPortal(@RequestBody SysDefDbcPortalVo sysDef) {
        TeSysUser currentUser = getTeSysUser();
        sysDefService.createDbcPortal(sysDef, currentUser.trans2User());
        return CommonResult.success();
    }

    /**
     * DBC门户配置更新
     * @param sysDef
     */
    @RequestMapping("/updateDbcPortal.action")
    @ResponseBody
    public CommonResult<Void> updateDbcPortal(@RequestBody SysDefDbcPortalVo sysDef) {
        sysDefService.updateDbcPortal(sysDef);
        return CommonResult.success();
    }

    /**
     * DBC门户配置更新
     * @param sysDef
     */
    @RequestMapping("/deleteDbcPortal.action")
    @ResponseBody
    public CommonResult<Void> deleteDbcPortal(@RequestBody SysDefDbcPortalVo sysDef) {
        sysDefService.deleteDbcPortal(sysDef);
        return CommonResult.success();
    }

    /**
     * DBC门户配置更新
     * @param srcDefId
     */
    @RequestMapping("/queryDbcPortal.action")
    @ResponseBody
    public CommonResult<List<SysDefDbcPortalVo>> queryDbcPortal(@RequestParam(value = "srcDefId") ObjectId srcDefId) {
        return CommonResult.success(sysDefService.queryDbcPortal(srcDefId));
    }

	/**
	 * 查询子系统菜单
	 * @param srcDefId
	 */
	@RequestMapping("/querySubSysMenu.action")
	@ResponseBody
	public CommonResult<List<SysDefTree>> querySubSysMenu(@RequestParam ObjectId srcDefId) {
		TeSysUser loginUser = getTeSysUser();
		String sbuId = loginUser.getSbuId();
		return CommonResult.success(sysDefService.querySubSysMenu(srcDefId,sbuId));
	}

	/**
	 * 获取当前用户登录信息
	 */
	private TeSysUser getTeSysUser(){
		String casLoginUserName = getCasLoginUser();
		TeSysUser loginUser = sysUserService.queryByLoginName(casLoginUserName);
		return loginUser;
	}

	@RequestMapping("/queryPmsValueItem.action")
	@ResponseBody
	public CommonResult<List<PmsValueItemVo>> queryPmsValueItem() {
		return CommonResult.success(sysDefService.queryPmsValueItem());
	}

	@RequestMapping("/getStatusByAuthBu.action")
	@ResponseBody
	public void getStatusByAuthBu(
			@RequestParam(value = "defTypeCodeName",required = true) String defTypeCodeName,
			@RequestParam(value = "srcDefCodeName",required = true) String srcDefCodeName,
			@RequestParam(value = "cndtItemCode[]",required = false) List<String> cndtItemCodes
	) {
		TeSysUser loginUser = getTeSysUser();
		if (loginUser == null){
			throw BusinessException.initExc("当前登陆人为空");
		}
		if (CollectionUtils.isEmpty(cndtItemCodes)){
			cndtItemCodes = new ArrayList<>();
			if (StringUtil.isNotNull(loginUser.getSbuId())){
				cndtItemCodes.add(loginUser.getSbuId());
			}
			//查询bu运营管理员
			List<TeSysDefRoleUser> roleUserDefs = sysDefRoleUserService.queryRoleUserList(Arrays.asList(loginUser.getId()),
					Arrays.asList(StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID)), null, SysDefTypeCodeName.AI_BU);
			if (CollectionUtils.isNotEmpty(roleUserDefs)){
				//bu定义id
				List<ObjectId> sbuDefIds = roleUserDefs.stream().filter(def -> def.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(sbuDefIds)) {
					//查询bu
					List<SysDef> sbuDefs = sysDefService.getSysDefsByIds(sbuDefIds);
					if (CollectionUtils.isNotEmpty(sbuDefs)){
						List<String> buCodes = sbuDefs.stream().filter(def -> StringUtil.isNotNull(def.getCodeName())).map(SysDef::getCodeName).collect(Collectors.toList());
						if (CollectionUtils.isNotEmpty(buCodes)){
							cndtItemCodes.addAll(buCodes);
						}
					}
				}
			}
			if (CollectionUtils.isEmpty(cndtItemCodes)){
				throw BusinessException.initExc("当前登陆人BU为空");
			}
		}
		this.returnResult(sysDefService.getSysDefByCndtItemCodes(defTypeCodeName, srcDefCodeName, cndtItemCodes));
	}

	@RequestMapping("/listSysDefByCndtItem.action")
	@ResponseBody
	public CommonResult<Object> listSysDefByCndtItem(@RequestParam(value = "cndtItemIds[]",required = false) List<ObjectId> cndtItemIds){
		return CommonResult.success(sysDefService.listSysDefByCndtItem(SysDefTypeCodeName.PRJ_DVT_RECTIFY_STATUS_TYPE,cndtItemIds));
	}

	@RequestMapping("/getWeekOfYearByNow.action")
	@ResponseBody
	public CommonResult<String> getWeekOfYearByNow(){
		return CommonResult.success(DateUtil.getWeekOfYearByNow());
	}

	@RequestMapping("/getSysDefsByDefTypeAndName.action")
	@ResponseBody
	public CommonResult<Object> getSysDefsByDefTypeAndName(@RequestParam(value = "codeName",required = true) String codeName){
		SysDefTypeCodeName sysDefTypeCodeName = SysDefTypeCodeName.valueOf(codeName);
		return CommonResult.success(sysDefService.getSysDefsByDefTypeAndName(sysDefTypeCodeName));
	}
}
