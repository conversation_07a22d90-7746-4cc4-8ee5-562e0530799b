package com.linkus.sysuser.service;

import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.MongoDao;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.api.model.ItfSysDefRoleUser;
import com.linkus.sysuser.api.model.ItfSysDefRoleUserQuery;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2DefType;
import com.linkus.sysuser.model.TeSysDefUserInfo;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.vo.OrganizeVo;
import com.linkus.sysuser.vo.TeSysDefRoleUser2UserVo;
import com.linkus.sysuser.vo.TeSysDefRoleUserVo;
import com.linkus.sysuser.vo.TeSysUserVo;
import org.bson.types.ObjectId;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ISysDefRoleUserService extends MongoDao<TeSysDefRoleUser>{
	
	/**
	 * addTeSysDefRoleUserList 批量添加成员
	 * 
	 * @param teSysDefRoleUsers
	 * @return
	 */
	List<TeSysDefRoleUser> addTeSysDefRoleUserList(List<TeSysDefRoleUser> teSysDefRoleUsers);
	
	/**
	 * addTeSysDefRoleUser 为项目小组添加成员
	 * 
     * @param teSysDefRoleUser
     * @param sysDefUserInfo
	 * @return
	 */
	TeSysDefRoleUser addTeSysDefRoleUserForPrjGroup(TeSysDefRoleUser teSysDefRoleUser, TeSysDefUserInfo sysDefUserInfo);
    
    /**
     * addTeSysDefRoleUser 为项目小组创建并添加成员
     * 
     * @param teSysDefRoleUser
     * @return
     */
    TeSysDefRoleUser createAddUserForPrjGroup(TeSysDefRoleUser teSysDefRoleUser);
	
	/**
	 * addTeSysDefRoleUserForFolder 为文件夹添加管理员
	 * 
	 * @param teSysDefRoleUser
	 * @return
	 */
	TeSysDefRoleUser addTeSysDefRoleUserForFolder(TeSysDefRoleUser teSysDefRoleUser);
	
	TeSysDefRoleUser wealthHouseAddTeSysDefRoleUserForFolder(TeSysDefRoleUser teSysDefRoleUser);
	
	/**
	 * changeGroupRespForGroup 更换组长信息
	 * 
	 * @param teSysDefRoleUserVo
	 */
	void changeGroupRespForGroup(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	/**
	 * removeTeSysDefRoleUser 删除项目小组成员
	 * 
	 * @param teSysDefRoleUser
	 */
	void removeTeSysDefRoleUser(TeSysDefRoleUser teSysDefRoleUser);
	
	/**
	 * 删除目录管理员
	 * @param teSysDefRoleUser
	 * @param defIds
	 */
	void removeByDefIdsAndTeSysDefRoleUser(List<ObjectId> defIds,TeSysDefRoleUser teSysDefRoleUser);
	
	/**
	 * queryByUserAndDefIdAndDefType 查询roleUser,通过user、defId、defType
	 * 
	 * @param teSysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> queryByUserAndDefIdAndDefType(TeSysDefRoleUser teSysDefRoleUser);

	/**
	 * 根据 defId || defType.defTypeCodeName || role.roleCodeName || roleUser.userName 查询用户信息
	 * @param sysDefRoleUser
	 */
	public List<TeSysDefRoleUser2UserVo> querySysUserDistinctColl(TeSysDefRoleUser sysDefRoleUser);

	List<TeSysDefRoleUser2UserVo> querySysUserByParentCtlg(TeSysDefRoleUser sysDefRoleUser);

	/**
	 * 根据用户名模糊查询所有User对象
	 * 需要参数defId、roleUser.userName
	 * 可以给定参数roleId
	 * 
	 * @param teSysDefRoleUserVo
	 * @return
	 */
	public List<TeSysDefRoleUser2UserVo> queryUserNameFuzzyByDefId(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	public List<TeSysDefRoleUser2UserVo> queryPrjUserAndAdminByDefId(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	
	/**
	 * 同步资源人员
	 * @param empData
	 */
	public Map<String, String> syncResourceEmp(String empData, String prjId, String prjName, String prjCodeName, String casLoginUserName);
	
	public ObjectId createRoleUserForKnowledgeDomain(TeSysDefRoleUser teSysDefRoleUser);
	
	public void removeTeSysDefRoleUserByDomain(TeSysDefRoleUserVo teSysDefRoleUserVo,TeSysUser loginUser);
	
	public void removeTeSysDefRoleUserByGroup(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	public void removeTeSysDefRoleUserByFolder(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	public void removeWealthHouseTeSysDefRoleUserByFolder(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	public List<TeSysDefRoleUser> queryRoleUserByUserIdAndType(TeSysDefRoleUser2DefType defType,TeSysUser teSysUser);
	
	public List<TeSysDefRoleUser> queryRoleUserBySrcAndType(TeSysDefRoleUser2DefType defType,List<ObjectId> idList);
	
	public void changeDomainAdmin(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	public List<TeSysDefRoleUser> queryBuPrjAdmin(TeSysUserVo teSysUserVo);
	
	public List<TeSysDefRoleUser> queryBuAdmin(TeSysUserVo teSysUserVo);
	
	public List<TeSysDefRoleUser> querySysAdmin(TeSysUserVo teSysUserVo);
	
	public List<TeSysDefRoleUser> queryAdminBydefIdsAndRoleUser(List<ObjectId> idList, TeSysUser teSysUser) ;
	
	public List<TeSysDefRoleUser> queryAdmin(TeSysUser teSysUser,ObjectId rootFolderId);
	
	public List<TeSysDefRoleUser2UserVo> queryTeSysRoleUserVoByBase(SysDef kmBase);
	
	public void addSysRoleUserAdmin(ObjectId defId,List<String>roleUserIdList,TeSysUser teSysUser);
	
	public void removeSysRoleUserAdmin(ObjectId defId,List<String>roleUserIdList);
	
	public List<TeSysDefRoleUser> queryIsGroupRespOfGroupInfo(List<ObjectId> groupIdList, String groupRespCodeName, ObjectId loginUserId);

	String importSysRoleUser(MultipartFile file, TeSysDefRoleUser teSysDefRoleUser,
        TeSysUser loginUser);

    void updateSysRoleUser(TeSysDefRoleUserVo sysDefRoleUserVo);

	List<TeSysDefRoleUser2UserVo> queryUserByDefIdAndCurrentGroupId(TeSysDefRoleUserVo teSysDefRoleUserVo);
	
	List<TeSysDefRoleUser> findByDefTypeAndRoleAndUser(TeSysDefRoleUserVo teSysDefRoleUserVo);
	/**
	 * 通过定义ID和人员ID查询人员角色
	 * @param defId
	 * @param userId
	 * @return
	 */
	List<TeSysDefRoleUser> querySysRoleUserByDefIdAndUserId(ObjectId defId, ObjectId userId,List<ObjectId> roleIds);
	
	/**
	 * CMC运维知识库同步PSO人员到公共库下
	 * @param teSysDefRoleUserVo
	 * @param teSysUser
	 */
	public String syncPSOEmployee(TeSysDefRoleUserVo teSysDefRoleUserVo,TeSysUser teSysUser);
	
	/**
	 * 通过定义类型ID,定义ID集合和角色ID查询人员角色
	 * @param defTypeId
	 * @param defIds
	 * @param roleId
	 * @return
	 */
   List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(ObjectId defTypeId, List<ObjectId> defIds,ObjectId roleId);
   
	/**
	 * 通过定义类型ID,定义ID集合和角色ID查询人员角色
	 * @param defTypeId
	 * @param defIds
	 * @param roleId
	 * @param userNameCode
	 * @return
	 */
   List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(ObjectId defTypeId, List<ObjectId> defIds,ObjectId roleId,String userNameCode,Pager pager);

	List<TeSysDefRoleUser> queryRoleUsers(ObjectId defTypeId, List<ObjectId> defIds, List<ObjectId> roleIds, String userNameCode, Pager pager);
	
   /**
	 * 通过ID集合删除人员角色
	 * @param ids
	 */
   void removeByIds(List<ObjectId> ids);
	
   /**
  	 * 通过人员角色Id集合更新人员角色
  	 * @param ids
  	 * @param fieldsMap
  	 */
   void updateRoleUser(List<String> ids,Map<String, Object> fieldsMap);
   
	/**
	 * 获取用户
	 * 
	 * @param roleIds
	 *            角色
	 * @param defIds
	 *            定义
	 * @param codeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, SysDefTypeCodeName codeName);  
	
	/**
	 * 获取用户
	 * 
	 * @param roleIds
	 *            角色
	 * @param defIds
	 *            定义
	 * @param codeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, String userName ,SysDefTypeCodeName codeName);  
	
	/**
	 * 获取用户
	 * 
	 * @param roleIds
	 *            角色
	 * @param defId
	 *            定义
	 * @param codeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, ObjectId defId, SysDefTypeCodeName codeName);  
	
	/**
	 * 获取用户
	 * 
	 * @param roleIds
	 *            角色
	 * @param defId
	 *            定义
	 * @param codeName
	 *            定义类型
	 * @param isStd
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, ObjectId defId, SysDefTypeCodeName codeName,Boolean isStd);

	/**
	 * 获取用户
	 * @param roleIds 角色
	 * @param defIds 定义
 	 * @param srcDefIds 源定义
 	 * @param defTypeCodeName 定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, List<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName);

	/**
	 * 获取定义
	 * 
	 * @param userIds
	 *            用户id
	 * @param roleIds
	 *            角色id
	 * @param codeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getDefs(List<ObjectId> userIds, List<ObjectId> roleIds, SysDefTypeCodeName codeName);

	/**
	 * 新增用户角色记录
	 *
	 * @param userId    用户id
	 * @param roleId    角色id
	 * @param defIds    定义id
	 * @param loginUser 添加人
	 * @return
	 */
	List<ObjectId> addSysUserRoles(ObjectId userId, ObjectId roleId, List<ObjectId> defIds, TeSysUser loginUser);

	/**
	 * 
	 * @param userId
	 * @param roleId
	 * @param defId
	 * @return
	 * 
	 */
    ObjectId addSysUserRoles(ObjectId userId, ObjectId roleId, ObjectId defId,List<ObjectId> linkedPrdIds,ObjectId prjId);
    
	void removeSysUserRole(ObjectId id);
	void removeSysUserRole(ObjectId userId, ObjectId roleId, ObjectId defId, SysDefTypeCodeName codeName);  
	void removeSysUserRole(ObjectId userId, ObjectId roleId, List<ObjectId> defIds,
			SysDefTypeCodeName codeName) ;

	List<TeSysDefRoleUser> queryRoleUserList(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
											 Collection<ObjectId> defIds, SysDefTypeCodeName codeName);
	
	List<TeSysDefRoleUser> getUserRoles(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
			Collection<ObjectId> defIds, SysDefTypeCodeName codeName);
	
	/**
	 * 运维管理-人员权限-添加人员
	 * @param roleUserList
	 */
	public Map<String, Object> addOperationUser(List<TeSysDefRoleUserVo> roleUserList);
	
	/**
	 * 查询sbuId对应roleUser
	 */
	public List<TeSysDefRoleUser> queryBuRoleUser(String sbuId, String roleCodeName, String loginName);
	
	/**
	 * 验证运维管理负责人或管理员
	 */
	public List<TeSysDefRoleUser> sureOperationRoleUser(ObjectId userId, String defType);

	/**
	 * 验证是否具有项目运营管理权限
	 * */
	TeSysDefRoleUser queryPomAuthByLoginName(String loginName);

	/**
	 * 项目运营管理访问权限
	 * @param userId
	 * @return
	 */
	Boolean queryPomAuth(ObjectId userId);
	
	List<TeSysDefRoleUser> findTeSysDefRoleUserByCondition(
			TeSysDefRoleUser teSysDefRoleUser);
	
	/**
	 * 经营指标访问清单访问权限
	 * @param userId
	 * @return
	 */
	Boolean visitAnalysisAuth(ObjectId userId);

	List<TeSysDef> getPrjUserRoleDef();
	
	List<com.linkus.sysuser.model.TeSysDefRoleUser2SysDef> getPrjsByUser(ObjectId userId);

	List<TeSysDefRoleUser> queryBuOmAdmin(TeSysUser sysUser);
	
	/**
	 * 根据角色和类型获取用户权限
	 *
	 * @param userIds 用户
	 * @param roleId 角色Id
	 * @return 权限
	 */
	List<TeSysDefRoleUser> queryPrdCtlgIdsByRoleUserId(List<ObjectId> userIds, ObjectId roleId);

	/**
	 *  查询组织名称
	 */
	List<OrganizeVo> queryOrganizeName();

	/**
	 * 通过定义类型ID,定义ID集合和角色ID查询人员角色
	 * @param defTypeId
	 * @param prdId
	 * @param roleId
	 * @param userNameCode
	 * @return
	 */
	List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndPrdId(ObjectId defTypeId, ObjectId prdId, ObjectId roleId, String userNameCode);
	
	List<String> queryUserAuthedBuCodes(ObjectId userId);

	void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList);

	void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList,
							 List<ObjectId> prdIdList, List<ObjectId> prdCtlgIdList);

	/**
	 * 如果查询的角色中包含“成员”角色
	 * 查询用户所在的CC或者BU，拥有哪些产品目录的权限
	 * 对拥有权限的目录，创建一个临时的该用户的权限
	 * @param roleIds
	 * @param userIds
	 * @param alreadyHaveSysDefRoleUserList
	 * @param prdIdList
	 * @param prdCtlgIdList
	 * @param isContainParentPrdCtlg 是否包含父目录，校验当前目录是否有权限时，需要考虑父目录是否有权限
	 * @param isAllPrdCtlgAuth false（默认）：产品下已有权限，对该产品就不再创建新的权限；true：会对所有人员生成所有目录的临时权限，但不产生重复权限
	 * @auth feizj
	 */
	void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList,
							 Collection<ObjectId> prdIdList, Collection<ObjectId> prdCtlgIdList, Boolean isContainParentPrdCtlg,
							 Boolean isAllPrdCtlgAuth);

	void getCCRoleUsers(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers);
	
    void getBuAndCcRoleUsers(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers,
                             List<ObjectId> prdIds, List<ObjectId> ctlgIds);

	/**
	 * 根据角色、defId、用户ID，查询对应数据
	 * @param roleId
	 * @param prjId
	 * @param id
	 * @param prjGroup
	 * @return
	 */
	List<TeSysDefRoleUser> getUsers(List<ObjectId> roleId, ObjectId srcDefId,ObjectId defId, ObjectId userId, SysDefTypeCodeName prjGroup);

	TeSysDefRoleUser addTeSysDefRoleUserForPrjGroup4StructureAndStaff(TeSysDefRoleUser toAddUser);

	Map<String,Object> batchAddUser4StructureAndStaff(List<TeSysDefRoleUser> toAddUserList, TeSysUser loginUser);

	/**
	 * 查询角色人员信息
	 *
	 * @param defIds
	 * @param defTypeCodeName
	 * @param roleId
	 * @return
	 */
	List<TeSysDefRoleUser> querySysDefRoleUsers(List<ObjectId> defIds, String defTypeCodeName, ObjectId roleId);

	/**
	 * 查询角色人员信息
	 *
	 * @param defTypeCodeName
	 * @param roleId
	 * @param roleUserIds
	 * @return
	 */
	List<TeSysDefRoleUser> querySysDefRoleUsers(String defTypeCodeName, ObjectId roleId, List<ObjectId> roleUserIds);

	/**
	 * 根据defId、roleId、roleUserIdd查询系统角色人员
	 *
	 * @param defId
	 * @param roleId
	 * @param roleUserId
	 * @return
	 */
	TeSysDefRoleUser querySysDefRoleUser(ObjectId defId, ObjectId roleId, ObjectId roleUserId);

	/**
	 * 查询角色人员信息
	 *
	 * @param defIds
	 * @param defTypeCodeName
	 * @param roleId
	 * @param roleUserIds
	 * @param srcDefId
	 * @return
	 */
	List<TeSysDefRoleUser> querySysDefRoleUsers(List<ObjectId> defIds, String defTypeCodeName, ObjectId roleId, List<ObjectId> roleUserIds, ObjectId srcDefId);

    void joinInPrj(TeSysDefRoleUser sysDefRoleUser, TeSysUser loginUser);

	void applyExitPrj(Date quitTime, TeSysUser loginUser, ObjectId prjId);

	List<Map<String,Object>> selectPrjForNotJoin(TeSysUser loginUser,String fuzzword);

	void updateEnterTimeAndGroup(String loginName, Date enterTime, ObjectId groupId, ObjectId prjId, ObjectId defPara,List<ObjectId> roleIds);
	/**
	 * 校验当前登陆人是否是产品管理员
	 */
	boolean checkIsPrdAdmin(TeSysUser loginUser);
	/**
	 * 校验当前登陆人是否是项目管理员
	 */
	boolean checkIsPrjAdmin(ObjectId prjId,TeSysUser loginUser);

	/**
	 * 校验当前登陆人是否是该项目的成员
	 */
	boolean checkIsPrjMember(ObjectId prjId,TeSysUser loginUser);
	/**
	 * 校验当前登陆人是否是该部门的成员
	 */
	TeSysDef checkIsDeptMember(ObjectId deptId,TeSysUser loginUser);
	/**
	 * 查询当前登陆人所有的部门
	 */
	List<SysDef> queryAllDept(TeSysUser loginUser);

	/**
	 * 校验当前登陆人是否是部门管理员
	 */
	boolean checkIsDeptAdmin(ObjectId deptId,TeSysUser loginUser);
	//查询bu项目管理员下的所有bu信息
	List<SysDef> queryAllBuByBuPrjAdmin(TeSysUser loginUser);

    /**
     * 验证是否是项目管理员
     * @param prjId
     * @param loginUser
     * @return
     */
    Boolean checkIsPrjManagerAndAdmin(ObjectId prjId, TeSysUser loginUser);

	/**
	 * 验证是否是项目人员效能管理员
	 * @param buId
	 * @param loginUser
	 * @return
	 */
	Boolean checkIsPrjEffectEvalutionAdmin(ObjectId buId, TeSysUser loginUser);

	List<SysDef> getBuByUserId(ObjectId userId);

    /**
     * 验证是否是BU项目预算管理员
     * @param buId
     * @param loginUser
     * @return
     */
    Boolean checkIsBuPrjBudgetAdmin(ObjectId buId, TeSysUser loginUser);

    /**
     * 验证是否是BU管理员
     * @param buId
     * @param loginUser
     * @return
     */
    Boolean checkIsBuAdmin(ObjectId buId, TeSysUser loginUser);

	/**
	 * 根据id列表逻辑删除sysDefRoleUser列表
	 *
	 * @param ids
	 */
	void deleteByIds(List<ObjectId> ids);

	/**
	 * 批量更新人员角色组记录
	 *
	 * @param empIdList
	 * @param prjId
	 * @param prjGroupId
	 * @param updateRole
	 */
	void bulkUpdateTeamEmpRole(List<ObjectId> empIdList, ObjectId prjId, ObjectId prjGroupId, TeIdNameCn updateRole);

	List<TeSysDef> getSysDefByCndtItemCids(String defTypeCode, String srcDefCode, List<ObjectId> cndtItemCids, Boolean auth, TeSysUser loginUser );

    List<TeSysDef> getSysDefByCndtItemCids(String defTypeCode, String srcDefCode, List<ObjectId> cndtItemCids, List<ObjectId> itemIds, Boolean auth, TeSysUser loginUser );

	void save(ItfSysDefRoleUser user);

	List<ItfSysDefRoleUser> queryData(ItfSysDefRoleUserQuery req);

	Integer updateIsValid(ItfSysDefRoleUserQuery req, boolean flag);

	// 确认是否为业务管理员
	boolean isBizManager(ObjectId prdCtlgId, TeSysUser loginUser);

	/**
	 * 查询质量体系跟踪人负责的省份
	 * @param userId
	 * @return
	 */
	List<ObjectId> listQualityTrackerProvId(ObjectId userId);
}
