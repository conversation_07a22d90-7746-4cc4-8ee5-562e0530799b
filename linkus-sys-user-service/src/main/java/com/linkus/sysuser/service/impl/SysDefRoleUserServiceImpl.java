package com.linkus.sysuser.service.impl;

import com.linkus.base.constants.EurekaClientConstants;
import com.linkus.base.constants.PrjPlanConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.constants.SysOmsConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.DbAndOperator;
import com.linkus.base.db.base.condition.impl.DbEqualCondition;
import com.linkus.base.db.base.condition.impl.DbExistCondition;
import com.linkus.base.db.base.condition.impl.DbFuzzyCondition;
import com.linkus.base.db.base.condition.impl.DbInCondition;
import com.linkus.base.db.base.condition.impl.DbLikeCondition;
import com.linkus.base.db.base.condition.impl.DbOrOperator;
import com.linkus.base.db.base.condition.impl.mini.DC_ALL;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_L;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.MongoDaoSupport;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.JsonUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.dao.SysDefTypeDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefQuerySortKey;
import com.linkus.sys.model.SysDefType;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeDefType;
import com.linkus.sys.model.po.TeSrcDef;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.model.po.TeSysDefType;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.api.model.ItfSysDefRoleUser;
import com.linkus.sysuser.api.model.ItfSysDefRoleUserQuery;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.dao.ISysDefUserInfoDao;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.dao.impl.ClockDaoImpl;
import com.linkus.sysuser.model.TeClock;
import com.linkus.sysuser.model.TeSysDefClickLog2User;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2DefType;
import com.linkus.sysuser.model.TeSysDefRoleUser2Role;
import com.linkus.sysuser.model.TeSysDefRoleUser2SysDef;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysDefUserInfo;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.sysuser.vo.OrganizeVo;
import com.linkus.sysuser.vo.TeSysDefRoleUser2UserVo;
import com.linkus.sysuser.vo.TeSysDefRoleUserVo;
import com.linkus.sysuser.vo.TeSysUserVo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service("SysDefRoleUserServiceImpl")
public class SysDefRoleUserServiceImpl extends MongoDaoSupport<TeSysDefRoleUser> implements ISysDefRoleUserService {
	
	@Autowired
	private ISysDefRoleUserDao sysDefRoleUserDao;

	@Resource
	private ISysDefService sysDefService;
	
	@Autowired
	private SysDefTypeDao sysDefTypeDao;
	
	@Autowired
	private ISysUserService sysUserService;
	
	@Autowired
	private ISysUserDao sysUserDao;

    @Autowired
    private ISysDefUserInfoDao sysDefUserInfoDao;
    @Autowired
    private SysDefDao sysDefDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;

	@Autowired
    private ClockDaoImpl clockDao;

	@Resource
	private RestTemplate restTemplate;

    public static final String PASSWORD = "123456";
    public static final String EMPLOYEETYPE = "Outsource";
    public static final ObjectId CMC_DEF_ID = new ObjectId("5aa60b9429974f7cabe911b8");

	@Override
	public List<TeSysDefRoleUser> addTeSysDefRoleUserList(List<TeSysDefRoleUser> teSysDefRoleUsers) {
		return sysDefRoleUserDao.batchSave(teSysDefRoleUsers);
	}
	
	/**
	 * 为项目小组添加成员
	 * 
	 */
	@Override
	public TeSysDefRoleUser addTeSysDefRoleUserForPrjGroup(TeSysDefRoleUser teSysDefRoleUser, TeSysDefUserInfo sysDefUserInfo) {
		//查询当前小组下是否存在该成员、重复判断
		List<TeSysDefRoleUser> findByDefIdAndUser = sysDefRoleUserDao.findByDefIdAndUser(teSysDefRoleUser);
		if(teSysDefRoleUser.getRoleUser() == null) {
	        throw new BaseException("项目角色人员未定义");
		}
        if(teSysDefRoleUser.getAddUser() == null) {
            throw new BaseException("项目角色新增人员未定义");
        }
        if(teSysDefRoleUser.getSrcDef() == null) {
            throw new BaseException("项目未定义");
        }
		if(findByDefIdAndUser!=null && !findByDefIdAndUser.isEmpty()){
			throw new BaseException(teSysDefRoleUser.getRoleUser().getUserName()+"已经在当前小组中！");
		}
		//defType给定
		TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		teSysDefRoleUser.setDefType(defType);
		//srcDef传入
		//defId传入
		//role给定
		if(StringUtil.isNull(teSysDefRoleUser.getRole())){
			List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
			TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
			role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
			role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
			roleUsers.add(role);
			teSysDefRoleUser.setRole(roleUsers);
		}
		//roleUser传入
		//isValid给定
		teSysDefRoleUser.setIsValid(true);
		//addTime给定
		teSysDefRoleUser.setAddTime(new Date());
		//addUser传入
		teSysDefRoleUser = sysDefRoleUserDao.save(teSysDefRoleUser);

        TeUser defUser = new TeUser();
        defUser.setJobCode(teSysDefRoleUser.getRoleUser().getJobCode());
        defUser.setUserId(teSysDefRoleUser.getRoleUser().getUserId());
        defUser.setLoginName(teSysDefRoleUser.getRoleUser().getLoginName());
        defUser.setUserName(teSysDefRoleUser.getRoleUser().getUserName());
        TeUser addUser = new TeUser();
        addUser.setJobCode(teSysDefRoleUser.getAddUser().getJobCode());
        addUser.setUserId(teSysDefRoleUser.getAddUser().getUserId());
        addUser.setLoginName(teSysDefRoleUser.getAddUser().getLoginName());
        addUser.setUserName(teSysDefRoleUser.getAddUser().getUserName());
        TeIdNameCn srcDef = new TeIdNameCn();
        srcDef.setCid(teSysDefRoleUser.getSrcDef().getSrcDefId());
        srcDef.setCodeName(teSysDefRoleUser.getSrcDef().getSrcDefCodeName());
        srcDef.setName(teSysDefRoleUser.getSrcDef().getSrcDefName());
        
        if(sysDefUserInfo == null) {
          sysDefUserInfo = new TeSysDefUserInfo();
          sysDefUserInfo.setDesc("");
          sysDefUserInfo.setStartDate(new Date());
          sysDefUserInfo.setAddTime(new Date());
        }

        SysDef sysDef = sysDefService.getSysDefById(teSysDefRoleUser.getDefId());
        TeIdNameCn def = new TeIdNameCn();
        def.setCid(sysDef.getId());
        def.setCodeName(sysDef.getCodeName());
        def.setName(sysDef.getDefName());
        TeIdNameCn userDefType = new TeIdNameCn();
        userDefType.setCid(sysDef.getDefType().getId());
        userDefType.setCodeName(sysDef.getDefType().getCodeName());
        userDefType.setName(sysDef.getDefType().getDefTypeName());
        sysDefUserInfo.setSrcDef(srcDef);
        sysDefUserInfo.setDefType(userDefType);
        sysDefUserInfo.setDef(def);
        sysDefUserInfo.setDefUser(defUser);
        sysDefUserInfo.setAddUser(addUser);
        sysDefUserInfo.setIsValid(true);
        sysDefUserInfoDao.save(sysDefUserInfo);
		return teSysDefRoleUser;
	}

	/**
	 * 为项目小组添加成员
	 *
	 */
	@Override
	public TeSysDefRoleUser addTeSysDefRoleUserForPrjGroup4StructureAndStaff(TeSysDefRoleUser toAddUser) {

		if(toAddUser.getRoleUser() == null) {
			throw new BaseException("项目角色人员未定义");
		}
		if(toAddUser.getAddUser() == null) {
			throw new BaseException("项目角色新增人员未定义");
		}
		if(toAddUser.getSrcDef() == null) {
			throw new BaseException("项目未定义");
		}

		//查询当前小组下是否存在该成员、重复判断
		List<TeSysDefRoleUser> findByDefIdAndUser = sysDefRoleUserDao.findByDefIdAndUser(toAddUser);
		if(findByDefIdAndUser!=null && !findByDefIdAndUser.isEmpty()){
			throw new BaseException(toAddUser.getRoleUser().getUserName()+"已经在当前小组中！");
		}
		List<TeSysDefRoleUser> expiredUser = sysDefRoleUserDao.findExpiredUserByDefIdAndUser(toAddUser);
		//比较添加的成员进入项目时间与原来的数据是否有冲突
		//1.获取存量数据中最大的退出日期
		Date maxEndDate = null;
		for(TeSysDefRoleUser user: expiredUser){
			Date quitTime = user.getQuitTime();
			if(maxEndDate == null){
				maxEndDate = quitTime;
			}else if(com.linkus.base.util.DateUtil.compareDate(quitTime,maxEndDate)>0){
				maxEndDate = quitTime;
			}
		}
		//2.比较新加入的数据的进入日期是否大于他的最大退出日期，如果否，则给出人员清单“以下人员的进入日期和之前的退出日期有冲突，请修改后再导入！”
		if(null != maxEndDate){
			Date toAddEnterTime = toAddUser.getEnterTime();
			if(com.linkus.base.util.DateUtil.compareDate(toAddEnterTime,maxEndDate)<0){
				throw new BaseException(toAddUser.getRoleUser().getUserName()+"已存在该时间的记录，请确认！");
			}
		}
		//defType给定
		TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		toAddUser.setDefType(defType);
		//srcDef传入
		//defId传入
		//role给定
		if(StringUtil.isNull(toAddUser.getRole())){
			List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
			TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
			role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
			role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
			roleUsers.add(role);
			toAddUser.setRole(roleUsers);
		}
		//roleUser传入
		//isValid给定
		toAddUser.setIsValid(true);
		//addTime给定
		toAddUser.setAddTime(new Date());
		//addUser传入
		toAddUser = sysDefRoleUserDao.save(toAddUser);
		return toAddUser;
	}

	/**
	 * 批量添加人员
	 */
	public Map<String,Object> batchAddUser4StructureAndStaff(List<TeSysDefRoleUser> toAddUserList,TeSysUser loginUser){
		Map<String,Object> result = new HashMap<>();
		StringBuilder msg = new StringBuilder();
		//1.获取所有人员的ID和他们的组织名，项目名
		List<ObjectId> toAddUserIdList = new ArrayList<>();//待添加的人员ID
		ObjectId prjId = null;
		ObjectId orgId = null;
		for(TeSysDefRoleUser user:toAddUserList){
			toAddUserIdList.add(user.getRoleUser().getUserId());
			prjId = user.getSrcDef().getSrcDefId();
			orgId = user.getDefId();
		}

		//2.根据人员名称查询是否有的人是已经健在的，防止重复添加(所有的在项人员只能出现在一个组织下)
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
		conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId),toAddUserIdList));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser_srcDef);
		fieldNames.add(DFN.common_isValid);
		fieldNames.add(DFN.SysDefCnfg.defId);
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.sysDefRoleUser_quitTime);
		fieldNames.add(DFN.sysDefRoleUser_enterTime);
		List<TeSysDefRoleUser> existedUserList = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);//已经存在的人员


		if(existedUserList == null || existedUserList.size() == 0){//均可以添加
			//3.查询待添加的人员是否是历史信息(剔除删除人员，即quitTime为空)，
			// 如果是历史信息 校验待添加的人员的加入项目时间是否正确，并且给没有角色的人员设置成员角色
			conds.clear();
			conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
			conds.add(new DC_E(DFN.common_isValid,false));
			conds.add(new DC_E(DFN.sysDefRoleUser_quitTime,null,true));
			conds.add(new DC_E(DFN.sysDefRoleUser_enterTime,null,true));
			conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId),toAddUserIdList));
			conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
			List<TeSysDefRoleUser> hisUserList = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);//历史人员信息
			//获取历史人员中，每个人员最大的退出时间，放入 hisUserMap中
			Map<String,Date> hisUserMap = new HashMap<>();
			for(TeSysDefRoleUser hisUser:hisUserList){
				String userId = hisUser.getRoleUser().getUserId().toHexString();
				Date quitTime = hisUser.getQuitTime();
				if(hisUserMap.get(userId) == null){
					hisUserMap.put(userId,quitTime);
				}else{
					Date existQuitTime = hisUserMap.get(userId);
					if(com.linkus.base.util.DateUtil.compareDate(existQuitTime,quitTime) < 0){
						existQuitTime = quitTime;
					}
					hisUserMap.put(userId,existQuitTime);
				}
			}
			for(TeSysDefRoleUser toAddUser : toAddUserList){
				Date toAddUserEnterTime = toAddUser.getEnterTime();
				String toAddUserId = toAddUser.getRoleUser().getUserId().toHexString();
				List<TeSysDefRoleUser2Role> users = toAddUser.getRole();
				if (null == users && users.isEmpty()){
					List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
					TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
					role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
					role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
					role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
					roleUsers.add(role);
					toAddUser.setRole(roleUsers);
				} else {
					List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
					List<ObjectId> ids = users.stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
					List<TeSysDef> teSysDefsByIds = sysDefService.getTeSysDefsByIds(ids);
					for (TeSysDef defsById : teSysDefsByIds) {
						TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
						role.setRoleId(defsById.getId());
						role.setRoleCodeName(defsById.getCodeName());
						role.setRoleName(defsById.getDefName());
						roleUsers.add(role);
						toAddUser.setRole(roleUsers);
					}
				}

				TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
				defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
				defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);
				defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
				toAddUser.setDefType(defType);
				toAddUser.setAddTime(new Date());
				TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
				addUser.setUserId(loginUser.getId());
				addUser.setLoginName(loginUser.getLoginName());
				addUser.setUserName(loginUser.getUserName());
				addUser.setJobCode(loginUser.getJobCode());
				toAddUser.setAddUser(addUser);
				toAddUser.setIsValid(true);
				if(hisUserMap.get(toAddUserId) != null){
					Date maxExitQuitTime = hisUserMap.get(toAddUserId);
					if(com.linkus.base.util.DateUtil.compareDate(toAddUserEnterTime,maxExitQuitTime) <= 0){
						msg.append(toAddUser.getRoleUser().getUserName()+",") ;
					}
				}else{
					continue;
				}

			}
			if(msg.length() > 0){
				msg = new StringBuilder(msg.substring(0,msg.length() - 1));
				msg.append("的新加入项目日期需大于最晚退出项目日期");
			}else{
				//4.添加
				List<TeSysDefRoleUser> addResult = sysDefRoleUserDao.batchSave(toAddUserList);//加入到数据库中的数据
				result.put("addInfo",addResult);
				msg = new StringBuilder("success");
			}

		}else{//有重复数据，需要给用户进行提示
			//3.获取已健在人员的名称，返回给前台
			for(TeSysDefRoleUser existedUser : existedUserList){
				msg.append(existedUser.getRoleUser().getUserName()+",") ;
			}
			msg = new StringBuilder(msg.substring(0,msg.length() - 1));
			msg.append("已加入项目，无法重复添加！");
		}
		result.put("msg",msg.toString());

		return result;
	}

	private void updateSysDefRoleUser(TeSysDefRoleUser teSysDefRoleUser) {
      if(teSysDefRoleUser.getSrcDef() == null || teSysDefRoleUser.getSrcDef().getSrcDefId() == null) {
        throw new BaseException("所属项目未定义");
      }
      if(teSysDefRoleUser.getDefId() == null) {
        throw new BaseException("归属小组未定义");
      }
      if(teSysDefRoleUser.getRoleUser() == null || teSysDefRoleUser.getRoleUser().getLoginName() == null) {
        throw new BaseException("NT账号未定义");
      }
      List<IDbCondition> conds = new ArrayList<IDbCondition>();
      List<UpdataData> updates = new ArrayList<UpdataData>();
      
      updates.clear();
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), teSysDefRoleUser.getSrcDef().getSrcDefId()));
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser__defId, teSysDefRoleUser.getDefId()));
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName), teSysDefRoleUser.getRoleUser().getLoginName()));
	  if (null != teSysDefRoleUser.getRole()){
		  updates.add(new UpdataData(DFN.sysDefRoleUser__role, teSysDefRoleUser.getRole()));
	  }
      sysDefRoleUserDao.updateByConds(conds, updates);
  }
    
    private void updateSysDefUserInfo(TeSysDefUserInfo sysDefUserInfo) {
      if(sysDefUserInfo.getSrcDef() == null || sysDefUserInfo.getSrcDef().getCid() == null) {
        throw new BaseException("所属项目未定义");
      }
      if(sysDefUserInfo.getDef() == null || sysDefUserInfo.getDef().getCid() == null) {
        throw new BaseException("归属小组未定义");
      }
      if(sysDefUserInfo.getDefUser() == null || sysDefUserInfo.getDefUser().getLoginName() == null) {
        throw new BaseException("NT账号未定义");
      }
      List<IDbCondition> conds = new ArrayList<IDbCondition>();
      conds.add(new DbEqualCondition(DFN.SysDefUserInfo.srcDef.dot(DFN.common_cid), sysDefUserInfo.getSrcDef().getCid()));
      conds.add(new DbEqualCondition(DFN.SysDefUserInfo.def.dot(DFN.common_cid), sysDefUserInfo.getDef().getCid()));
      conds.add(new DbEqualCondition(DFN.SysDefUserInfo.defUser.dot(DFN.SysDefUserInfo.loginName), sysDefUserInfo.getDefUser().getLoginName()));
      conds.add(new DbEqualCondition(DFN.common_isValid, true));
      List<UpdataData> updates = new ArrayList<UpdataData>();
      updates.add(new UpdataData(DFN.SysDefUserInfo.startDate, sysDefUserInfo.getStartDate()));
      updates.add(new UpdataData(DFN.SysDefUserInfo.endDate, sysDefUserInfo.getEndDate()));
      updates.add(new UpdataData(DFN.SysDefUserInfo.desc, sysDefUserInfo.getDesc()));
      sysDefUserInfoDao.updateByConds(conds, updates);
  }
    
    /**
     * 为项目小组创建并添加成员
     * 
     * 1、创建用户
     * 2、查询当前小组下是否存在该成员、重复判断
     * 3、添加
     */
    @Override
    @Transactional
    public TeSysDefRoleUser createAddUserForPrjGroup(TeSysDefRoleUser teSysDefRoleUser) {
        if(teSysDefRoleUser == null || teSysDefRoleUser.getRoleUser() == null) {
          throw new BaseException("注册的用户为空");
        }
        TeSysUser sysUser = registerSysUser(teSysDefRoleUser.getRoleUser().getUserName(), 
            teSysDefRoleUser.getRoleUser().getLoginName());
        teSysDefRoleUser.getRoleUser().setUserId(sysUser.getId());
        return addTeSysDefRoleUserForPrjGroup(teSysDefRoleUser, null);
    }
	
	@Override
	public TeSysDefRoleUser addTeSysDefRoleUserForFolder(TeSysDefRoleUser teSysDefRoleUser) {
		//查询当前小组下是否存在该成员、重复判断
		List<TeSysDefRoleUser> findByDefIdAndUser = sysDefRoleUserDao.findByDefIdAndUser(teSysDefRoleUser);
		if(findByDefIdAndUser!=null && !findByDefIdAndUser.isEmpty()){
			throw new BaseException(findByDefIdAndUser.get(0).getRoleUser().getUserName()+"已经是管理员！");
		}
		
		//defId传入
		//role给定
		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
		role.setRoleName(SysDefConstants.ADMIN_DEF_NAME);
		role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		
		//roleUserId传入
		ObjectId roleUserId = teSysDefRoleUser.getRoleUser().getUserId();
		TeSysUser teSysUser = sysUserService.findById(roleUserId);
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUser.getId());
		roleUser.setUserName(teSysUser.getUserName());
		roleUser.setLoginName(teSysUser.getLoginName());
		roleUser.setJobCode(teSysUser.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		//isValid给定
		teSysDefRoleUser.setIsValid(true);
		//addTime给定
		teSysDefRoleUser.setAddTime(new Date());
		//addUser传入
		teSysDefRoleUser=sysDefRoleUserDao.save(teSysDefRoleUser);
		return teSysDefRoleUser;
	}
	
	@Override
	public TeSysDefRoleUser wealthHouseAddTeSysDefRoleUserForFolder(TeSysDefRoleUser teSysDefRoleUser) {
		//查询当前小组下是否存在该成员、重复判断
		List<TeSysDefRoleUser> findByDefIdAndUser = sysDefRoleUserDao.findByDefIdAndUser(teSysDefRoleUser);
		if(findByDefIdAndUser!=null && !findByDefIdAndUser.isEmpty()){
			throw new BaseException(findByDefIdAndUser.get(0).getRoleUser().getUserName()+"已经是管理员！");
		}
		
		//defId传入
		//role给定
		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
		role.setRoleName(SysDefConstants.ADMIN_DEF_NAME);
		role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		
		//roleUserId传入
		ObjectId roleUserId = teSysDefRoleUser.getRoleUser().getUserId();
		TeSysUser teSysUser = sysUserService.findById(roleUserId);
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUser.getId());
		roleUser.setUserName(teSysUser.getUserName());
		roleUser.setLoginName(teSysUser.getLoginName());
		roleUser.setJobCode(teSysUser.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		//TeSysDefRoleUser2DefType
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(StringUtil.toObjectId(SysDefTypeConstants.FILEFOLDER_DEF_ID));
		defType.setDefTypeName(SysDefConstants.FOLDER_NAME);
		defType.setDefTypeCodeName(SysDefTypeCodeName.FILE_FOLDER.getValue());
		teSysDefRoleUser.setDefType(defType);
		
		//isValid给定
		teSysDefRoleUser.setIsValid(true);
		//addTime给定
		teSysDefRoleUser.setAddTime(new Date());
		//addUser传入
		teSysDefRoleUser=sysDefRoleUserDao.save(teSysDefRoleUser);
		return teSysDefRoleUser;
	}
	
	/**
	 * changeGroupRespForGroup 更换组长信息
	 */
	@Override
	public void changeGroupRespForGroup(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		//对象转换
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(teSysDefRoleUserVo,teSysDefRoleUser);
		if (null != teSysDefRoleUserVo.getRole()){
			List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
			TeSysDefRoleUser2Role role = teSysDefRoleUserVo.getRole();
			roles.add(role);
			teSysDefRoleUser.setRole(roles);
		}
		//项目小组type
		TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		
		teSysDefRoleUser.setDefType(defType);
		teSysDefRoleUser.setIsValid(true);
		
		List<TeSysDefRoleUser> tmps = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);

		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
		
		if(teSysDefRoleUserVo.getIsGroupResp()){
			//组长角色
			role.setRoleId(new ObjectId(SysDefConstants.GROUPRESP_DEF_ID));
			role.setRoleName(SysDefConstants.GROUPRESP_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.GROUPRESP_CODENAME);
			roleUsers.add(role);
		}else{
			//成员角色
			role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
			role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
			roleUsers.add(role);
		}
		
		for (TeSysDefRoleUser t : tmps) {
			t.setRole(roleUsers);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		}
	}

	/**
	 * 更换知识域下域管理员信息
	 */
	@Override
	public void changeDomainAdmin(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		//对象转换
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(teSysDefRoleUserVo,teSysDefRoleUser);
		if (null != teSysDefRoleUserVo.getRole()){
			List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
			TeSysDefRoleUser2Role role = teSysDefRoleUserVo.getRole();
			roles.add(role);
			teSysDefRoleUser.setRole(roles);
		}
		//知识域type
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.KMDOMAIN_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.KMDOMAIN_DEF_TYPENAME);
		defType.setDefTypeCodeName(SysDefTypeConstants.KMDOMAIN_CODENAME);
		
		teSysDefRoleUser.setDefType(defType);
		teSysDefRoleUser.setIsValid(true);
		
		List<TeSysDefRoleUser> tmps = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);

		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role=new TeSysDefRoleUser2Role();
		
		if(teSysDefRoleUserVo.getIsGroupResp()){
			//管理员角色
			role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
			role.setRoleName(SysDefConstants.ADMIN_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
			roleUsers.add(role);
		}else{
			//成员角色
			role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
			role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
			role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
			roleUsers.add(role);
		}
				
		for (TeSysDefRoleUser t : tmps) {
			t.setRole(roleUsers);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		}

	}
	
	/**
	 * removeTeSysDefRoleUser 删除项目小组成员
	 */
	@Override
	public void removeTeSysDefRoleUser(TeSysDefRoleUser teSysDefRoleUser) {
		TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		teSysDefRoleUser.setDefType(defType);
		List<TeSysDefRoleUser> tmps = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
		for (TeSysDefRoleUser t : tmps) {
			t.setIsValid(false);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
			
		    List<IDbCondition> conds = new ArrayList<IDbCondition>();
		    conds.add(new DbEqualCondition(DFN.SysDefUserInfo.srcDef.dot(DFN.common_cid), t.getSrcDef().getSrcDefId()));
		    conds.add(new DbEqualCondition(DFN.SysDefUserInfo.def.dot(DFN.common_cid), t.getDefId()));
		    conds.add(new DbEqualCondition(DFN.SysDefUserInfo.defUser.dot(DFN.SysDefUserInfo.loginName), t.getRoleUser().getLoginName()));
		    conds.add(new DbEqualCondition(DFN.common_isValid, true));
		    List<UpdataData> updates = new ArrayList<UpdataData>();
		    updates.add(new UpdataData(DFN.common_isValid, false));
		    sysDefUserInfoDao.updateByConds(conds, updates);
		}
	}
	
	@Override
	public void removeByDefIdsAndTeSysDefRoleUser(List<ObjectId> defIds,TeSysDefRoleUser teSysDefRoleUser) {
		List<TeSysDefRoleUser> tmps = sysDefRoleUserDao.findByTeSysDefRoleUserAndDefIds(teSysDefRoleUser, defIds);
		for (TeSysDefRoleUser t : tmps) {
			t.setIsValid(false);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		}
		
	}
	
	@Override
	public List<TeSysDefRoleUser> queryByUserAndDefIdAndDefType(TeSysDefRoleUser teSysDefRoleUser) {
		return sysDefRoleUserDao.findByUserAndDefIdAndDefType(teSysDefRoleUser);
	}

	/**
	 * 根据 defId || defType.defTypeCodeName || role.roleCodeName || roleUser.userName 查询用户信息
	 * @param sysDefRoleUser
	 */
	@Override
	public List<TeSysDefRoleUser2UserVo> querySysUserDistinctColl(TeSysDefRoleUser sysDefRoleUser) {

		List<TeSysDefRoleUser> userList = sysDefRoleUserDao.querySysUserDistinctColl(sysDefRoleUser);
		
		List<TeSysDefRoleUser2UserVo> userVoList = new LinkedList<>();
		//用来剔重 K：loginName V：
		Set<String> userMap = new HashSet();

		for (TeSysDefRoleUser teSysDefRoleUser : userList) {
			TeSysDefRoleUser2UserVo userVo = new TeSysDefRoleUser2UserVo();
			if (null != teSysDefRoleUser.getDefId()){
				userVo.setDefId(teSysDefRoleUser.getDefId());
			}
			TeSysDefRoleUser2User roleUser = teSysDefRoleUser.getRoleUser();
			if (null != roleUser && null != roleUser.getUserId() && !userMap.contains(roleUser.getUserId().toHexString())){
				userVo.setJobCode(roleUser.getJobCode());
				userVo.setLoginName(roleUser.getLoginName());
				userVo.setUserId(roleUser.getUserId());
				userVo.setUserName(roleUser.getUserName());
				userVoList.add(userVo);
				userMap.add(roleUser.getUserId().toHexString());
			}
		}
		return userVoList;
	}

	@Override
	public List<TeSysDefRoleUser2UserVo> querySysUserByParentCtlg(TeSysDefRoleUser sysDefRoleUser) {
		ObjectId defId = sysDefRoleUser.getDefId();
		ObjectId roleId = sysDefRoleUser.getRole().get(0).getRoleId();
		TeSysDef sysDef = sysDefDao.findById(defId);
		if (null == sysDef){
			throw BusinessException.initExc("defId不存在");
		}
		List<ObjectId> parent2SelfIds = sysDef.getParent2SelfIds();

		List<IDbCondition> conds = new ArrayList<IDbCondition>();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_I<>(DFN.sysDefRoleUser__defId,parent2SelfIds));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId),roleId));
		List<TeSysDefRoleUser> userList = sysDefRoleUserDao.findByConds(conds, null);
		 filterFireUsers(userList);
		List<TeSysDefRoleUser2UserVo> userVoList = new LinkedList<>();
		//用来剔重 K：loginName V：
		Set<String> userMap = new HashSet();

		for (TeSysDefRoleUser teSysDefRoleUser : userList) {
			TeSysDefRoleUser2UserVo userVo = new TeSysDefRoleUser2UserVo();
			if (null != teSysDefRoleUser.getDefId()){
				userVo.setDefId(teSysDefRoleUser.getDefId());
			}
			TeSysDefRoleUser2User roleUser = teSysDefRoleUser.getRoleUser();
			if (null != roleUser && null != roleUser.getUserId() && !userMap.contains(roleUser.getUserId().toHexString())){
				userVo.setJobCode(roleUser.getJobCode());
				userVo.setLoginName(roleUser.getLoginName());
				userVo.setUserId(roleUser.getUserId());
				userVo.setUserName(roleUser.getUserName());
				userVoList.add(userVo);
				userMap.add(roleUser.getUserId().toHexString());
			}
		}
		return userVoList;
	}

	//获取当前角色列表中在职人员的userId数组
	private void filterFireUsers(List<TeSysDefRoleUser> userList) {
		List<ObjectId> userIds = userList.stream().map(TeSysDefRoleUser::getRoleUser).map(TeSysDefRoleUser2User::getUserId).collect(Collectors.toList());
		List<TeSysUser> hireUsers = sysUserDao.findUserByIdList(userIds);
        List<ObjectId> hireUserIds = hireUsers.stream().map(TeSysUser::getId).collect(Collectors.toList());
        Iterator<TeSysDefRoleUser> iterator = userList.iterator();
        while (iterator.hasNext()){
            TeSysDefRoleUser sysDefRoleUser = iterator.next();
            TeSysDefRoleUser2User roleUser = sysDefRoleUser.getRoleUser();
            if (roleUser==null||roleUser.getUserId()==null||!hireUserIds.contains(roleUser.getUserId())){
                iterator.remove();
            }
        }
    }

	/**
	 * 根据用户名模糊查询所有User对象 需要参数defId、roleUser.userName 可以给定参数roleId
	 */
	@Override
	public List<TeSysDefRoleUser2UserVo> queryUserNameFuzzyByDefId(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(teSysDefRoleUserVo, teSysDefRoleUser);
		return querySysUserDistinctColl(teSysDefRoleUser);
	}
	
	/**
	 * 根据用户名模糊查询所有User对象 需要参数defId、roleUser.userName 可以给定参数roleId,包涵sysAdmin和buAdmin
	 */
	@Override
	public List<TeSysDefRoleUser2UserVo> queryPrjUserAndAdminByDefId(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		TeSysDefRoleUser roleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(teSysDefRoleUserVo,roleUser);
		if (null != teSysDefRoleUserVo.getRole()){
			List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
			TeSysDefRoleUser2Role role = teSysDefRoleUserVo.getRole();
			roles.add(role);
			roleUser.setRole(roles);
		}
		//项目下的人员
		List<TeSysDefRoleUser2UserVo> prjUserList = querySysUserDistinctColl(roleUser);
		//系统管理员
		List<TeSysDefRoleUser> adminList = new ArrayList<TeSysDefRoleUser>();
		TeSysDefRoleUser2Role sysAdmin_role = new TeSysDefRoleUser2Role();
		sysAdmin_role.setRoleCodeName(SysDefConstants.SYSADMIN_CODENAME);
		List<TeSysDefRoleUser> sysAdminList = sysDefRoleUserDao.queryRoleUserByRoleAndDefId(sysAdmin_role, null);
		adminList.addAll(sysAdminList);
		//bu管理员
		SysDef buDef = sysDefService.getSysDefByCodeName(teSysDefRoleUserVo.getBuId(), SysDefTypeCodeName.AI_BU);
		TeSysDefRoleUser2Role buAdmin_role = new TeSysDefRoleUser2Role();
		buAdmin_role.setRoleCodeName(SysDefConstants.BUADMIN_CODENAME);
		List<TeSysDefRoleUser> buAdminList = sysDefRoleUserDao.queryRoleUserByRoleAndDefId(buAdmin_role, buDef.getId());
		adminList.addAll(buAdminList);
		List<TeSysDefRoleUser2UserVo> adminUserList = new ArrayList<TeSysDefRoleUser2UserVo>();
		for (TeSysDefRoleUser defRoleUser : adminList) {
			TeSysDefRoleUser2UserVo tmp = new TeSysDefRoleUser2UserVo();
			BeanUtils.copyProperties(defRoleUser.getRoleUser(), tmp);
			adminUserList.add(tmp);
		}
		prjUserList.addAll(adminUserList);
		
		//去重，系统管理员可能已经属于项目下的人员
		this.removeDuplicate(prjUserList);
		return prjUserList;
		
	}
	
	private List<TeSysDefRoleUser2UserVo> removeDuplicate(List<TeSysDefRoleUser2UserVo> list) {
		for (int i = 0; i < list.size() - 1; i++) {
			for (int j = list.size() - 1; j > i; j--) {
				if (list.get(j).getLoginName().equals(list.get(i).getLoginName())) {
					list.remove(j);
				}
			}
		}
		return list;
	}

	/**
	 * 同步资源人员
	 * @param empData
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public Map<String, String> syncResourceEmp(String empData, String prjId, String prjName, String prjCodeName, String casLoginUserName) {
		
		HashMap<String, String> resultMap = new HashMap<String, String>();
		
		if (StringUtil.isNull(empData) || StringUtil.isNull(prjId)) {
			resultMap.put("flag", "exception");
			resultMap.put("info", "request params empData or prjId is null.");
			return resultMap;
		}
		
		// 解析数据
		List<Map> empDataList = JsonUtil.toList(empData, Map.class);
		if (null==empDataList || empDataList.isEmpty()) {
			resultMap.put("flag", "exception");
			resultMap.put("info", "resolve empData to empDataList is null.");
			return resultMap;
		}
		
		// 获取项目根组
		SysDef rootDef = new SysDef();
		// SysDef rootDef = this.queryRootGroupBySrc(new ObjectId(prjId));
		// 构建[小组名称:小组人员SET]MAP
		Map<String, ObjectId> teamDefIdMap   = new HashMap<String, ObjectId>();
		Map<String, String> teamGroupRespMap = new HashMap<String, String>();
		Map<String, Set<String>> teamEmpMap  = this.queryTeamEmpMapInfo(new ObjectId(prjId), rootDef, teamDefIdMap, teamGroupRespMap);
		
		if (null==teamEmpMap || teamEmpMap.isEmpty()) {
			resultMap.put("flag", "exception");
			resultMap.put("info", "get prj teamEmpMap is null.");
			return resultMap;
		}
		if (null==rootDef 	 || null==rootDef.getId()) {
			resultMap.put("flag", "exception");
			resultMap.put("info", "project root group is null.");
			return resultMap;
		}
		
		// 比对小组成员
		Set<String> usTeamEmpNtSet 	= null;
		Set<String> teamEmpNtSet 	= null;
		Map<String, String> 	 groupRespNtMap = new HashMap<String, String>();
		Map<String, Set<String>> syncTeamEmpMap = new HashMap<String, Set<String>>();
		
		for (Map map : empDataList) {
			
			String teamName = StringUtil.getNotNullStr(map.get("teamname"));
			String empNt 	= StringUtil.getNotNullStr(map.get("empnt"));
			String roleId 	= StringUtil.getNotNullStr(map.get("role"));
			
			if (StringUtil.isNull(empNt)) {
				continue;
			}
			
			String syncTeamName = "";
			usTeamEmpNtSet  = teamEmpMap.get(teamName);
			
			// 确认同步成员所属小组
			if (null==usTeamEmpNtSet) {
				if (!teamEmpMap.get(rootDef.getDefName()).contains(empNt)) {
					syncTeamName = rootDef.getDefName();
				} else {
					continue;
				}
			} else if (!usTeamEmpNtSet.contains(empNt)){
				syncTeamName = teamName;
			} else {
				continue;
			}
			
			teamEmpNtSet = syncTeamEmpMap.get(syncTeamName);
			if (null==teamEmpNtSet || teamEmpNtSet.isEmpty()) {
				teamEmpNtSet = new HashSet<String>();
			}
			
			// 确认成员角色
			if (StringUtil.isNotNull(roleId) && Integer.valueOf(roleId)==PrjPlanConstants.PRJ_GROUPRESP_ROLEID) {
				groupRespNtMap.put(empNt, roleId);
			}
			
			// 确认需要的各小组与小组成员
			teamEmpNtSet.add(empNt);
			syncTeamEmpMap.put(syncTeamName, teamEmpNtSet);
		}
		
		// 查询用户信息
		Map<String, TeSysUser> sysUserMap = this.queryUsersMapByMultNt(syncTeamEmpMap, casLoginUserName);
		
		// 同步项目小组成员
		resultMap = this.syncPrjTeamEmp(prjId, prjName, prjCodeName, casLoginUserName, teamDefIdMap, syncTeamEmpMap, groupRespNtMap, sysUserMap, teamGroupRespMap);
		
		return resultMap;
	}
	
	// 同步项目小组成员
	private HashMap<String, String> syncPrjTeamEmp(String 					prjId, 
												   String 					prjName, 
												   String 					prjCodeName,
												   String 					casLoginUserName,
												   Map<String, ObjectId> 	teamDefIdMap, 
												   Map<String, Set<String>> syncTeamEmpMap, 
												   Map<String, String> 	 	groupRespNtMap,
												   Map<String, TeSysUser> 	sysUserMap,
												   Map<String, String> 		teamGroupRespMap
												   ) {
		
		HashMap<String, String> resultMap = new HashMap<String, String>();
		
		if (null==teamDefIdMap || teamDefIdMap.isEmpty() || null==syncTeamEmpMap || syncTeamEmpMap.isEmpty() || null==sysUserMap || sysUserMap.isEmpty()) {
			resultMap.put("flag", "exception");
			resultMap.put("info", "teamDefIdMap or syncTeamEmpMap or sysUserMap is null.");
		}
		
		Set<String> teamEmpNtSet = null;
		List<TeSysDefRoleUser> syncRoleUserList = new ArrayList<TeSysDefRoleUser>();
		
		TeSysDefRoleUser 			roleUser = null;
		TeSysDefRoleUser2DefType	 defType = null;
		TeSysDefRoleUser2SysDef		  srcDef = null;
		TeSysDefRoleUser2Role 			role = null;
		TeSysDefRoleUser2User 	roleUserInfo = null;
		TeSysDefRoleUser2User 		 addUser = null;
		
		for (String teamName : syncTeamEmpMap.keySet()) {
			
			teamEmpNtSet = syncTeamEmpMap.get(teamName);
			
			for (String empNt : teamEmpNtSet) {
				
				roleUser = new TeSysDefRoleUser();
				
				defType = new TeSysDefRoleUser2DefType();
				defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
				defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);
				defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
				roleUser.setDefType(defType);
				
				srcDef = new TeSysDefRoleUser2SysDef();
				srcDef.setSrcDefId(new ObjectId(prjId));
				srcDef.setSrcDefName(prjName);
				srcDef.setSrcDefCodeName(prjCodeName);
				roleUser.setSrcDef(srcDef);
				
				roleUser.setDefId(teamDefIdMap.get(teamName));
				
				if (StringUtil.isNotNull(groupRespNtMap.get(empNt)) && StringUtil.isNull(teamGroupRespMap.get(teamName))) {
					List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
					role = new TeSysDefRoleUser2Role();
					role.setRoleId(new ObjectId(SysDefConstants.GROUPRESP_DEF_ID));
					role.setRoleName(SysDefConstants.GROUPRESP_DEF_NAME);
					role.setRoleCodeName(SysDefConstants.GROUPRESP_CODENAME);
					roleUsers.add(role);
					roleUser.setRole(roleUsers);
				} else {
					List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
					role = new TeSysDefRoleUser2Role();
					role.setRoleId(new ObjectId(SysDefConstants.MEMBER_DEF_ID));
					role.setRoleName(SysDefConstants.MEMBER_DEF_NAME);
					role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
					roleUsers.add(role);
					roleUser.setRole(roleUsers);
				}
				
				roleUserInfo = new TeSysDefRoleUser2User();
				roleUserInfo.setUserId(sysUserMap.get(empNt).getId());
				roleUserInfo.setLoginName(sysUserMap.get(empNt).getLoginName());
				roleUserInfo.setUserName(sysUserMap.get(empNt).getUserName());
				roleUserInfo.setJobCode(sysUserMap.get(empNt).getJobCode());
				roleUser.setRoleUser(roleUserInfo);
				
				roleUser.setIsValid(true);
				roleUser.setAddTime(new Date());
				
				addUser = new TeSysDefRoleUser2User();
				addUser.setUserId(sysUserMap.get(casLoginUserName).getId());
				addUser.setLoginName(sysUserMap.get(casLoginUserName).getLoginName());
				addUser.setUserName(sysUserMap.get(casLoginUserName).getUserName());
				addUser.setJobCode(sysUserMap.get(casLoginUserName).getJobCode());
				roleUser.setAddUser(addUser);
				
				syncRoleUserList.add(roleUser);
			}
		}
		
		sysDefRoleUserDao.batchSave(syncRoleUserList);
		resultMap.put("flag", "success");
		return resultMap;
	}
	
	// 查询[人员NT:小组人员user]MAP
	private Map<String, TeSysUser> queryUsersMapByMultNt(Map<String, Set<String>> syncTeamEmpMap, String casLoginUserName) {
		
		Map<String, TeSysUser> sysUserMap = new HashMap<String, TeSysUser>();
		
		if (null==syncTeamEmpMap || syncTeamEmpMap.isEmpty()) {
			return sysUserMap;
		}
		
		Set<String> userNtSet = new HashSet<String>();
		userNtSet.add(casLoginUserName);
		for (String teamName : syncTeamEmpMap.keySet()) {
			userNtSet.addAll(syncTeamEmpMap.get(teamName));
		}
		
		sysUserMap = sysUserService.queryUsersMapByMultNt(userNtSet);
		
		return sysUserMap;
	}
	
	// 查询[小组名称:小组人员SET]MAP
	private Map<String, Set<String>> queryTeamEmpMapInfo(ObjectId prjId, SysDef rootDef, Map<String, ObjectId> teamDefIdMap, Map<String, String> teamGroupRespMap) {
		
		Map<String, Set<String>> teamEmpMap = new HashMap<String, Set<String>>();
		
		// 查询项目小组
		List<SysDef> sysDefsList = sysDefService.getSysDefsBySrc(prjId, SysDefTypeCodeName.PRJ_GROUP);
		if (null == sysDefsList || sysDefsList.isEmpty()) {
			return teamEmpMap;
		}
		
		// 获取根组
		//rootDef = sysDefsList.get(0);
		rootDef.setId(sysDefsList.get(0).getId());
		rootDef.setDefName(sysDefsList.get(0).getDefName());
		
		// 构建小组MAP
		Map<String, String> teamDefNameMap = new HashMap<String, String>();
		List<ObjectId> ids = new ArrayList<ObjectId>();
		for(SysDef def : sysDefsList){
			ids.add(def.getId());
			teamDefNameMap.put(def.getId().toHexString(), def.getDefName());
			teamDefIdMap.put(def.getDefName(), def.getId());
			teamEmpMap.put(def.getDefName(), new HashSet<String>());
		}
		
		// 查询项目小组人员
		List<TeSysDefRoleUser> roleUserList = sysDefRoleUserDao.queryGroupRespByGroup(SysDefTypeCodeName.PRJ_GROUP.getValue(), ids, null);
		if (null == roleUserList || roleUserList.isEmpty()) {
			return teamEmpMap;
		}
		
		// 构建[小组名称:小组人员SET]MAP
		Set<String> empNtSet = null;
		for(TeSysDefRoleUser roleUser : roleUserList){
			List<TeSysDefRoleUser2Role> role = roleUser.getRole();
			for (TeSysDefRoleUser2Role teSysDefRoleUser2Role : role) {
				if (SysDefConstants.GROUPRESP_CODENAME.equals(teSysDefRoleUser2Role.getRoleCodeName())) {
					teamGroupRespMap.put(teamDefNameMap.get(roleUser.getDefId().toHexString()), roleUser.getRoleUser().getLoginName());
				}
			}


			empNtSet = teamEmpMap.get(teamDefNameMap.get(roleUser.getDefId().toHexString()));
			if (null==empNtSet || empNtSet.isEmpty()) {
				empNtSet = new HashSet<String>();
			}
			empNtSet.add(roleUser.getRoleUser().getLoginName());
			teamEmpMap.put(teamDefNameMap.get(roleUser.getDefId().toHexString()), empNtSet);
		}
		
		return teamEmpMap;
	}
	
	// 查询项目根组
	@SuppressWarnings("unused")
	private SysDef queryRootGroupBySrc(ObjectId prjId) {
		
		List<SysDefQuerySortKey> sortKeys = new ArrayList<SysDefQuerySortKey>();
		SysDefQuerySortKey key = new  SysDefQuerySortKey(SysDefQuerySortKey.Key.NAME, true);
		sortKeys.add(key);
		List<SysDef> sysDefList = sysDefService.getSysDefTreeRootsBySrc(prjId, SysDefTypeCodeName.PRJ_GROUP, sortKeys);
		
		if (null==sysDefList || sysDefList.isEmpty()) {
			return null;
		}
		return sysDefList.get(0);
	} 
	
	@SuppressWarnings("unused")
	@Override
	public ObjectId createRoleUserForKnowledgeDomain(TeSysDefRoleUser teSysDefRoleUser) {
		//查询当前知识域下是否存在该成员、重复判断
		List<TeSysDefRoleUser> findByDefIdAndUser = sysDefRoleUserDao.findByDefIdAndUser(teSysDefRoleUser);
		//if(findByDefIdAndUser!=null && !findByDefIdAndUser.isEmpty()){
		// new BaseException(teSysDefRoleUser.getRoleUser().getUserName()+"已经在当前知识域中！");
		//}
		TeSysDefRoleUser save = sysDefRoleUserDao.save(teSysDefRoleUser);
		return save.getId();
	}

	@Override
	public void removeTeSysDefRoleUserByDomain(TeSysDefRoleUserVo teSysDefRoleUserVo,TeSysUser loginUser) {
		if(StringUtil.isNotNull(teSysDefRoleUserVo.getRoleUser())){
			TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
			TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
			defType.setDefTypeCodeName(SysDefTypeConstants.KMDOMAIN_CODENAME);
			teSysDefRoleUser.setDefType(defType);
			teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
			List<TeSysDefRoleUser2Role> role = new ArrayList<>();
			role.add(teSysDefRoleUserVo.getRole());
			teSysDefRoleUser.setRole(role);
			TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
			roleUser.setUserId(teSysDefRoleUserVo.getRoleUser().getUserId());
			roleUser.setLoginName(teSysDefRoleUserVo.getRoleUser().getLoginName());
			roleUser.setUserName(teSysDefRoleUserVo.getRoleUser().getUserName());
			roleUser.setJobCode(teSysDefRoleUserVo.getRoleUser().getJobCode());
			teSysDefRoleUser.setRoleUser(roleUser);
			
			List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);

			TeUser delUser = loginUser.trans2User();
			Date delTime = new Date();
			for (TeSysDefRoleUser t : teSysDefRoleUsers) {
			t.setIsValid(false);
			t.setDelUser(delUser);
			t.setDelTime(delTime);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		    }
		}
		
	}
	
	@Override
	public void removeTeSysDefRoleUserByGroup(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		if(StringUtil.isNotNull(teSysDefRoleUserVo.getRoleUser())){
			TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
			TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
			defType.setDefTypeCodeName(SysDefTypeConstants.FILEBASEGROUP_CODENAME);
			teSysDefRoleUser.setDefType(defType);
			teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
			TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
			roleUser.setUserId(teSysDefRoleUserVo.getRoleUser().getUserId());
			roleUser.setLoginName(teSysDefRoleUserVo.getRoleUser().getLoginName());
			roleUser.setUserName(teSysDefRoleUserVo.getRoleUser().getUserName());
			roleUser.setJobCode(teSysDefRoleUserVo.getRoleUser().getJobCode());
			teSysDefRoleUser.setRoleUser(roleUser);
			
			List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
		
			for (TeSysDefRoleUser t : teSysDefRoleUsers) {
			t.setIsValid(false);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		    }
		}
		
	}
	
	@Override
	public void removeTeSysDefRoleUserByFolder(TeSysDefRoleUserVo teSysDefRoleUserVo) {

		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		
		//roleUser
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysDefRoleUserVo.getRoleUser().getUserId());
		roleUser.setLoginName(teSysDefRoleUserVo.getRoleUser().getLoginName());
		roleUser.setUserName(teSysDefRoleUserVo.getRoleUser().getUserName());
		roleUser.setJobCode(teSysDefRoleUserVo.getRoleUser().getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		//role
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
		
		//defId文件夹id
		teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
		
		List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
	
		for (TeSysDefRoleUser t : teSysDefRoleUsers) {
		t.setIsValid(false);
		sysDefRoleUserDao.updateByObjectId(t.getId(), t);
	    }
	}

	@Override
	public void removeWealthHouseTeSysDefRoleUserByFolder(TeSysDefRoleUserVo teSysDefRoleUserVo){
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		//roleUser
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysDefRoleUserVo.getRoleUser().getUserId());
		roleUser.setLoginName(teSysDefRoleUserVo.getRoleUser().getLoginName());
		roleUser.setUserName(teSysDefRoleUserVo.getRoleUser().getUserName());
		roleUser.setJobCode(teSysDefRoleUserVo.getRoleUser().getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		//defId文件夹id
		teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
		List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
	    List<ObjectId> ids = new ArrayList<ObjectId>();
		for (TeSysDefRoleUser t : teSysDefRoleUsers) {
			ids.add(t.getId());
	    }
		List<IDbCondition> conds = new ArrayList<IDbCondition>();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_I<ObjectId>(DFN.common__id,ids));
		List<UpdataData> updateDatas = new ArrayList<UpdataData>();
		UpdataData updataData = new UpdataData(DFN.common_isValid, false);
		updateDatas.add(updataData);
		sysDefRoleUserDao.updateByConds(conds, updateDatas);
		
	}
	
	@Override
	public List<TeSysDefRoleUser> queryRoleUserByUserIdAndType(TeSysDefRoleUser2DefType defType, TeSysUser teSysUser) {
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.queryRoleUserByUserIdAndType(defType, teSysUser);
		return list;
	}

	@Override
	public List<TeSysDefRoleUser> queryRoleUserBySrcAndType(TeSysDefRoleUser2DefType defType,List<ObjectId> idList) {
		List<TeSysDefRoleUser> list= sysDefRoleUserDao.queryRoleUserBySrcAndType(defType, idList);
		return list;
	}

	@Override
	public List<TeSysDefRoleUser> queryBuPrjAdmin(TeSysUserVo teSysUserVo) {
		
		//定义BU
		SysDef defBu = sysDefService.getSysDefByCodeName(teSysUserVo.getSbuId(),SysDefTypeCodeName.AI_BU);
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		teSysDefRoleUser.setDefId(defBu.getId());

		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.BUPRJADMIN_DEF_ID));
		role.setRoleCodeName(SysDefConstants.BUPRJADMIN_CODENAME);
		role.setRoleName(SysDefConstants.BUPRJADMIN_DEF_NAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUserVo.getId());
		roleUser.setLoginName(teSysUserVo.getLoginName());
		roleUser.setUserName(teSysUserVo.getUserName());
		roleUser.setJobCode(teSysUserVo.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		teSysDefRoleUser.setIsValid(true);
		
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);
		
		return list;
	}
	
	@Override
	public List<TeSysDefRoleUser> queryBuAdmin(TeSysUserVo teSysUserVo) {
		//定义BU
		SysDef defBu = sysDefService.getSysDefByCodeName(teSysUserVo.getSbuId(), SysDefTypeCodeName.AI_BU);
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		teSysDefRoleUser.setDefId(defBu.getId());

		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.BUADMIN_DEF_ID));
		role.setRoleCodeName(SysDefConstants.BUADMIN_CODENAME);
		role.setRoleName(SysDefConstants.BUADMIN_DEF_NAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUserVo.getId());
		roleUser.setLoginName(teSysUserVo.getLoginName());
		roleUser.setUserName(teSysUserVo.getUserName());
		roleUser.setJobCode(teSysUserVo.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		teSysDefRoleUser.setIsValid(true);
		
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);
		
		return list;
	}
	
	@Override
	public List<TeSysDefRoleUser> querySysAdmin(TeSysUserVo teSysUserVo) {
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		
		//sysAdmin
		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.SYSADMIN_DEF_ID));
		role.setRoleCodeName(SysDefConstants.SYSADMIN_CODENAME);
		role.setRoleName(SysDefConstants.SYSADMIN_DEF_NAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUserVo.getId());
		roleUser.setLoginName(teSysUserVo.getLoginName());
		roleUser.setUserName(teSysUserVo.getUserName());
		roleUser.setJobCode(teSysUserVo.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);
		
		teSysDefRoleUser.setIsValid(true);
		
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);
		
		return list;
	}
	
	@Override
	public List<TeSysDefRoleUser> queryAdmin(TeSysUser teSysUser,ObjectId rootFolderId){
		List<ObjectId> idList = new ArrayList<>();
		idList.add(rootFolderId);
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
		role.setRoleName(SysDefConstants.ADMIN_DEF_NAME);
		role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);

		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUser.getId());
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.queryAdminBydefIdsAndRoleUser(idList, role, roleUser);
		return list;
	}

	@Override
	public List<TeSysDefRoleUser> queryAdminBydefIdsAndRoleUser(List<ObjectId> idList, TeSysUser teSysUser) {

		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
		
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(teSysUser.getId());
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.queryAdminBydefIdsAndRoleUser(idList, role, roleUser);
		return list;
	}

	@Override
	public List<TeSysDefRoleUser2UserVo> queryTeSysRoleUserVoByBase(SysDef kmBase) {
		List<TeSysDefRoleUser2UserVo> resulut = new ArrayList<TeSysDefRoleUser2UserVo>();
		//知识库下所有的知识域
		List<ObjectId> domainIds = new ArrayList<ObjectId>();
		List<SysDef> domainList = sysDefService.getSysDefsBySrc(kmBase.getId(), SysDefTypeCodeName.KM_DOMAIN);
		for(SysDef sysDef : domainList){
			domainIds.add(sysDef.getId());
		}
		//知识库下的所有人员
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		List<TeSysDefRoleUser> teSysDefRoleUserList = sysDefRoleUserDao.findByTeSysDefRoleUserAndDefIds(teSysDefRoleUser, domainIds);
		List<String> userIds = new ArrayList<String>();
		
		for(TeSysDefRoleUser tmp : teSysDefRoleUserList){
			TeSysDefRoleUser2UserVo tmpDefRoleUser2UserVo = new TeSysDefRoleUser2UserVo();
			TeSysDefRoleUser2User roleUser = tmp.getRoleUser();
			BeanUtils.copyProperties(roleUser, tmpDefRoleUser2UserVo);
			if(!userIds.contains(roleUser.getUserId().toHexString())){
				resulut.add(tmpDefRoleUser2UserVo);
				userIds.add(roleUser.getUserId().toHexString());
			}
		}
		return resulut;
	}
	
	@Override
	public List<TeSysDefRoleUser> queryIsGroupRespOfGroupInfo(List<ObjectId> groupIdList, String groupRespCodeName, ObjectId loginUserId) {
		
		return sysDefRoleUserDao.queryIsGroupRespOfGroupInfo(groupIdList, groupRespCodeName, loginUserId);
	}

	@Override
	public void addSysRoleUserAdmin(ObjectId defId,List<String>roleUserIdList,TeSysUser loginUser) {
		List<TeSysDefRoleUser> teSysDefRoleUsers = new ArrayList<TeSysDefRoleUser>();
		if(roleUserIdList != null){
			List<TeSysUser> roleUserList = sysUserDao.findUserByIdValueList(roleUserIdList);
			for (TeSysUser teSysUser : roleUserList) {
				List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
				TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
				TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
				role.setRoleId(new ObjectId(SysDefConstants.ADMIN_DEF_ID));
				role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
				role.setRoleName(SysDefConstants.ADMIN_DEF_NAME);
				roleUsers.add(role);
				teSysDefRoleUser.setRole(roleUsers);
				teSysDefRoleUser.setDefId(defId);
				teSysDefRoleUser.setIsValid(true);
				teSysDefRoleUser.setAddTime(new Date());
				TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
				addUser.setUserId(loginUser.getId());
				addUser.setLoginName(loginUser.getLoginName());
				addUser.setUserName(loginUser.getUserName());
				addUser.setJobCode(loginUser.getJobCode());
				teSysDefRoleUser.setAddUser(addUser);
				TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
				roleUser.setUserId(teSysUser.getId());
				roleUser.setUserName(teSysUser.getUserName());
				roleUser.setLoginName(teSysUser.getLoginName());
				roleUser.setJobCode(teSysUser.getJobCode());
				teSysDefRoleUser.setRoleUser(roleUser);
				teSysDefRoleUsers.add(teSysDefRoleUser);
			}
			this.addTeSysDefRoleUserList(teSysDefRoleUsers);
		}
	}

	@Override
	public void removeSysRoleUserAdmin(ObjectId defId,List<String> roleUserIdList) {
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		//defId文件夹id
		teSysDefRoleUser.setDefId(defId);
		//role
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleCodeName(SysDefConstants.ADMIN_CODENAME);
		List<TeSysUser> roleUserList = sysUserDao.findUserByIdValueList(roleUserIdList);
		//roleUser
		for (TeSysUser teSysUser : roleUserList) {
			TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
			roleUser.setUserId(teSysUser.getId());
			roleUser.setLoginName(teSysUser.getLoginName());
			teSysDefRoleUser.setRoleUser(roleUser);
			List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
			for (TeSysDefRoleUser t : teSysDefRoleUsers) {
			t.setIsValid(false);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		    }
		}
	}

	@Override
	public String importSysRoleUser(MultipartFile file, TeSysDefRoleUser teSysDefRoleUser, TeSysUser loginUser) {
	  
      String result = "";

      List<TeSysUserVo> sysUserAddList = getExcelUserData(file);
      //判断导入的数据中是否有重复数据
	  boolean isRepeat = false;
	  String repeatLoginName = "";
	  List<String> loginNameList = new ArrayList<>();

	  for (TeSysUserVo user:sysUserAddList){
		if(loginNameList.contains(user.getLoginName())){
			isRepeat = true;
			repeatLoginName += user.getLoginName()+",";
		}else{
			loginNameList.add(user.getLoginName());
		}
	  }
	  if(isRepeat){
	  	return "导入模板中人员"+repeatLoginName.substring(0,repeatLoginName.length() - 1)+"存在重复数据，请修改后再操作";
	  }
	  //完善数据
      result = completionUserData(teSysDefRoleUser, sysUserAddList);
      if(StringUtil.isNotNull(result)){
		  return result;
	  }
      try {
        analysisSysUserList(teSysDefRoleUser, sysUserAddList);
      } catch (Exception e) {
        return e.getMessage();
      }

      TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
      defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
      defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
      defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);

      TeSysDefRoleUser2User addUser=new TeSysDefRoleUser2User();
      addUser.setJobCode(loginUser.getJobCode());
      addUser.setLoginName(loginUser.getLoginName());
      addUser.setUserId(loginUser.getId());
      addUser.setUserName(loginUser.getUserName());

      teSysDefRoleUser.setAddUser(addUser);
      teSysDefRoleUser.setDefType(defType);
      teSysDefRoleUser.setAddTime(new Date());
      teSysDefRoleUser.setIsValid(true);

		// 添加小组用户
		List<TeSysDefRoleUser> sysDefRoleUserAdds = getSysDefRoleUser(sysUserAddList, teSysDefRoleUser, loginUser);
		for (TeSysDefRoleUser sysDefRoleUserAdd : sysDefRoleUserAdds) {
		  try {
			  addTeSysDefRoleUserForPrjGroup4StructureAndStaff(sysDefRoleUserAdd);
		  } catch (Exception e) {
			result += e.getMessage() + ";";
		  }
		}

      return result;
    }

    @Override
    public void updateSysRoleUser(TeSysDefRoleUserVo sysDefRoleUserVo) {
      if("roleCode".equals(sysDefRoleUserVo.getUpdateFlag())) {
        TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
        srcDef.setSrcDefId(sysDefRoleUserVo.getSrcDef().getSrcDefId());
        ObjectId defId = StringUtil.toObjectId(sysDefRoleUserVo.getDefIdValue());
        TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
        roleUser.setLoginName(sysDefRoleUserVo.getRoleUser().getLoginName());
        
        TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
        sysDefRoleUser.setSrcDef(srcDef);
        sysDefRoleUser.setDefId(defId);
        sysDefRoleUser.setRoleUser(roleUser);

		  if (null != sysDefRoleUserVo.getRoleIds() && !sysDefRoleUserVo.getRoleIds().isEmpty()){
			  List<TeSysDef> defsByIds = sysDefService.getTeSysDefsByIds(sysDefRoleUserVo.getRoleIds());
			  List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
			  for (TeSysDef roleDef : defsByIds) {
				  TeSysDefRoleUser2Role role2Role = new TeSysDefRoleUser2Role();
				  role2Role.setRoleCodeName(roleDef.getCodeName());
				  role2Role.setRoleId(roleDef.getId());
				  role2Role.setRoleName(roleDef.getDefName());
				  roles.add(role2Role);
			  }
			  sysDefRoleUser.setRole(roles);
			  updateSysDefRoleUser(sysDefRoleUser);
		  }
      } else {
        TeIdNameCn srcDef = new TeIdNameCn();
        srcDef.setCid(sysDefRoleUserVo.getSrcDef().getSrcDefId());
        TeIdNameCn def = new TeIdNameCn();
        def.setCid(StringUtil.toObjectId(sysDefRoleUserVo.getDefIdValue()));
        TeUser defUser = new TeUser();
        defUser.setLoginName(sysDefRoleUserVo.getRoleUser().getLoginName());
        
        TeSysDefUserInfo sysDefUserInfo = new TeSysDefUserInfo();
        sysDefUserInfo.setSrcDef(srcDef);
        sysDefUserInfo.setDef(def);
        sysDefUserInfo.setDefUser(defUser);
        
        sysDefUserInfo.setStartDate(com.linkus.base.util.DateUtil.parseDate(sysDefRoleUserVo.getStartDate(), 
            com.linkus.base.util.DateUtil.DATE_FORMAT));
        sysDefUserInfo.setEndDate(com.linkus.base.util.DateUtil.parseDate(sysDefRoleUserVo.getEndDate(), 
            com.linkus.base.util.DateUtil.DATE_FORMAT));
        sysDefUserInfo.setDesc(sysDefRoleUserVo.getSysDefUserInfoDesc());
        updateSysDefUserInfo(sysDefUserInfo);
      }
    }

	/**
	 *
	 * @param sysDefRoleUser
	 * @param sysUserAddList //这个是待加入到项目中的用户，但是这些用户的添加时间还没有进行校验
	 */
	private void analysisSysUserList(TeSysDefRoleUser sysDefRoleUser,List<TeSysUserVo> sysUserAddList) {
	    
  	    if(sysDefRoleUser == null || sysDefRoleUser.getSrcDef() == null 
  	        || sysDefRoleUser.getSrcDef().getSrcDefId() == null) {
  	        throw new BaseException("所属项目为空");
  	    }
  	    
  	    List<IDbCondition> conds = new ArrayList<IDbCondition>();

  	    Set<String> existUserLoginNameList = new HashSet<>();
  	    
  	    conds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), 
  	        sysDefRoleUser.getSrcDef().getSrcDefId()));
        conds.add(new DbEqualCondition(DFN.common_isValid, true));
	    List<TeSysDefRoleUser> eixstedUser = sysDefRoleUserDao.findByConds(conds, null);
	    for (TeSysDefRoleUser item : eixstedUser) {
	      if(item.getDefId() == null) continue;
	      existUserLoginNameList.add(item.getRoleUser().getLoginName());
	      existUserLoginNameList.add(item.getRoleUser().getJobCode());
        }


	    //用户自己填的NT账号，这个集合中的数据并不清楚是否已经注册过
	    List<String> regLoginNames = new ArrayList<String>();
		// excel导入数据中，已注册用户的的NT账号，这个数据是从regLoginNames筛选过的，已经注册的数据
		List<String> allRegUsers = new ArrayList<String>();
        for (TeSysUserVo sysUser : sysUserAddList) {
          if(StringUtil.isNotNull(sysUser.getLoginName())) {
            regLoginNames.add(sysUser.getLoginName());
          }
        }
		conds.clear();
        List<IDbCondition> orConds = new ArrayList<>();
		orConds.add(new DbInCondition<String>(DFN.sysUser__loginName, regLoginNames));
		orConds.add(new DC_I<String>(DFN.sysUser_jobCode,regLoginNames));
		conds.add(new DC_OR(orConds));
		conds.add(new DC_E(DFN.common_isValid,true));
        List<TeSysUser> regSysUsers = sysUserDao.findByConds(conds, null);
        for (TeSysUser user : regSysUsers) {
          allRegUsers.add(user.getLoginName());
          allRegUsers.add(user.getJobCode());
        }
		//获取所有人的LoginName,并查询sysDefRoleUser表找到对应数据
		List<String> toAddUserLoginNameList = new ArrayList<>();
        for (TeSysUserVo sysUser : sysUserAddList) {
            // 1.2 未添加到小组的 判断用户是否存在
            if(!allRegUsers.contains(sysUser.getLoginName())) {
				throw new BaseException("NT账号/工号（" + sysUser.getLoginName() + "）不存在或填写错误，请重新填写！");
            }
            if(existUserLoginNameList.contains(sysUser.getLoginName())){
				throw new BaseException("NT账号/工号（" + sysUser.getLoginName() + "）已经是在项人员，请重新填写！");
			}
			toAddUserLoginNameList.add(sysUser.getLoginName());
        }

        //校验待加入的用户，是否满足加入时间大于最大退出时间
//		stockExpireUserInfoMap:<K:用户LoginName，V:最晚离项时间>
		Map<String,Date> stockExpireUserInfoMap = new HashMap<>();

		conds.clear();
		orConds.clear();
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),
				sysDefRoleUser.getSrcDef().getSrcDefId()));
		conds.add(new DbEqualCondition(DFN.common_isValid, false));
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser_quitTime, null,true));
		conds.add(new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeConstants.PRJGROUP_CODENAME));
		orConds.add(new DbInCondition<String>(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName),toAddUserLoginNameList));
		orConds.add(new DbInCondition<String>(DFN.sysDefRoleUser__roleUser.dot(DFN.common_jobCode),toAddUserLoginNameList));
		conds.add(new DC_OR(orConds));
		List<TeSysDefRoleUser> stockExpiredUserInfo = sysDefRoleUserDao.findByConds(conds, null);
		for(TeSysDefRoleUser stockUser:stockExpiredUserInfo){
			String loginName = stockUser.getRoleUser().getLoginName();
			String jobCode = stockUser.getRoleUser().getJobCode();
			Date quitTime = stockUser.getQuitTime();
			if(stockExpireUserInfoMap.get(loginName) != null){
				Date stockQuitTime = stockExpireUserInfoMap.get(loginName);
					if(com.linkus.base.util.DateUtil.compareDate(quitTime,stockQuitTime)>0){
						stockExpireUserInfoMap.put(loginName,quitTime);
					}
			}else{
				stockExpireUserInfoMap.put(loginName,quitTime);
			}
			if(stockExpireUserInfoMap.get(jobCode) != null){
				Date stockQuitTime = stockExpireUserInfoMap.get(jobCode);
				if(com.linkus.base.util.DateUtil.compareDate(quitTime,stockQuitTime)>0){
					stockExpireUserInfoMap.put(jobCode,quitTime);
				}
			}else{
				stockExpireUserInfoMap.put(jobCode,quitTime);
			}
		}
		//校验
		String errorUserList = new String();//导入数据中开始时间填错的人员名单
		for(TeSysUserVo toAddUser:sysUserAddList){
			if(stockExpireUserInfoMap.get(toAddUser.getLoginName()) != null){
				Date maxQuitTime = stockExpireUserInfoMap.get(toAddUser.getLoginName());
				if(maxQuitTime != null){
					String maxQuitTimeStr = com.linkus.base.util.DateUtil.formatDate2Str(maxQuitTime, com.linkus.base.util.DateUtil.DATE_FORMAT);
					if(maxQuitTimeStr.compareTo(toAddUser.getStartDate())>0){//说明导入数据的时间小于最大退出时间
						errorUserList += toAddUser.getLoginName()+",";
					}
				}
			}
		}
		if(!errorUserList.isEmpty()){
			errorUserList = errorUserList.substring(0,errorUserList.length() - 1);
			throw new BaseException(errorUserList +
					"已存在该时间的记录，请确认！");
		}

    }
	
	private List<TeSysDefRoleUser> getSysDefRoleUser(List<TeSysUserVo> sysUserList, TeSysDefRoleUser sysDefRoleUser, TeSysUser loginUser) {

      List<ObjectId> roleIds = new ArrayList<ObjectId>();
      Set<String> loginNames = new HashSet<String>();
	  for (TeSysUserVo sysUser : sysUserList) {
		  List<String> roleCodes = sysUser.getRoleCodes();
		  for (String roleCode : roleCodes) {
			  if(!roleIds.contains(StringUtil.toObjectId(roleCode))) {
				  roleIds.add(StringUtil.toObjectId(roleCode));
		  }
        }
        if(StringUtil.isNull(sysUser.getLoginName())) {
          throw new BaseException("姓名未定义");
        }
        loginNames.add(sysUser.getLoginName());
      }

      List<SysDef> roleDefs = sysDefService.getSysDefsByIds(roleIds);

	  List<String> loginNameJobCodeList = new ArrayList<>();
	  loginNameJobCodeList.addAll(loginNames);
	  List<IDbCondition> conds = new ArrayList<>();
	  List<IDbCondition> orConds = new ArrayList<>();
	  conds.add(new DC_E(DFN.common_isValid,true));
	  orConds.add(new DC_I<String>(DFN.sysUser_jobCode,loginNameJobCodeList));
	  orConds.add(new DC_I<String>(DFN.sysUser__loginName,loginNameJobCodeList));
	  conds.add(new DC_OR(orConds));
	  List<DbFieldName> fieldNameList = new ArrayList<>();
	  fieldNameList.add(DFN.sysUser__userName);
	  fieldNameList.add(DFN.sysUser__loginName);
	  fieldNameList.add(DFN.sysUser_jobCode);
	  fieldNameList.add(DFN.common__id);
	  List<TeSysUser> sysUsers = sysUserDao.findByFieldAndConds(conds, fieldNameList);


		//List<TeSysUser> sysUsers = sysUserService.queryUsersByMultNt(loginNames);
      
      Map<ObjectId, SysDef> roleDefMap = new HashMap<ObjectId, SysDef>();
      Map<String, TeSysUser> sysUserMap = new HashMap<String, TeSysUser>();
      for (SysDef roleDef : roleDefs) {
        roleDefMap.put(roleDef.getId(), roleDef);
      }
      for (TeSysUser sysUser : sysUsers) {
        sysUserMap.put(sysUser.getLoginName(), sysUser);
        sysUserMap.put(sysUser.getJobCode(),sysUser);
      }
	  
	  List<TeSysDefRoleUser> sysDefRoleUserlist = new ArrayList<TeSysDefRoleUser>();
      for(TeSysUserVo sysUser : sysUserList){
          TeSysDefRoleUser roleUser = new TeSysDefRoleUser();
          BeanUtils.copyProperties(sysDefRoleUser, roleUser);
          
          TeSysUser user = sysUserMap.get(sysUser.getLoginName());
          if(user == null) continue;
          TeSysDefRoleUser2User role2User = new TeSysDefRoleUser2User();
          role2User.setJobCode(user.getJobCode());
          role2User.setLoginName(user.getLoginName());
          role2User.setUserId(user.getId());
          role2User.setUserName(user.getUserName());
          roleUser.setRoleUser(role2User);
          
          roleUser.setDefId(StringUtil.toObjectId(sysUser.getGroupIdValue()));

		  List<String> roleCodes = sysUser.getRoleCodes();
		  List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
		  for (String roleCode : roleCodes) {
			  SysDef sysDef = roleDefMap.get(StringUtil.toObjectId(roleCode));
			  TeSysDefRoleUser2Role role2Role = new TeSysDefRoleUser2Role();
			  role2Role.setRoleCodeName(sysDef.getCodeName());
			  role2Role.setRoleId(sysDef.getId());
			  role2Role.setRoleName(sysDef.getDefName());
			  roles.add(role2Role);
		  }
		  roleUser.setRole(roles);
		  roleUser.setEnterTime(com.linkus.base.util.DateUtil.parseDate(sysUser.getStartDate(),com.linkus.base.util.DateUtil.DATE_FORMAT));
		  roleUser.setDefPara(sysUser.getEmpWorkTypeCn());
		  sysDefRoleUserlist.add(roleUser);
      }
      return sysDefRoleUserlist;
    }
	
//	public void notFindSysUserByLoginName(List<TeSysUser> teSysUserloginNameLists,List<String> loginNameLists){
//		for(int i=0;i<teSysUserloginNameLists.size();i++){
//			for(int j=0;j<loginNameLists.size();j++){
//				if(teSysUserloginNameLists.get(i).getLoginName().equals(loginNameLists.get(j))){
//					loginNameLists.remove(loginNameLists.get(j));
//				}
//			}
//		}
//	}
//	//导入信息与数据库信息取差值
//	public List<TeSysUser> neddImportSysuser(TeSysDefRoleUser teSysDefRoleUser,List<TeSysUser> teSysUserloginNameList,List<TeSysUser> existSysUserLists){
//		//当前项目小组人员
//		List<TeSysUserVo> teSysUserVoList = sysUserService.queryTeSysUserVoByGroup(teSysDefRoleUser);
//		List<TeSysUser> teSysUserList=new ArrayList<TeSysUser>();
//		for(TeSysUserVo tem:teSysUserVoList){
//			TeSysUser teSysUser = new TeSysUser();
//			BeanUtils.copyProperties(tem,teSysUser);
//			teSysUserList.add(teSysUser);
//		}
//		//用户导入数据去重
//		Set<TeSysUser> set=new HashSet<TeSysUser>(teSysUserloginNameList);
//		teSysUserloginNameList=new ArrayList<>(set);
//		//用户与数据库中数据去重
//		for(int i=0;i<teSysUserList.size();i++){
//			for(int j=0;j<teSysUserloginNameList.size();j++){
//				if(teSysUserloginNameList.get(j).getId().equals(teSysUserList.get(i).getId())){
//					existSysUserLists.add(teSysUserloginNameList.get(j));
//					teSysUserloginNameList.remove(teSysUserloginNameList.get(j));
//				}
//			}
//		}
//		return teSysUserloginNameList;
//	}
//	 public void redExcelContent(MultipartFile file,List<String> loginNameLists){
//		 	String estimateValue="NT账号";
//			InputStream in = null;
//			Workbook wb = null;
//			Sheet sheet = null;
//			Row dataRow = null;
//			String cellData="";
//			String fileName=file.getOriginalFilename();
//			int empNo=1;
//			try {
//				in=file.getInputStream();
//				wb = this.estimateVersion(fileName,in,wb);
//				sheet=wb.getSheetAt(0);
//				int rownum=sheet.getPhysicalNumberOfRows();
//				dataRow=sheet.getRow(0);
//				int columnum=dataRow.getPhysicalNumberOfCells();
//				for(int i=0;i<columnum;i++){
//					cellData=(String)getCellFormatValue(dataRow.getCell(i));
//					if(cellData.equals(estimateValue)){
//						empNo=i;
//					}
//				}
//				for(int i=0;i<rownum;i++){
//					dataRow=sheet.getRow(i);
//					cellData=(String)getCellFormatValue(dataRow.getCell(empNo));
//					loginNameLists.add(cellData);
//				}
//			} catch (IOException e) {
//				e.printStackTrace();
//			} catch (EncryptedDocumentException e) {
//				e.printStackTrace();
//			}
//	 }
	 
	private List<TeSysUserVo> getExcelUserData(MultipartFile file){
	    List<TeSysUserVo> sysUserList = new ArrayList<TeSysUserVo>();
  	    InputStream in = null;
        Workbook wb = null;
        Sheet sheet = null;
        Row dataRow = null;
        if(file.getSize() / 1024 > SysDefConstants.IMPORT_FILE_SIZ) {
          throw new BaseException("文件大小不能超过2M");
        }
  
        try {
            in = file.getInputStream();
            wb = WorkbookFactory.create(in);
            sheet = wb.getSheetAt(0);
        } catch (IOException excelIOError) {
            throw new BaseException(" reading the excel file IO error ! ");
        } catch (InvalidFormatException excelChangeError) {
            throw new BaseException(" excel file conversion error ! ");
        } finally {
            try {
                in.close();
            } catch (IOException e) {
                throw new BaseException(" close file IO error ! ");
            }
        }

        int rowNum = sheet.getPhysicalNumberOfRows();
        if(rowNum == 0) {
            throw new BaseException("导入excel为空");
        }
        Row firstRow = sheet.getRow(1);
        int colNum = firstRow.getPhysicalNumberOfCells();
        if(colNum == 0) {
            throw new BaseException("导入excel为空");
        }
        if (PrjPlanConstants.ROWNUM <= rowNum || PrjPlanConstants.COLNUM <= colNum) {
            throw new BaseException(PrjPlanConstants.IMPORTFILE_FORMAT_ERROR);
        }
        
        String title;
        Map<String, Integer> fildCol = new HashMap<String, Integer>();
        for(int i=0; i < colNum; i++){
            title = ExcelUtils.getCellFormatValue(firstRow.getCell(i)).trim();
            fildCol.put(title, i);
        }
        
        for (int i = 2; i < rowNum; i++) {
            dataRow = sheet.getRow(i);
  
            if (null == dataRow)
                continue;
            if (ExcelUtils.isRowEmpty(dataRow))
                break;
            
            TeSysUserVo sysUser = excelRow2TeSysUserVo(dataRow, fildCol);
            sysUserList.add(sysUser);
        }
        
        return sysUserList;
	}

	/**
	 * 完善sysUserList的数据，校验姓名、归属小组、开始时间、项目角色
	 * @param sysDefRoleUser
	 * @param sysUserList
	 * @return
	 */
	private String completionUserData(TeSysDefRoleUser sysDefRoleUser, List<TeSysUserVo> sysUserList) {
	  String result = "";
	  if(sysDefRoleUser == null || sysDefRoleUser.getSrcDef() == null
	      || sysDefRoleUser.getSrcDef().getSrcDefId() == null) {
        return "所属项目为空";
	  }

      List<IDbCondition> conds = new ArrayList<IDbCondition>();
      conds.add(new DbEqualCondition(DFN.sysDef__isValid, true));
      conds.add(new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeConstants.PRJGROUP_CODENAME));
      conds.add(new DbEqualCondition(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), sysDefRoleUser.getSrcDef().getSrcDefId()));
      List<TeSysDef> groupSysDefs = sysDefDao.findByConds(conds, null);
      Map<String, ObjectId> groupSysMap = new HashMap<String, ObjectId>();
      for (TeSysDef groupSysDef : groupSysDefs) {
        groupSysMap.put(groupSysDef.getDefName(), groupSysDef.getId());
      }

      	conds.clear();
		conds.add(new DbEqualCondition(DFN.sysDef__isValid, true));
		conds.add(new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
		conds.add(new DbEqualCondition(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.EMPWORKTYPE));
		List<TeSysDef> workTypeSysDefs = sysDefDao.findByConds(conds, null);
		Map<String, TeIdNameCn> workTypeSysMap = new HashMap<String, TeIdNameCn>();
		for (TeSysDef workTypeSysDef : workTypeSysDefs) {
			TeIdNameCn cn = new TeIdNameCn();
			cn.setName(workTypeSysDef.getDefName());
			cn.setCid(workTypeSysDef.getId());
			cn.setCodeName(workTypeSysDef.getCodeName());
			workTypeSysMap.put(workTypeSysDef.getDefName(), cn);
		}

      List<TeSysDef> prjUserRoleSysDefs = getPrjUserRoleDef();
      Map<String, ObjectId> prjUserRoleSysMap = new HashMap<String, ObjectId>();
      for (TeSysDef prjUserRoleSysDef : prjUserRoleSysDefs) {
        prjUserRoleSysMap.put(prjUserRoleSysDef.getDefName(), prjUserRoleSysDef.getId());
      }
      
      for (TeSysUserVo sysUserVo : sysUserList) {

        if(StringUtil.isNull(sysUserVo.getLoginName())) {
          result += "NT账号/工号不能为空；";
        }
//        String[] userNames = sysUserVo.getUserName().split("/");
//        if(userNames.length != 2) {
//          result += "姓名格式：用户名/NT账号；";
//        } else {
//          sysUserVo.setUserName(userNames[0]);
//          sysUserVo.setLoginName(userNames[1]);
//        }
        
        if(StringUtil.isNull(sysUserVo.getGroupName())) {
          result += sysUserVo.getLoginName() + "归属小组不能为空；";
        } else if(!groupSysMap.containsKey(sysUserVo.getGroupName())) {
          result += sysUserVo.getLoginName() + "归属小组[" + sysUserVo.getGroupName() + "]未定义；";
        } else {
          sysUserVo.setGroupIdValue(groupSysMap.get(sysUserVo.getGroupName()).toHexString());
        }

        if(StringUtil.isNull(sysUserVo.getStartDate())) {
          // 默认当前时间
          sysUserVo.setStartDate(com.linkus.base.util.DateUtil.formatDate2Str(new Date(),
              com.linkus.base.util.DateUtil.DATE_FORMAT));
        }else if(com.linkus.base.util.DateUtil.parseDate(sysUserVo.getStartDate(),com.linkus.base.util.DateUtil.DATE_FORMAT) == null){
        	result += sysUserVo.getLoginName() + "进入项目时间有误；";
		}
        if(StringUtil.isNotNull(sysUserVo.getEndDate())){
			if(sysUserVo.getEndDate().compareTo(sysUserVo.getStartDate()) <0){
				result += sysUserVo.getLoginName() + "进入项目时间不能大于离开项目时间；";
			}
		}

		String roleCode = sysUserVo.getRoleCode();
		List<String> codes = new ArrayList<>();
		List<String> roleCodes = Arrays.asList(roleCode.split(","));

		  for (String code : roleCodes) {
			  if ("成员".equals(code)){
				  codes.add(SysDefConstants.MEMBER_DEF_ID);
				  sysUserVo.setRoleCodes(codes);
			  } else if (!prjUserRoleSysMap.containsKey(code)){
				  result += sysUserVo.getLoginName() + "项目角色[" + sysUserVo.getRoleCode() + "]未定义；";
			  } else {
				  String codeId = prjUserRoleSysMap.get(code).toHexString();
				  codes.add(codeId);
				  sysUserVo.setRoleCodes(codes);
			  }
		  }


		  if(StringUtil.isNull(sysUserVo.getEmpWorkType())) {
			  result += sysUserVo.getLoginName() + "办公方式不能为空；";
		  } else if(!workTypeSysMap.containsKey(sysUserVo.getEmpWorkType())) {
			  result += sysUserVo.getLoginName() + "办公方式[" + sysUserVo.getEmpWorkType() + "]未定义；";
		  } else {
			  sysUserVo.setEmpWorkTypeCn(workTypeSysMap.get(sysUserVo.getEmpWorkType()));
		  }
      }
      return result;
	}
	
	private TeSysUserVo excelRow2TeSysUserVo(Row dataRow, Map<String, Integer> fildCol) {
      
        TeSysUserVo sysUser = new TeSysUserVo();
        
        String loginName = confirmColumn(dataRow, fildCol.get("* 人员（NT账号 或 工号）"));
        sysUser.setLoginName(loginName);
		String employeeType = confirmColumn(dataRow, fildCol.get("* 员工类型"));
		sysUser.setEmployeeType(employeeType);
		String groupName = confirmColumn(dataRow, fildCol.get("* 归属小组"));
		sysUser.setGroupName(groupName);
        String roleCode = confirmColumn(dataRow, fildCol.get("* 项目角色"));
        sysUser.setRoleCode(roleCode);
		String empWorkType = confirmColumn(dataRow, fildCol.get("* 办公方式"));
		sysUser.setEmpWorkType(empWorkType);
        String startDate = confirmColumn(dataRow, fildCol.get("* 加入项目日期"));
        sysUser.setStartDate(startDate);


      
        return sysUser;
    }
	
	/**
    * 确认导入行对应下标的值
    * 
    * @param dataRow
    * @param index
    * @return
    */
    private String confirmColumn(Row dataRow, Integer index) {

        String value = "";

        if (index != null) {
            value = ExcelUtils.getCellFormatValueRetentionAccuracy(dataRow.getCell(index)).trim();
        }

        return value;
    }
	
	@SuppressWarnings("deprecation")
	public Object getCellFormatValue(Cell cell){
	        Object cellValue = null;
	        if(cell!=null){
	            //判断cell类型
	            switch(cell.getCellType()){
	            case Cell.CELL_TYPE_NUMERIC:{
	                cellValue = String.valueOf(cell.getNumericCellValue());
	                break;
	            }
	            case Cell.CELL_TYPE_FORMULA:{
	                //判断cell是否为日期格式
	                if(DateUtil.isCellDateFormatted(cell)){
	                    //转换为日期格式YYYY-mm-dd
	                    cellValue = cell.getDateCellValue();
	                }else{
	                    //数字
	                    cellValue = String.valueOf(cell.getNumericCellValue());
	                }
	                break;
	            }
	            case Cell.CELL_TYPE_STRING:{
	                cellValue = cell.getRichStringCellValue().getString();
	                break;
	            }
	            default:
	                cellValue = "";
	            }
	        }else{
	            cellValue = "无";
	        }
	        return cellValue;
	    }
	 public Workbook estimateVersion(String fileName,InputStream in,Workbook wb) throws IOException{
		  if(fileName.endsWith(".xls")){
              wb = new HSSFWorkbook(in);
          }else if(fileName.endsWith(".xlsx")){
              wb = new XSSFWorkbook(in);
          }else{
              wb = null;
          }
		  return wb;
	 }

	@Override
	public List<TeSysDefRoleUser2UserVo> queryUserByDefIdAndCurrentGroupId(TeSysDefRoleUserVo teSysDefRoleUserVo) {
		List<SysDef> sysDefsBySrc = sysDefService.getSysDefsBySrc(teSysDefRoleUserVo.getDefId(), SysDefTypeCodeName.PRJ_GROUP);
		List<SysDef> allChildList=new ArrayList<SysDef>();
		this.recursionChild(sysDefsBySrc,null,teSysDefRoleUserVo.getCurrentGroupId(),allChildList);
		for(SysDef tem : sysDefsBySrc){
			if(tem.getId().equals(teSysDefRoleUserVo.getCurrentGroupId())){
				allChildList.add(tem);
			}
		}
		List<TeSysDefRoleUser> findTeSysDefRoleUserByList = sysDefRoleUserDao.findTeSysDefRoleUserByList(allChildList);
		List<TeSysDefRoleUser2UserVo> teSysDefRoleUser2UserVoLists=new ArrayList<TeSysDefRoleUser2UserVo>();
		for(TeSysDefRoleUser tem : findTeSysDefRoleUserByList){
			TeSysDefRoleUser2UserVo teSysDefRoleUser2UserVo=new TeSysDefRoleUser2UserVo();
			BeanUtils.copyProperties(tem.getRoleUser(), teSysDefRoleUser2UserVo);
			teSysDefRoleUser2UserVoLists.add(teSysDefRoleUser2UserVo);
		}
		Map<Object, TeSysDefRoleUser2UserVo> tempMap=new HashMap<Object, TeSysDefRoleUser2UserVo>();
		List<TeSysDefRoleUser2UserVo> lists=new ArrayList<TeSysDefRoleUser2UserVo>();
		for(TeSysDefRoleUser2UserVo tem : teSysDefRoleUser2UserVoLists){
				tempMap.put(tem.getUserIdValue(),tem);
		}
		for(TeSysDefRoleUser2UserVo value : tempMap.values()){
			lists.add(value);
		}
		return lists;
	}
	private void recursionChild(List<SysDef> sysDefsBySrc,List<SysDef> needRecursionList,ObjectId currentGroupId,List<SysDef> allChildList){
		List<SysDef> needRecursionChildList=new ArrayList<SysDef>();
		
		for(SysDef tem:sysDefsBySrc){
			if(tem.getParentDefId()==null){
				continue;
			}else{
				if(tem.getParentDefId().equals(currentGroupId)){
					needRecursionChildList.add(tem);
					allChildList.add(tem);
				}
			}
		}
		for(SysDef tem:needRecursionChildList){
			recursionChild(sysDefsBySrc,needRecursionChildList,tem.getId(),allChildList);
		}
	}

	@Override
	public List<TeSysDefRoleUser> findByDefTypeAndRoleAndUser(
			TeSysDefRoleUserVo teSysDefRoleUserVo) {
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(teSysDefRoleUserVo,teSysDefRoleUser);
		if (null != teSysDefRoleUserVo.getRole()){
			List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
			TeSysDefRoleUser2Role role = teSysDefRoleUserVo.getRole();
			roles.add(role);
			teSysDefRoleUser.setRole(roles);
		}
		List<TeSysDefRoleUser> result = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(teSysDefRoleUser);
		return result;
	}

	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefIdAndUserId(
			ObjectId defId, ObjectId userId,List<ObjectId> roleIds) {
		return sysDefRoleUserDao.findByDefIdAndUser(defId,userId,roleIds);
	}	
	
	@Override
	public String syncPSOEmployee(TeSysDefRoleUserVo teSysDefRoleUserVo,TeSysUser teSysUser){
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		TeSysDefRoleUser2DefType defType=new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeConstants.KMDOMAIN_CODENAME);
		teSysDefRoleUser.setDefType(defType);
		teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
		//查询当前公共库下面的人员
		List<TeSysUserVo> currentUserList = sysUserService.queryTeSysUserVoByDomain(teSysDefRoleUser);
		List<String> userIdList = new ArrayList<String>();
		if(null != currentUserList && !currentUserList.isEmpty()){
			for(TeSysUserVo userVo : currentUserList){
				userIdList.add(userVo.getId().toString());
			}
		}
		List<TeSysUserVo> repeatUserList= new ArrayList<TeSysUserVo>();
		SysDef sysDef = sysDefService.getSysDefById(teSysDefRoleUserVo.getDefId());
		//查询与公共库同级下的其他库
		List<SysDef> sysDefs = sysDefService.getSysDefsByParent(sysDef.getParentDefId(),SysDefTypeCodeName.KM_DOMAIN);
		if( null != sysDefs && !sysDefs.isEmpty()){
			for(SysDef def : sysDefs){
				if(!def.getId().equals(sysDef.getId())){
					TeSysDefRoleUser user = new TeSysDefRoleUser();
					user.setDefId(def.getId());
					List<TeSysUserVo> userVos = sysUserService.recurseQueryTeSysUserVoByDomain(user);
					if(null != userVos){
						repeatUserList.addAll(userVos);
					}
				}
				
			}
		}
		if(!repeatUserList.isEmpty()){
			List<TeSysUserVo> addUserList= new ArrayList<TeSysUserVo>();
			for(TeSysUserVo userVo : repeatUserList){
				if(!userIdList.contains(userVo.getId().toString())){
					addUserList.add(userVo);
					userIdList.add(userVo.getId().toString());
				}
			}
			if(!addUserList.isEmpty()){
				List<TeSysDefRoleUser> teSysDefRoleUsers= getTeSysDefRoleUser (addUserList, teSysDefRoleUserVo,  teSysUser);
				addTeSysDefRoleUserList(teSysDefRoleUsers);
				return teSysDefRoleUsers.size()+"";
			}
		}
		return "0";
	}

	private List<TeSysDefRoleUser> getTeSysDefRoleUser (List<TeSysUserVo> addUserList,TeSysDefRoleUserVo teSysDefRoleUserVo ,TeSysUser teSysUser){
		List<TeSysDefRoleUser> resultList = new ArrayList<TeSysDefRoleUser>();
		Date date = new Date();
		//defType知识域	
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.KMDOMAIN_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.KMDOMAIN_DEF_TYPENAME);
		defType.setDefTypeCodeName(SysDefTypeConstants.KMDOMAIN_CODENAME);
		//srcDef知识库
		SysDef kmBase = sysDefService.getSysDefById(teSysDefRoleUserVo.getSrcDef().getSrcDefId());
		TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
		srcDef.setSrcDefId(kmBase.getId());
		srcDef.setSrcDefName(kmBase.getDefName());
		srcDef.setSrcDefCodeName(kmBase.getCodeName());
		//role成员角色
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		SysDef roleDef = sysDefService.getSysDefById(teSysDefRoleUserVo.getRole().getRoleId());
		role.setRoleId(roleDef.getId());
		role.setRoleCodeName(roleDef.getCodeName());
		role.setRoleName(roleDef.getDefName());
		//addUser添加人信息
		TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
		addUser.setUserId(teSysUser.getId());
		addUser.setLoginName(teSysUser.getLoginName());
		addUser.setUserName(teSysUser.getUserName());
		addUser.setJobCode(teSysUser.getJobCode());
		for(TeSysUserVo userVo : addUserList ){
				List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
				TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
				teSysDefRoleUser.setDefType(defType);
				teSysDefRoleUser.setSrcDef(srcDef);
				teSysDefRoleUser.setDefId(teSysDefRoleUserVo.getDefId());
				roleUsers.add(role);
				teSysDefRoleUser.setRole(roleUsers);
				TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
				roleUser.setUserId(userVo.getId());
				roleUser.setLoginName(userVo.getLoginName());
				roleUser.setUserName(userVo.getUserName());
				roleUser.setJobCode(userVo.getJobCode());
				teSysDefRoleUser.setRoleUser(roleUser);
				teSysDefRoleUser.setAddUser(addUser);
				teSysDefRoleUser.setAddTime(date);
				teSysDefRoleUser.setIsValid(true);
				
				resultList.add(teSysDefRoleUser);
		}
		return resultList;
	}
	
	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(ObjectId defTypeId, List<ObjectId> defIds,ObjectId roleId){
		return sysDefRoleUserDao.querySysRoleUserByDefTypeIdAndDefIds(defTypeId,defIds,roleId);
	}
	
	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(
			ObjectId defTypeId, List<ObjectId> defIds, ObjectId roleId,
			String userNameCode, Pager pager) {
    	List<ObjectId> roleIds = new ArrayList<ObjectId>(1);
		roleIds.add(roleId);
	    return queryRoleUsers(defTypeId, defIds, roleIds, userNameCode, pager);
	}

	@Override
	public List<TeSysDefRoleUser> queryRoleUsers(
			ObjectId defTypeId, List<ObjectId> defIds, List<ObjectId> roleIds,
			String userNameCode, Pager pager) {
		List<IDbCondition> conds = new ArrayList<IDbCondition>();
		conds.add(new DbEqualCondition(DbFieldName.sysDefRoleUser_isValid, true));
		if( null != defTypeId){
			IDbCondition defTypeCond = new DbEqualCondition(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeId),defTypeId);
			conds.add(defTypeCond);
		}
		if(null != defIds){
			IDbCondition defCond = new DbInCondition<ObjectId>(DbFieldName.sysDefRoleUser__defId, defIds);
			conds.add(defCond);
		}
		if(!CollectionUtils.isEmpty(roleIds)){
			IDbCondition roleCond = new DbInCondition<ObjectId>(DbFieldName.sysDefRoleUser__role.dot(DbFieldName.sysDefRoleUser__roleId),roleIds);
			conds.add(roleCond);
		}
		if(!StringUtil.isNull(userNameCode)){
			if(userNameCode.contains("/")){
				IDbCondition userNameCond = new DbLikeCondition(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__userName), userNameCode.split("/")[0]);
				conds.add(userNameCond);
				IDbCondition loginNameCond = new DbLikeCondition(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__loginName), userNameCode.split("/")[1]);
				conds.add(loginNameCond);
			} else {
				List<DbFieldName> fieldNames = new ArrayList<DbFieldName>();
				fieldNames.add(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__loginName));
				fieldNames.add(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__userName));
				IDbCondition userNameCodeCond = new DbFuzzyCondition(fieldNames,userNameCode);
				conds.add(userNameCodeCond);
			}
		}
		return sysDefRoleUserDao.findByConds(conds, null, pager);
	}

	@Override
	public void removeByIds(List<ObjectId> ids){
		List<TeSysDefRoleUser> tmps = sysDefRoleUserDao.findByIds(ids);
		for (TeSysDefRoleUser t : tmps) {
			t.setIsValid(false);
			sysDefRoleUserDao.updateByObjectId(t.getId(), t);
		}
	}
	
	@Override
	public void updateRoleUser(List<String> ids,Map<String, Object> fieldsMap){
		if(null != ids && !ids.isEmpty()){
			sysDefRoleUserDao.updateFieldsByIdList(ids, fieldsMap);
		}
	}

	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds,
			List<ObjectId> defIds, SysDefTypeCodeName codeName) {
		if ((defIds == null || defIds.isEmpty()) && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}
		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(null, roleIds, defIds, codeName);
		this.getCCRoleUsers(roleIds,null,sysUserRole);
		return sysUserRole;
	}

	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds,
			List<ObjectId> defIds, String userName, SysDefTypeCodeName codeName) {
		if ((defIds == null || defIds.isEmpty()) && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}
		return sysDefRoleUserDao.getSysUserRoleByName(userName, roleIds, defIds, codeName);
	}

	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds,
			ObjectId defId, SysDefTypeCodeName codeName) {
		if (defId == null  && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}
		List<ObjectId> defIds = new ArrayList<>();
		if(defId != null) {
			defIds.add(defId);
		}

		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(null, roleIds, defIds, codeName);
		this.getCCRoleUsers(roleIds,null,sysUserRole);
		return sysUserRole;
	}

	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds,
			ObjectId defId, SysDefTypeCodeName codeName, Boolean isStd) {
		if (defId == null  && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}
		List<ObjectId> defIds = new ArrayList<>();
		if(defId != null) {
			defIds.add(defId);
		}
		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(null, roleIds, defIds, codeName, isStd);
		this.getCCRoleUsers(roleIds,null,sysUserRole);
		return sysUserRole;
	}

	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, List<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName) {
		if (CollectionUtils.isEmpty(defIds) && CollectionUtils.isEmpty(srcDefIds)  && defTypeCodeName == null) {
			throw new BaseException("定义ID，源定义ID，定义类型不能全部为空");
		}
		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(null, roleIds, defIds, srcDefIds, defTypeCodeName);
		this.getCCRoleUsers(roleIds,null,sysUserRole);
		return sysUserRole;
	}

	@Override
	public List<TeSysDefRoleUser> getDefs(List<ObjectId> userIds, List<ObjectId> roleIds, SysDefTypeCodeName codeName) {
		if (codeName == null){
			throw new BaseException("定义类型不能为null");
		}
		List<TeSysDefRoleUser> teSysDefRoleUsers = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, codeName);
		this.getBuAndCcRoleUsers(roleIds,userIds,teSysDefRoleUsers, null, null);
		return teSysDefRoleUsers;

	}



	@Override
	public List<ObjectId> addSysUserRoles(ObjectId userId, ObjectId roleId,
										  List<ObjectId> defIds, TeSysUser loginUser) {
		// 获取角色信息
		SysDef roleDef = sysDefService.getSysDefById(roleId);
		if (roleDef == null) {
			throw new BaseException("角色信息不存在或者已经失效");
		}

		// 获取用户信息
		TeSysUser user = sysUserService.findById(userId);
		if (user == null) {
			throw new BaseException("用户信息不存在或者已经失效");
		}

		// 获取定义信息
		List<SysDef> sysDefs = sysDefService.getSysDefsByIds(defIds);
		if (defIds.isEmpty()) {
			throw new BaseException("定义信息不存在或者已经失效");
		}
		
		
		Date now = new Date();

		List<TeSysDefRoleUser> sysUserRoles = new ArrayList<>();
		for (SysDef sysDef : sysDefs) {
			TeSysDefRoleUser sysUserRole = new TeSysDefRoleUser();
			SysDefType defType = sysDef.getDefType();
			TeSysDefRoleUser2DefType roleUserDefType = new TeSysDefRoleUser2DefType();
			roleUserDefType.setDefTypeCodeName(defType.getCodeName());
			roleUserDefType.setDefTypeId(defType.getId());
			roleUserDefType.setDefTypeName(defType.getDefTypeName());
			sysUserRole.setDefId(sysDef.getId());
			sysUserRole.setDefType(roleUserDefType);
			if (sysDef.getSrcDef() != null) {
				TeSysDefRoleUser2SysDef srcSysDef = new TeSysDefRoleUser2SysDef();
				SysDef srcDef = sysDef.getSrcDef();
				srcSysDef.setSrcDefCodeName(srcDef.getCodeName());
				srcSysDef.setSrcDefId(srcDef.getId());
				srcSysDef.setSrcDefName(srcDef.getDefName());
				sysUserRole.setSrcDef(srcSysDef);
			}
			List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
			TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
			role.setRoleCodeName(roleDef.getCodeName());
			role.setRoleId(roleDef.getId());
			role.setRoleName(roleDef.getDefName());
			roleUsers.add(role);
			sysUserRole.setRole(roleUsers);
			TeSysDefRoleUser2User user2User = new TeSysDefRoleUser2User();
			user2User.setJobCode(user.getJobCode());
			user2User.setLoginName(user.getLoginName());
			user2User.setUserId(user.getId());
			user2User.setUserName(user.getUserName());
			sysUserRole.setRoleUser(user2User);					
			sysUserRole.setAddTime(now);
			sysUserRole.setIsValid(true);
			if (loginUser != null) {
				TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
				addUser.setUserId(loginUser.getId());
				addUser.setLoginName(loginUser.getLoginName());
				addUser.setUserName(loginUser.getUserName());
				addUser.setJobCode(loginUser.getJobCode());
				sysUserRole.setAddUser(addUser);
			}
			sysUserRoles.add(sysUserRole);
		}

		sysDefRoleUserDao.batchSave(sysUserRoles);

		List<ObjectId> ids = new ArrayList<>();
		for (TeSysDefRoleUser sur : sysUserRoles) {
			ids.add(sur.getId());
		}
		return ids;
	}
	@Override
	public ObjectId addSysUserRoles(ObjectId userId, ObjectId roleId,
			ObjectId defId,List<ObjectId> linkedPrdIds,ObjectId prjId) {
		// 获取定义信息
		SysDef sysDef = sysDefService.getSysDefById(defId);
		if (sysDef == null) {
			throw new BaseException("定义信息不存在或者已经失效");
		}

		// 获取角色信息
		SysDef roleDef = sysDefService.getSysDefById(roleId);
		if (roleDef == null) {
			throw new BaseException("角色信息不存在或者已经失效");
		}

		// 获取用户信息
		TeSysUser user = sysUserService.findById(userId);
		if (user == null) {
			throw new BaseException("用户信息不存在或者已经失效");
		}

		TeSysDefRoleUser sysUserRole = new TeSysDefRoleUser();
		sysUserRole.setDefId(sysDef.getId());
		
		TeSysDefRoleUser2DefType roleUserDefType = new TeSysDefRoleUser2DefType();
		roleUserDefType.setDefTypeCodeName(sysDef.getDefType().getCodeName());
		roleUserDefType.setDefTypeId(sysDef.getDefType().getId());
		roleUserDefType.setDefTypeName(sysDef.getDefType().getDefTypeName());
		
		sysUserRole.setDefType(roleUserDefType);
		if (sysDef.getSrcDef() != null) {
			TeSysDefRoleUser2SysDef srcSysDef = new TeSysDefRoleUser2SysDef();
			SysDef srcDef = sysDef.getSrcDef();
			srcSysDef.setSrcDefCodeName(srcDef.getCodeName());
			srcSysDef.setSrcDefId(srcDef.getId());
			srcSysDef.setSrcDefName(srcDef.getDefName());
			sysUserRole.setSrcDef(srcSysDef);
		}
		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleCodeName(roleDef.getCodeName());
		role.setRoleId(roleDef.getId());
		role.setRoleName(roleDef.getDefName());
		roleUsers.add(role);
		sysUserRole.setRole(roleUsers);
		
		TeSysDefRoleUser2User user2User = new TeSysDefRoleUser2User();
		user2User.setJobCode(user.getJobCode());
		user2User.setLoginName(user.getLoginName());
		user2User.setUserId(user.getId());
		user2User.setUserName(user.getUserName());
		sysUserRole.setRoleUser(user2User);	
		sysUserRole.setIsValid(true);
		sysUserRole.setAddTime(new Date());

		if (null != linkedPrdIds && !linkedPrdIds.isEmpty()){
			List<TeSysDef> prdList = sysDefDao.getSysDefsByIds(linkedPrdIds);
			List<TeIdNameCn> linkedDefs = new ArrayList<>();
			for (TeSysDef teSysDef : prdList) {
				TeIdNameCn cn = new TeIdNameCn();
				cn.setCid(teSysDef.getId());
				cn.setName(teSysDef.getDefName());
				cn.setCodeName(teSysDef.getDefCode());
				linkedDefs.add(cn);
			}
			sysUserRole.setLinkedDefs(linkedDefs);
		}

		if (null != prjId){
			SysDef sysDefById = sysDefService.getSysDefById(prjId);
			if (sysDefById == null) {
				throw new BaseException("项目不存在或者已经失效");
			}
			TeIdNameCn cn = new TeIdNameCn();
			cn.setCid(sysDefById.getId());
			cn.setName(sysDefById.getDefName());
			sysUserRole.setPrj(cn);
		}
		sysDefRoleUserDao.save(sysUserRole);
		return sysUserRole.getId();
	}
	@Override
	public void removeSysUserRole(ObjectId id) {
		TeSysDefRoleUser t = new TeSysDefRoleUser();
		t.setId(id);
		sysDefRoleUserDao.deleteById(t);
	}

	@Override
	public void removeSysUserRole(ObjectId userId, ObjectId roleId, ObjectId defId, SysDefTypeCodeName codeName) {
		List<ObjectId> userIds = new ArrayList<>();
		if (userId != null) {
			userIds.add(userId);
		}

		List<ObjectId> roleIds = new ArrayList<>();
		if (roleId != null) {
			roleIds.add(roleId);
		}

		List<ObjectId> defIds = new ArrayList<>();
		if (defId != null) {
			defIds.add(defId);
		}

		// 当定义id为空的时候，必须指定定义类型
		if (defIds.isEmpty() && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}

		sysDefRoleUserDao.removeSysUserRoles(userIds, roleIds, defIds, codeName);
	}
	@Override
	public void removeSysUserRole(ObjectId userId, ObjectId roleId, List<ObjectId> defIds,
			SysDefTypeCodeName codeName) {
		List<ObjectId> userIds = new ArrayList<>();
		if (userId != null) {
			userIds.add(userId);
		}

		List<ObjectId> roleIds = new ArrayList<>();
		if (roleId != null) {
			roleIds.add(roleId);
		}

		// 当定义id为空的时候，必须指定定义类型
		if ((defIds == null || defIds.isEmpty()) && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}

		sysDefRoleUserDao.removeSysUserRoles(userIds, roleIds, defIds, codeName);
	}

	@Override
	public List<TeSysDefRoleUser> queryRoleUserList(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
													Collection<ObjectId> defIds, SysDefTypeCodeName codeName) {

    	if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(roleIds) && CollectionUtils.isEmpty(defIds) && null == codeName) {
			throw new BaseException("入参不能全部为空");
		}
		return sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds, codeName);
	}

	@Override
	public List<TeSysDefRoleUser> getUserRoles(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
			Collection<ObjectId> defIds, SysDefTypeCodeName codeName) {

		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds, codeName);
		this.getCCRoleUsers(roleIds,userIds,sysUserRole);
		return sysUserRole;
	}
	
	
	/**
	 * *************************************************运维管理*************************************************
	 */
	
	/**
	 * 运维管理-人员权限-添加人员
	 * @param roleUserList
	 */
	@Override
	public Map<String, Object> addOperationUser(List<TeSysDefRoleUserVo> roleUserList) {
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		if (null==roleUserList || roleUserList.isEmpty()) {
			resultMap.put("result", "fail");
			resultMap.put("info", "params roleUserList is null.");
			return resultMap;
		}
		
		// 定义类型
		TeSysDefType df = null;
		if (SysDefTypeCodeName.OMS_CHECK_PROV.getValue().equals(roleUserList.get(0).getUserType())) {
			df = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeCodeName.OMS_CHECK_PROV.getValue());	// 省份
		} else if (SysDefTypeCodeName.OMS_CHECK_OMSPL.getValue().equals(roleUserList.get(0).getUserType())) {
			df = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeCodeName.OMS_CHECK_OMSPL.getValue());	// 产品线
		} else if (SysDefTypeCodeName.AI_BU.getValue().equals(roleUserList.get(0).getUserType())) {
			df = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeCodeName.AI_BU.getValue());	// BU
		} else if (SysDefTypeCodeName.DEPT.getValue().equals(roleUserList.get(0).getUserType())) {
			df = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeCodeName.DEPT.getValue());	// 区域
		}
		
		// 角色
		SysDef dr = null;
		if (SysOmsConstants.ROLE_ADMIN_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_ADMIN_CODENAME, SysDefTypeCodeName.ROLE);	// 管理员
		} else if (SysOmsConstants.ROLE_RESP_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_RESP_CODENAME, SysDefTypeCodeName.ROLE);	// 负责人
			// 查询对应用户信息
			List<ObjectId> defObjIdList = new ArrayList<ObjectId>();
			defObjIdList.add(roleUserList.get(0).getDefId());
			List<TeSysDefRoleUser> respRoleUserList = sysDefRoleUserDao.getSysUserRole(null, null, defObjIdList, null);
			if (null!=respRoleUserList && !respRoleUserList.isEmpty()) {
				
				for (TeSysDefRoleUser respRoleUser : respRoleUserList) {
					List<TeSysDefRoleUser2Role> role = respRoleUser.getRole();
					for (TeSysDefRoleUser2Role user2Role : role) {
						if (SysOmsConstants.ROLE_RESP_CODENAME.equals(user2Role.getRoleCodeName())){
							sysDefRoleUserDao.deleteByCondition(respRoleUser);
						}
					}
				}
			}
		} else if (SysOmsConstants.ROLE_OMSM_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_OMSM_CODENAME, SysDefTypeCodeName.ROLE);	// 运维总监
			
		} else if (SysOmsConstants.ROLE_OMRESP_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_OMRESP_CODENAME, SysDefTypeCodeName.ROLE);	// 	运维负责人
		} else if (SysOmsConstants.ROLE_BUADMIN_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_BUADMIN_CODENAME, SysDefTypeCodeName.ROLE);	// 	BU管理员
		} else if (SysOmsConstants.ROLE_OMREGIONSM_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_OMREGIONSM_CODENAME, SysDefTypeCodeName.ROLE);	// 	区域总监
		}else if (SysOmsConstants.ROLE_DEPTMANGER_CODENAME.equals(roleUserList.get(0).getRoleCodeName())) {
			dr = sysDefService.getSysDefByCodeName(SysOmsConstants.ROLE_DEPTMANGER_CODENAME, SysDefTypeCodeName.ROLE);	// 	部门经理
			// 查询对应用户信息
			List<ObjectId> defObjIdList = new ArrayList<ObjectId>();
			defObjIdList.add(roleUserList.get(0).getDefId());
			List<TeSysDefRoleUser> respRoleUserList = sysDefRoleUserDao.getSysUserRole(null, null, defObjIdList, null);
			if (null!=respRoleUserList && !respRoleUserList.isEmpty()) {

				for (TeSysDefRoleUser respRoleUser : respRoleUserList) {
					List<TeSysDefRoleUser2Role> role = respRoleUser.getRole();
					for (TeSysDefRoleUser2Role user2Role : role) {
						if (SysOmsConstants.ROLE_DEPTMANGER_CODENAME.equals(user2Role.getRoleCodeName())){
							sysDefRoleUserDao.deleteByCondition(respRoleUser);
						}
					}
				}
			}
		}
		
		TeSysDefRoleUser roleuser = null;
		List<TeSysDefRoleUser> dataList = new ArrayList<TeSysDefRoleUser>();
		for (TeSysDefRoleUserVo vo : roleUserList) {
			
			roleuser = new TeSysDefRoleUser();
			BeanUtils.copyProperties(vo, roleuser);
			
			
			TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
			defType.setDefTypeId(df.getId());
			defType.setDefTypeName(df.getDefTypeName());
			defType.setDefTypeCodeName(df.getCodeName());
			roleuser.setDefType(defType);

			List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
			TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
			role.setRoleId(dr.getId());
			role.setRoleName(dr.getDefName());
			role.setRoleCodeName(dr.getCodeName());
			roleUsers.add(role);
			roleuser.setRole(roleUsers);
			
			roleuser.setIsValid(true);
			roleuser.setAddTime(new Date());
			dataList.add(roleuser);
		}
		
		sysDefRoleUserDao.batchSave(dataList);
		resultMap.put("result", "success");
		return resultMap;
	}
	
	/**
	 * 查询sbuId对应roleUser
	 */
	@Override
	public List<TeSysDefRoleUser> queryBuRoleUser(String sbuId, String roleCodeName, String loginName) {
		
		//定义BU
		//SysDef defBu = sysDefService.getSysDefByCodeName(sbuId, SysDefTypeCodeName.AI_BU);
		
		// 查询对应用户角色信息
		TeSysDefRoleUser defRoleUser = new TeSysDefRoleUser();
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeCodeName.AI_BU.getValue());
		defRoleUser.setDefType(defType);
		if (StringUtil.isNotNull(sbuId)) {
			defRoleUser.setDefId(new ObjectId(sbuId));
		}
		if (StringUtil.isNotNull(roleCodeName)) {
			List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
			TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
			role.setRoleCodeName(roleCodeName);
			roleUsers.add(role);
			defRoleUser.setRole(roleUsers);
		}
		if (StringUtil.isNotNull(loginName)) {
			TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
			roleUser.setLoginName(loginName);
			defRoleUser.setRoleUser(roleUser);
		}
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.findByDefTypeAndRoleAndUser(defRoleUser);

		return list;
	}
	
	/**
	 * 验证运维管理负责人或管理员或运维总监
	 */
	@Override
	public List<TeSysDefRoleUser> sureOperationRoleUser(ObjectId userId, String defType) {
		
		List<ObjectId> userIdList = new ArrayList<ObjectId>();
		userIdList.add(userId);
		
		List<ObjectId> roleIdList = new ArrayList<ObjectId>();
		if (SysOmsConstants.PROVSM.equals(defType)){
			roleIdList.add(SysOmsConstants.ROLE_OMSM_OBJID);
		} else {
			roleIdList.add(SysOmsConstants.ROLE_ADMIN_OBJID);
			roleIdList.add(SysOmsConstants.ROLE_RESP_OBJID);
		}
		
		SysDefTypeCodeName sysDefType = SysDefTypeCodeName.OMS_CHECK_PROV;
		if (SysDefTypeCodeName.OMS_CHECK_OMSPL.getValue().equals(defType)){
			sysDefType = SysDefTypeCodeName.OMS_CHECK_OMSPL;
		} else if (SysOmsConstants.PROVSM.equals(defType)){
			sysDefType = SysDefTypeCodeName.OMS_CHECK_PROV;
		}
			
		List<TeSysDefRoleUser> list = sysDefRoleUserDao.getSysUserRole(userIdList, roleIdList, null, sysDefType);
		this.getCCRoleUsers(roleIdList,userIdList,list);
		return list;
	}

	@Override
	public TeSysDefRoleUser queryPomAuthByLoginName(String loginName) {
		return sysDefRoleUserDao.queryPomAuthByLoginName(loginName);
	}

	@Override
	public Boolean queryPomAuth(ObjectId userId) {
		Boolean auth=false;
		//系统管理员
		TeSysDefRoleUser systemMgt = sysDefRoleUserDao.queryByUserIdAndRoleId(userId,new ObjectId("5a61fa4c29974f7cabe91128"));
		//项目运营管理员
		TeSysDefRoleUser prjMgt = sysDefRoleUserDao.queryByUserIdAndRoleId(userId,new ObjectId("5c08ddc9900add501a1413e2"));
		if(StringUtil.isNotNull(systemMgt) || StringUtil.isNotNull(prjMgt)){
			auth=true;
		}
		return auth;
	}
	
	@Override
	public List<TeSysDefRoleUser> findTeSysDefRoleUserByCondition(
			TeSysDefRoleUser teSysDefRoleUser){
		return sysDefRoleUserDao.findTeSysDefRoleUserByCondition(teSysDefRoleUser);
	}

	@Override
	public Boolean visitAnalysisAuth(ObjectId userId) {
		Boolean auth=false;
		TeSysDefRoleUser visitAnalysisMgt = sysDefRoleUserDao.queryByUserIdAndRoleId(userId,new ObjectId("5c33fda3900add501a1414d7"));
		if(StringUtil.isNotNull(visitAnalysisMgt)){
			auth=true;
		}
		return auth;
	}

	/**
	 * *************************************************运维管理*************************************************
	 */

    /**
     * 注册用户
     * 
     * @param userName
     * @param loginName
     * @return
     */
    public TeSysUser registerSysUser(String userName, String loginName) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("result", false);
            
        if(StringUtil.isNull(loginName)) {
            throw new BaseException("创建的邮箱必填");
        }
        String reg="[A-Za-z0-9\\u4e00-\\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+";
        if(!loginName.matches(reg)) {
            throw new BaseException("创建用户时邮箱（" + loginName + "）格式错误");
        }
        
        List<IDbCondition> conds = new ArrayList<IDbCondition>(2);
        conds.add(new DbEqualCondition(DFN.sysUser__loginName, loginName));
        conds.add(new DbEqualCondition(DFN.sysUser_isValid, true));
        long n = sysUserDao.countByConds(conds);
        if(n > 0) {
            throw new BaseException("创建用户时邮箱（" + loginName + "）已存在");
        }

        String password = DigestUtils.md5DigestAsHex(PASSWORD.getBytes());
//        Employee employee = new Employee();
//        employee.setCompanyId(5);
//        employee.setEmp(loginName);
//        employee.setEmployeeEmail(loginName);
//        employee.setEmployeeNo(loginName);
//        employee.setName(userName);
//        employee.setPassword(password);
//        employee.setStatus("80");
//        employee.setUseNewDmp("1");
//        employeeMapper.insertEmployee(employee);
        String sql = "INSERT INTO EMPLOYEE "
            + "(EMPLOYEEID, EMP, EMPLOYEENO, NAME, PASSWORD, EMPLOYEEEMAIL, STATUS, USE_NEW_DMP, COMPANY_ID, CREATE_TIME) "
            + "VALUES(SEQ_DOSS.NEXTVAL, ?, ?, ?, ?, ?, '80', '1', 5, TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS'))";
        jdbcTemplate.update(sql, new Object[] { loginName, loginName, userName, password, loginName });
        
        TeSysUser teSysUser = new TeSysUser();
        teSysUser.setIsOutsideUser(true);
        teSysUser.setLoginName(loginName);
        teSysUser.setUserName(userName);
        teSysUser.setMailBox(loginName);
        teSysUser.setDmpUserStatusId(80);
        teSysUser.setDmpUserStatus("有效");
        teSysUser.setEmployeeType(EMPLOYEETYPE);
        teSysUser.setIsValid(true);
        teSysUser.setAddTime(new Date());
        teSysUser.setAddDesc("项目管理-组织架构-用户注册");
        
        teSysUser = sysUserDao.save(teSysUser);
        return teSysUser;
    }

    @Override
    public List<TeSysDef> getPrjUserRoleDef() {
      List<IDbCondition> conds = new ArrayList<IDbCondition>(2);
      conds.add(new DbEqualCondition(DFN.sysDef__isValid, true));
      conds.add(new DbEqualCondition(DFN.sysDef__isStd, true));
      conds.add(new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.PRJ_USER_ROLE.getValue()));
      List<TeSysDef> prjUserRoleSysDefs = sysDefDao.findByConds(conds, null);
      return prjUserRoleSysDefs;
    }

    @Override
    public List<TeSysDefRoleUser2SysDef> getPrjsByUser(ObjectId userId) {
      List<TeSysDefRoleUser2SysDef> result = new ArrayList<TeSysDefRoleUser2SysDef>();
      List<ObjectId> defIds = new ArrayList<ObjectId>();
      List<IDbCondition> conds = new ArrayList<IDbCondition>(3);
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser_isValid, true));
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), SysDefTypeCodeName.PRJ_GROUP.getValue()));
      conds.add(new DbEqualCondition(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), userId));
      List<TeSysDefRoleUser> prjUserRoleSysDefs = sysDefRoleUserDao.findByConds(conds, null);
      if(prjUserRoleSysDefs != null && !prjUserRoleSysDefs.isEmpty()) {
        for (TeSysDefRoleUser prjUserRoleSysDef : prjUserRoleSysDefs) {
          TeSysDefRoleUser2SysDef srcDef = prjUserRoleSysDef.getSrcDef();
          if(srcDef != null && srcDef.getSrcDefId() != null && !defIds.contains(srcDef.getSrcDefId())) {
            defIds.add(srcDef.getSrcDefId());
            result.add(srcDef);
          }
        }
      }
      return result;
    }

	@Override
	public List<TeSysDefRoleUser> queryBuOmAdmin(TeSysUser sysUser) {
		//定义BU
		SysDef defBu = sysDefService.getSysDefByCodeName(sysUser.getSbuId(),SysDefTypeCodeName.AI_BU);
		TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
		teSysDefRoleUser.setDefId(defBu.getId());

		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(new ObjectId(SysDefConstants.BUOMADMIN_DEF_ID));
		role.setRoleCodeName(SysDefConstants.BUOMADMIN_CODENAME);
		role.setRoleName(SysDefConstants.BUOMADMIN_DEF_NAME);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);

		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(sysUser.getId());
		roleUser.setLoginName(sysUser.getLoginName());
		roleUser.setUserName(sysUser.getUserName());
		roleUser.setJobCode(sysUser.getJobCode());
		teSysDefRoleUser.setRoleUser(roleUser);

		teSysDefRoleUser.setIsValid(true);

		List<TeSysDefRoleUser> list = sysDefRoleUserDao.findByCondition(teSysDefRoleUser);

		return list;
	}

	/**
	 * 根据角色Id和类型获取用户权限
	 *
	 * @param userIds 用户
	 * @param roleId 角色Id
	 * @return 权限
	 */
	@Override
	public List<TeSysDefRoleUser> queryPrdCtlgIdsByRoleUserId(List<ObjectId> userIds, ObjectId roleId){
		SysDef sysDef = sysDefService.getSysDefById(roleId);
		if(sysDef == null){
			throw BusinessException.initExc("角色的不存在");
		}
		List<ObjectId> roleIds = new ArrayList<>();
		roleIds.add(sysDef.getId());
        return getDefs(userIds, roleIds, SysDefTypeCodeName.PRDCTLG);
	}

	@Override
	public List<OrganizeVo> queryOrganizeName() {
		List<OrganizeVo> result = new ArrayList<>();
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName),SysDefTypeCodeName.OPTION.getValue()));
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId),SysDefConstants.ORGANIZE_NAME));
		List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
		List<ObjectId> defIds = new ArrayList<>();
		for (TeSysDef sysDef : sysDefs) {
			defIds.add(sysDef.getId());
		}
		conds.clear();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), SysDefConstants.RESP));
		conds.add(new DC_I(DFN.sysDefRoleUser__defId, defIds));
		List<TeSysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.findByConds(conds, null);
		for (TeSysDefRoleUser sysDefRoleUser : sysDefRoleUsers) {
			for (TeSysDef sysDef : sysDefs) {
				if (sysDef.getId().equals(sysDefRoleUser.getDefId())){
					OrganizeVo vo = new OrganizeVo();
					vo.setUserId(sysDefRoleUser.getRoleUser().getUserId());
					vo.setDefId(sysDefRoleUser.getDefId());
					vo.setName(sysDef.getDefName()+"/"+sysDefRoleUser.getRoleUser().getUserName());
					result.add(vo);
				}
			}
		}
		return result;
	}

	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndPrdId(
			ObjectId defTypeId,  ObjectId prdId, ObjectId roleId, String userNameCode) {

		List<ObjectId> roleIds = new ArrayList<>(1);
		roleIds.add(roleId);
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DbEqualCondition(DbFieldName.sysDefRoleUser_isValid, true));
		if( null != defTypeId){
			IDbCondition defTypeCond = new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId),defTypeId);
			conds.add(defTypeCond);
		}
		if(null != prdId){
			IDbCondition defCond = new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), prdId);
			conds.add(defCond);
		}
		if(!CollectionUtils.isEmpty(roleIds)){
			IDbCondition roleCond = new DbInCondition<>(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId),roleIds);
			conds.add(roleCond);
		}
		if(!StringUtil.isNull(userNameCode)){
			if(userNameCode.contains("/")){
				IDbCondition userNameCond = new DbLikeCondition(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__userName), userNameCode.split("/")[0]);
				conds.add(userNameCond);
				IDbCondition loginNameCond = new DbLikeCondition(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__loginName), userNameCode.split("/")[1]);
				conds.add(loginNameCond);
			} else {
				List<DbFieldName> fieldNames = new ArrayList<DbFieldName>();
				fieldNames.add(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__loginName));
				fieldNames.add(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysUser__userName));
				IDbCondition userNameCodeCond = new DbFuzzyCondition(fieldNames,userNameCode);
				conds.add(userNameCodeCond);
			}
		}
		return sysDefRoleUserDao.findByConds(conds, null, null);
	}
	
	@Override
	public List<String> queryUserAuthedBuCodes(ObjectId userId){
		List<String> authedBuCodes = new ArrayList<>();
		
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), 
				SysDefTypeConstants.AI_BU_CODENAME));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), 
				StringUtil.toObjectId(SysDefConstants.BUPRJADMIN_DEF_ID)));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), userId));
		
		List<TeSysDefRoleUser> roleUserDefs = sysDefRoleUserDao.findByConds(conds, null);
		List<ObjectId> sbuDefIds = new ArrayList<>();
		for(TeSysDefRoleUser roleUserDef : roleUserDefs) {
			sbuDefIds.add(roleUserDef.getDefId());
		}
		
		if(sbuDefIds.size() > 0) {
			List<TeSysDef> sbuDefs = sysDefDao.getSysDefsByIds(sbuDefIds);
			for(TeSysDef sbuDef : sbuDefs) {
				authedBuCodes.add(sbuDef.getCodeName());
			}
		}
		return authedBuCodes;
	}


	@Override
	public void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList){
		listCCAndBURoleUser(roleIds, userIds, alreadyHaveSysDefRoleUserList, null, null);
	}

	@Override
	public void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList,
									List<ObjectId> prdIdList, List<ObjectId> prdCtlfIdList){
		listCCAndBURoleUser(roleIds, userIds, alreadyHaveSysDefRoleUserList, prdIdList, prdCtlfIdList, false, true);
	}

	@Override
	public void listCCAndBURoleUser(Collection<ObjectId> roleIds, Collection<ObjectId> userIds, List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList,
									Collection<ObjectId> prdIdList, Collection<ObjectId> prdCtlgIdList, Boolean isContainParentPrdCtlg,
									Boolean isAllPrdCtlgAuth){
		// 角色为空、角色中不包含成员角色、人员为空，三种情况不处理
		if(CollectionUtils.isEmpty(roleIds) || !roleIds.contains(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER)
				|| CollectionUtils.isEmpty(userIds)){
			return;
		}

		// 查询传入的用户信息
		List<TeSysUser> sysUserList = listCcOrBuNotNullUser(new ArrayList<>(userIds));
		if(CollectionUtils.isEmpty(sysUserList)){
			return;
		}

		// 组装用户和CC、BU的关系
		// 由于产品目录的CC和BU都配置在ccList字段中，且默认buId和ccId永远不同（如果相同无法区分CC和BU）
		// 所以CC和BU做相同的处理
		List<String> ccOrBuIdList = new ArrayList<>();
		Map<String, List<TeSysUser>> ccOrBuId2UserMap = new HashMap<>();
		for (TeSysUser sysUser : sysUserList){
			// CC
			handleCcBuUserInfo(sysUser.getCcId(), sysUser, ccOrBuIdList, ccOrBuId2UserMap);
			// BU
			handleCcBuUserInfo(sysUser.getSbuId(), sysUser, ccOrBuIdList, ccOrBuId2UserMap);
		}

		if (CollectionUtils.isEmpty(ccOrBuIdList)){
			return;
		}

		// 查询用户所在的CC和BU下，有权限的产品目录
		List<TeSysDef> matchPrdCtlgList = listMatchPrdCtlg(ccOrBuIdList, prdIdList, prdCtlgIdList, isContainParentPrdCtlg);
		if (CollectionUtils.isEmpty(matchPrdCtlgList)){
			return;
		}

		List<TeSysDefRoleUser> needAddRoleUserList = new ArrayList<>();
		Map<ObjectId, List<ObjectId>> needAddRoleUserId2PrdIdsMap = new HashMap<>();
		for (TeSysDef matchPrdCtlg : matchPrdCtlgList){
			List<String> prdCtlgCcList = StringUtil.transIds2List(matchPrdCtlg.getCcList(), ",", String.class);
			if (CollectionUtils.isEmpty(prdCtlgCcList)){
				continue;
			}
			// 该产品目录下配置的CC或者BU下的所有人员（传入的需要检验权限的人员），已去重
			List<TeSysUser> authUserList = new ArrayList<>();
			for (String ccOrBuId : prdCtlgCcList) {
				if(StringUtil.isNull(ccOrBuId)){
					continue;
				}
				List<TeSysUser> ccOrBuUserList = ccOrBuId2UserMap.get(ccOrBuId);
				if(CollectionUtils.isEmpty(ccOrBuUserList)){
					continue;
				}
				for (TeSysUser ccOrBuUser : ccOrBuUserList) {
					if (authUserList.contains(ccOrBuUser)){
						continue;
					}
					authUserList.add(ccOrBuUser);
				}
			}

			// 创建该产品目录下的，有权限用户的临时权限
			ObjectId prdId = matchPrdCtlg.getSrcDef() == null ? null : matchPrdCtlg.getSrcDef().getSrcDefId();
			for (TeSysUser authUser : authUserList) {
				if (BooleanUtils.isTrue(isAllPrdCtlgAuth)){
					needAddRoleUserList.add(createNewSysDefRoleUser(matchPrdCtlg, authUser));
				}else {
					// 如果该目录所在产品下，已经创建过该用户的临时权限，就不再创建
					List<ObjectId> needAddUserId2SrcDefIdList = needAddRoleUserId2PrdIdsMap.getOrDefault(authUser.getId(), new ArrayList<>());
					if (!needAddUserId2SrcDefIdList.contains(prdId)) {
						needAddRoleUserList.add(createNewSysDefRoleUser(matchPrdCtlg, authUser));
						needAddUserId2SrcDefIdList.add(prdId);
						needAddRoleUserId2PrdIdsMap.put(authUser.getId(), needAddUserId2SrcDefIdList);
					}
				}
			}
		}

		// 角色去重
		if (BooleanUtils.isTrue(isAllPrdCtlgAuth)){
			// 传入所有产品目录下的角色权限
			includeSamePrdRoleUser(alreadyHaveSysDefRoleUserList, needAddRoleUserList);
		}else{
			// 已有角色权限中，该用户已经有某个产品的权限，不再传入该产品下其他目录的权限
			excludeSamePrdRoleUser(alreadyHaveSysDefRoleUserList, needAddRoleUserList);
		}
	}

	@Override
	public void getCCRoleUsers(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers){
		listCCAndBURoleUser(roleIds, userIds, teSysDefRoleUsers);
	}

	@Override
	public void getBuAndCcRoleUsers(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers,
									List<ObjectId> prdIds, List<ObjectId> ctlgIds){
		listCCAndBURoleUser(roleIds, userIds, teSysDefRoleUsers, prdIds, ctlgIds);
	}

	public void getCCRoleUsersOld(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers){
		//如果查询的角色中包含member角色，那么就需要通过用户名查看cc表，查看用户是否是cc用户，如果是，那么将cc所在的项目纳入进来
		List<TeSysDefRoleUser> toAdd = new ArrayList<>();
		List<TeSysDefRoleUser> add = new ArrayList<>();
		//同一个用户他cc会不会所在的产品和sysDefRoleUser表中的产品不一致
		//toAddCCUserTool K：用户id V:srcDefId
		Map<String,List<String>> toAddCCUserTool = new HashMap<>();
		if(null != roleIds && roleIds.contains(StringUtil.toObjectId(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER))){
			List<IDbCondition> conds = new ArrayList<>();
			List<ObjectId> userIdList = new ArrayList<>(userIds);

			List<IDbCondition> andConds = new ArrayList<>();
			andConds.add(new DbExistCondition(DFN.sysUser_ccId));
			andConds.add(new DbEqualCondition(DFN.sysUser_ccId,"",true));
			andConds.add(new DbExistCondition(DFN.sysUser_ccName));
			andConds.add(new DbEqualCondition(DFN.sysUser_ccName,"",true));
			andConds.add(new DC_E(DFN.common_isValid,true));
			andConds.add(new DC_I<ObjectId>(DFN.common__id,userIdList));
			conds.add(new DbAndOperator(andConds));
//			List<TeSysUser> sysUserList = sysUserDao.findByConds(conds,null);
			List<DbFieldName> fieldNames = new ArrayList<>();
			fieldNames.add(DFN.common_id);
			fieldNames.add(DFN.common_jobCode);
			fieldNames.add(DFN.common_loginName);
			fieldNames.add(DFN.common_userName);
			fieldNames.add(DFN.sysUser_ccId);
			List<TeSysUser> sysUserList = sysUserDao.findByFieldAndConds(conds,fieldNames,null);
			if(sysUserList.isEmpty()){
				return;
			}
			List<String> ccIdList = new ArrayList<>();
			Map<String,List<TeSysUser>> cc2User = new HashMap<>();
			for(TeSysUser sysUser:sysUserList){
				String ccId = sysUser.getCcId();
				if(StringUtil.isNotNull(ccId)){
					ccIdList.add(ccId);
					List<TeSysUser> cc2UserValueList = cc2User.get(ccId);
					if(!Objects.isNull(cc2UserValueList)){
						boolean isExist = false;
						for (TeSysUser user : cc2UserValueList) {
							if(user.getId().equals(sysUser.getId())){
								isExist = true;
								break;
							}
						}
						if(!isExist){
							cc2UserValueList.add(sysUser);
							cc2User.put(ccId,cc2UserValueList);
						}
					}else{
						cc2UserValueList = new ArrayList<>();
						cc2UserValueList.add(sysUser);
						cc2User.put(ccId,cc2UserValueList);
					}
				}
			}
			conds.clear();
			andConds.clear();
			andConds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), StringUtil.toObjectId(SysDefTypeConstants.PRDCTLG_DEF_ID)));
			andConds.add(new DC_E(DFN.common_isValid,true));
			List<IDbCondition> orConds = new ArrayList<>();

			for(String ccId:ccIdList){
				orConds.add(new DbLikeCondition(DFN.sysDef__ccList,ccId));
			}
			andConds.add(new DbOrOperator(orConds));
			conds.add(new DbAndOperator(andConds));
			List<TeSysDef> sysDefTotal = sysDefDao.findByConds(conds,null);
			for(TeSysDef sysDef:sysDefTotal){
				String ccList = sysDef.getCcList();
				String[] ccListArr = ccList.split(",");
				//该产品目录下所有cc对应的人员
				List<TeSysUser> totalUser = new ArrayList<>();
				for (String ccListContent:ccListArr) {
					if(StringUtil.isNotNull(ccListContent) && null != cc2User.get(ccListContent)){
						totalUser.addAll(cc2User.get(ccListContent));
					}
				}
				for(TeSysUser sysUser:totalUser){
					//srcDef
					TeSrcDef srcDef = sysDef.getSrcDef();
					TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
					TeSysDefRoleUser2SysDef teSysDefRoleUser2SysDef = new TeSysDefRoleUser2SysDef();
					BeanUtils.copyProperties(srcDef,teSysDefRoleUser2SysDef);
					teSysDefRoleUser.setSrcDef(teSysDefRoleUser2SysDef);
					//defType
					TeDefType defType = sysDef.getDefType();
					TeSysDefRoleUser2DefType teSysDefRoleUser2DefType = new TeSysDefRoleUser2DefType();
					BeanUtils.copyProperties(defType,teSysDefRoleUser2DefType);
					teSysDefRoleUser.setDefType(teSysDefRoleUser2DefType);
					//role:设置成 成员
					List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
					TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
					role.setRoleId(StringUtil.toObjectId(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER));
					role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
					role.setRoleName(SysDefConstants.DEF_NAME_MEMBER);
					roleUsers.add(role);
					teSysDefRoleUser.setRole(roleUsers);
					teSysDefRoleUser.setIsValid(true);
					teSysDefRoleUser.setDefId(sysDef.getId());
					TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
					roleUser.setUserId(sysUser.getId());
					roleUser.setJobCode(sysUser.getJobCode());
					roleUser.setLoginName(sysUser.getLoginName());
					roleUser.setUserName(sysUser.getUserName());
					teSysDefRoleUser.setRoleUser(roleUser);
					if(null == toAddCCUserTool.get(sysUser.getId().toHexString())){
						toAddCCUserTool.put(sysUser.getId().toHexString(),
								Arrays.asList(teSysDefRoleUser2SysDef.getSrcDefId().toHexString()));
						toAdd.add(teSysDefRoleUser);
					}else{
						List<String> srcDefIdList = new ArrayList<>(toAddCCUserTool.get(sysUser.getId().toHexString()));
						if(srcDefIdList.contains(teSysDefRoleUser2SysDef.getSrcDefId().toHexString())){
							continue;
						}else{
							srcDefIdList.add(teSysDefRoleUser2SysDef.getSrcDefId().toHexString());
							toAddCCUserTool.put(sysUser.getId().toHexString(), srcDefIdList);
							toAdd.add(teSysDefRoleUser);
						}
					}
				}

			}

			//resultTool K：用户id V:srcDefId
			Map<String,List<String>> resultTool = new HashMap<>();
			for(TeSysDefRoleUser roleUser:teSysDefRoleUsers){
				if(roleUser.getRoleUser() == null || roleUser.getRoleUser().getUserId() == null
						|| roleUser.getSrcDef() == null || roleUser.getSrcDef().getSrcDefId() == null){
					continue;
				}
				if(null == resultTool.get(roleUser.getRoleUser().getUserId().toHexString())){
					resultTool.put(roleUser.getRoleUser().getUserId().toHexString(),Arrays.asList(roleUser.getSrcDef().getSrcDefId().toHexString()));
				}else{
					List<String> srcDefIdList = new ArrayList<>(resultTool.get(roleUser.getRoleUser().getUserId().toHexString()));
					if(srcDefIdList.contains(roleUser.getSrcDef().getSrcDefId().toHexString())){
						continue;
					}else{
						srcDefIdList.add(roleUser.getSrcDef().getSrcDefId().toHexString());
						resultTool.put(roleUser.getRoleUser().getUserId().toHexString(), srcDefIdList);
					}
				}
			}
			for(TeSysDefRoleUser roleUser:toAdd){
				List<String> srcDefIdList = resultTool.get(roleUser.getRoleUser().getUserId().toHexString());
				if(null == srcDefIdList || !srcDefIdList.contains(roleUser.getSrcDef().getSrcDefId().toHexString())){
					teSysDefRoleUsers.add(roleUser);
				}
			}

		}
	}

	public void getBuAndCcRoleUsersOld(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers,
                                    List<ObjectId> prdIds, List<ObjectId> ctlgIds){
		List<TeSysDefRoleUser> toAdd = new ArrayList<>();
		Map<String,List<String>> toAddCCUserTool = new HashMap<>();//toAddCCUserTool K：用户id V:srcDefId
		if(null != roleIds && roleIds.contains(StringUtil.toObjectId(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER))){
			List<ObjectId> userIdList = new ArrayList<>(userIds);
			List<TeSysUser> sysUserList = this.getBuAndCcSysUsersByUserIds(userIdList);//sysUserList是userId所在的所有人员对象
			if(sysUserList.isEmpty()){
				return;
			}
			List<String> buIdList = new ArrayList<>();
			Map<String,List<TeSysUser>> bu2User = new HashMap<>();
			Map<String,List<TeSysUser>> cc2User = new HashMap<>();
			List<String> ccIdList = new ArrayList<>();
			this.getBu2SysUserList(sysUserList,buIdList,bu2User,ccIdList,cc2User);//获取每一个bu下对应的所有人员对象
			List<TeSysDef> sysDefTotal = this.getBu2SysDefs(buIdList, prdIds, ctlgIds);//获取所有cc下对应的所有产品目录
			sysDefTotal.addAll(this.getBu2SysDefs(ccIdList, prdIds, ctlgIds));
			for(TeSysDef sysDef:sysDefTotal){
				String ccList = sysDef.getCcList();
				String[] ccListArr = ccList.split(",");//ccList存sbuId
				List<TeSysUser> totalUser = new ArrayList<>();//该产品目录下所有bu对应的人员
				for (String ccListContent:ccListArr) {
					if(StringUtil.isNotNull(ccListContent) && null != bu2User.get(ccListContent)){
						totalUser.addAll(bu2User.get(ccListContent));
					}
				}
				for (String ccListContent:ccListArr) {//ccList存ccId
					if(StringUtil.isNotNull(ccListContent) && null != cc2User.get(ccListContent)){
						totalUser.addAll(cc2User.get(ccListContent));
					}
				}
				for(TeSysUser sysUser:totalUser){
					TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
					TeSysDefRoleUser2SysDef newSysDefRoleUser2SysDef = new TeSysDefRoleUser2SysDef();
					this.getfakeTeSysDefRoleUser(sysDef, sysUser, teSysDefRoleUser,newSysDefRoleUser2SysDef);
					String srcDefId = newSysDefRoleUser2SysDef.getSrcDefId().toHexString();
					if(null == toAddCCUserTool.get(sysUser.getId().toHexString())){
						toAddCCUserTool.put(sysUser.getId().toHexString(),
								Arrays.asList(srcDefId));
						toAdd.add(teSysDefRoleUser);
					}else{
						List<String> srcDefIdList = new ArrayList<>(toAddCCUserTool.get(sysUser.getId().toHexString()));
						if(srcDefIdList.contains(srcDefId)){
							continue;
						}else{
							srcDefIdList.add(srcDefId);
							toAddCCUserTool.put(sysUser.getId().toHexString(), srcDefIdList);
							toAdd.add(teSysDefRoleUser);
						}
					}
				}
			}
			//resultTool K：用户id V:srcDefId
			Map<String,List<String>> resultTool = new HashMap<>();
			for(TeSysDefRoleUser roleUser:teSysDefRoleUsers){
				if(roleUser.getRoleUser() == null || roleUser.getRoleUser().getUserId() == null
						|| roleUser.getSrcDef() == null || roleUser.getSrcDef().getSrcDefId() == null){
					continue;
				}
				if(null == resultTool.get(roleUser.getRoleUser().getUserId().toHexString())){
					resultTool.put(roleUser.getRoleUser().getUserId().toHexString(),Arrays.asList(roleUser.getSrcDef().getSrcDefId().toHexString()));
				}else{
					List<String> srcDefIdList = new ArrayList<>(resultTool.get(roleUser.getRoleUser().getUserId().toHexString()));
					if(srcDefIdList.contains(roleUser.getSrcDef().getSrcDefId().toHexString())){
						continue;
					}else{
						srcDefIdList.add(roleUser.getSrcDef().getSrcDefId().toHexString());
						resultTool.put(roleUser.getRoleUser().getUserId().toHexString(), srcDefIdList);
					}
				}
			}
			for(TeSysDefRoleUser roleUser:toAdd){//如果没有加cc之前的所有产品目录（该人员userId所对应的所有源项目）没有要添加的项目，就把该项目加进去
				List<String> srcDefIdList = resultTool.get(roleUser.getRoleUser().getUserId().toHexString());
				if(null == srcDefIdList || !srcDefIdList.contains(roleUser.getSrcDef().getSrcDefId().toHexString())){
					teSysDefRoleUsers.add(roleUser);
				}
			}
		}
	}
	
	private List<TeSysUser>  getBuAndCcSysUsersByUserIds(List<ObjectId> userIdList){
		List<IDbCondition> conds = new ArrayList<>();
		List<IDbCondition> andConds = new ArrayList<>();
		andConds.add(new DbExistCondition(DFN.sysUser_sbuId));
		andConds.add(new DbEqualCondition(DFN.sysUser_sbuId,"",true));
		andConds.add(new DbExistCondition(DFN.sysUser_sbuName));
		andConds.add(new DbEqualCondition(DFN.sysUser_sbuName,"",true));
		andConds.add(new DbExistCondition(DFN.sysUser_ccId));
		andConds.add(new DbEqualCondition(DFN.sysUser_ccId,"",true));
		andConds.add(new DbExistCondition(DFN.sysUser_ccName));
		andConds.add(new DbEqualCondition(DFN.sysUser_ccName,"",true));
		andConds.add(new DC_E(DFN.common_isValid,true));
		andConds.add(new DC_I<ObjectId>(DFN.common__id,userIdList));
		conds.add(new DbAndOperator(andConds));
		List<TeSysUser> sysUserList = sysUserDao.findByConds(conds,null);
		return sysUserList;
	}
	private void getBu2SysUserList(List<TeSysUser> sysUserList,List<String> buIdList,Map<String,List<TeSysUser>> bu2User,
			List<String> ccIdList,Map<String,List<TeSysUser>> cc2User){
		for(TeSysUser sysUser:sysUserList){
			String buId = sysUser.getSbuId();
			if(StringUtil.isNotNull(buId)){
				buIdList.add(buId);
				List<TeSysUser> bu2UserValueList = bu2User.get(buId);
				if(!Objects.isNull(bu2UserValueList)){
					boolean isExist = false;
					for (TeSysUser user : bu2UserValueList) {
						if(user.getId().equals(sysUser.getId())){
							isExist = true;
							break;
						}
					}
					if(!isExist){
						bu2UserValueList.add(sysUser);
						bu2User.put(buId,bu2UserValueList);
					}
				}else{
					bu2UserValueList = new ArrayList<>();
					bu2UserValueList.add(sysUser);
					bu2User.put(buId,bu2UserValueList);
				}
			}
			String ccId = sysUser.getCcId();
			if(StringUtil.isNotNull(ccId)){
				ccIdList.add(ccId);
				List<TeSysUser> cc2UserValueList = cc2User.get(ccId);
				if(!Objects.isNull(cc2UserValueList)){
					boolean isExist = false;
					for (TeSysUser user : cc2UserValueList) {
						if(user.getId().equals(sysUser.getId())){
							isExist = true;
							break;
						}
					}
					if(!isExist){
						cc2UserValueList.add(sysUser);
						cc2User.put(ccId,cc2UserValueList);
					}
				}else{
					cc2UserValueList = new ArrayList<>();
					cc2UserValueList.add(sysUser);
					cc2User.put(ccId,cc2UserValueList);
				}
			}
		}
	}
	
	//获取所有bu下对应的所有产品目录
	private List<TeSysDef> getBu2SysDefs(List<String> buIdList, List<ObjectId> prdIds, List<ObjectId> ctlgIds){
		List<IDbCondition> conds = new ArrayList<>();
		List<IDbCondition> andConds = new ArrayList<>();
		andConds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), StringUtil.toObjectId(SysDefTypeConstants.PRDCTLG_DEF_ID)));
		andConds.add(new DC_E(DFN.common_isValid,true));
		if(!CollectionUtils.isEmpty(prdIds)){
            andConds.add(new DC_I<>(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), prdIds));
        }
        if(!CollectionUtils.isEmpty(ctlgIds)){
            andConds.add(new DC_I<>(DFN.common__id, ctlgIds));
        }
		List<IDbCondition> orConds = new ArrayList<>();
		for(String buId:buIdList){
			orConds.add(new DbLikeCondition(DFN.sysDef__ccList,buId));
		}
		andConds.add(new DbOrOperator(orConds));
		conds.add(new DbAndOperator(andConds));
		List<TeSysDef> sysDefTotal = sysDefDao.findByConds(conds,null);
		return sysDefTotal;
	}
	
	private void getfakeTeSysDefRoleUser(TeSysDef sysDef,TeSysUser sysUser,
			TeSysDefRoleUser teSysDefRoleUser,TeSysDefRoleUser2SysDef teSysDefRoleUser2SysDef){
		//srcDef
		TeSrcDef srcDef = sysDef.getSrcDef();
		//TeSysDefRoleUser2SysDef teSysDefRoleUser2SysDef = new TeSysDefRoleUser2SysDef();
		BeanUtils.copyProperties(srcDef,teSysDefRoleUser2SysDef);
		teSysDefRoleUser.setSrcDef(teSysDefRoleUser2SysDef);
		//defType
		TeDefType defType = sysDef.getDefType();
		TeSysDefRoleUser2DefType teSysDefRoleUser2DefType = new TeSysDefRoleUser2DefType();
		BeanUtils.copyProperties(defType,teSysDefRoleUser2DefType);
		teSysDefRoleUser.setDefType(teSysDefRoleUser2DefType);
		//role:设置成 成员
		List<TeSysDefRoleUser2Role> roleUsers = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(StringUtil.toObjectId(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER));
		role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
		role.setRoleName(SysDefConstants.DEF_NAME_MEMBER);
		roleUsers.add(role);
		teSysDefRoleUser.setRole(roleUsers);
		teSysDefRoleUser.setIsValid(true);
		teSysDefRoleUser.setDefId(sysDef.getId());
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(sysUser.getId());
		roleUser.setJobCode(sysUser.getJobCode());
		roleUser.setLoginName(sysUser.getLoginName());
		roleUser.setUserName(sysUser.getUserName());
		teSysDefRoleUser.setRoleUser(roleUser);
	}

	/**
	 * 根据角色、defId、用户ID，查询对应数据
	 *
	 * @param roleId
	 * @param defId
	 * @param prjGroup
	 * @return
	 */
	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleId, ObjectId srcDefId,ObjectId defId, ObjectId userId, SysDefTypeCodeName prjGroup) {
		List<IDbCondition> conds = new ArrayList<>();
		if (!CollectionUtils.isEmpty(roleId)){
			conds.add(new DbInCondition(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId),roleId));
		}
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),prjGroup.getValue()));
		if(srcDefId != null){
			conds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),srcDefId));
		}
		if(defId != null){
			conds.add(new DbEqualCondition(DFN.sysDefRoleUser__defId,defId));
		}
		conds.add(new DbEqualCondition(DFN.common_isValid,true));
		if (userId != null){
			conds.add(new DbEqualCondition(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId),userId));
		}
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser_defType);
		fieldNames.add(DFN.sysDefRoleUser_srcDef);
		fieldNames.add(DFN.sysDefRoleUser__defId);
		fieldNames.add(DFN.sysDefRoleUser__role);
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.common_isValid);
		List<TeSysDefRoleUser> result = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
		return result;
	}

	@Override
	public List<TeSysDefRoleUser> querySysDefRoleUsers(List<ObjectId> defIds, String defTypeCodeName, ObjectId roleId) {
		return querySysDefRoleUsers(defIds, defTypeCodeName, roleId, null, null);
	}

	@Override
	public List<TeSysDefRoleUser> querySysDefRoleUsers(String defTypeCodeName, ObjectId roleId, List<ObjectId> roleUserIds) {
		return querySysDefRoleUsers(null, defTypeCodeName, roleId, roleUserIds, null);
	}

	@Override
	public TeSysDefRoleUser querySysDefRoleUser(ObjectId defId, ObjectId roleId, ObjectId roleUserId) {
		List<TeSysDefRoleUser> sysDefRoleUsers = querySysDefRoleUsers(Arrays.asList(defId), null, roleId, Arrays.asList(roleUserId), null);
		return CollectionUtils.isEmpty(sysDefRoleUsers) ? null : sysDefRoleUsers.get(0);
	}

	@Override
	public List<TeSysDefRoleUser> querySysDefRoleUsers(List<ObjectId> defIds, String defTypeCodeName, ObjectId roleId,
													   List<ObjectId> roleUserIds, ObjectId srcDefId) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
		if (!CollectionUtils.isEmpty(defIds)) {
			conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__defId, defIds));
		}
		if (StringUtil.isNotNull(defTypeCodeName)) {
			conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), defTypeCodeName));
		}
		if (null != roleId) {
			conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), roleId));
		}
		if (!CollectionUtils.isEmpty(roleUserIds)) {
			conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), roleUserIds));
		}
		if (srcDefId != null) {
			conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), srcDefId));
		}
		Assert.isTrue(conds.size() > 1, "查询条件为空");
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common__id);
		fieldNames.add(DFN.sysDefRoleUser__defId);
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		return sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
	}

	@Override
	public void joinInPrj(TeSysDefRoleUser sysDefRoleUser,TeSysUser loginUser) {
		Date enterTime = sysDefRoleUser.getEnterTime();
		String currentTime = com.linkus.base.util.DateUtil.getCurrentTime(com.linkus.base.util.DateUtil.DATE_FORMAT);
		String enterTimeStr = com.linkus.base.util.DateUtil.formatDate2Str(enterTime, com.linkus.base.util.DateUtil.DATE_FORMAT);
		if(enterTime != null && enterTimeStr.compareTo(currentTime)<0 ){
			throw BusinessException.initExc("加入项目日期需大于等于当前日期");
		}
		List<TeSysDefRoleUser2Role> roles = sysDefRoleUser.getRole();
		if (null == roles || roles.isEmpty()){
			throw BusinessException.initExc("角色不能为空");
		}
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		defType.setDefTypeId(new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID));
		defType.setDefTypeName(SysDefTypeConstants.PRJGROUP_DEF_TYPENAME);

		TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
		addUser.setUserId(loginUser.getId());
		addUser.setUserName(loginUser.getUserName());
		addUser.setLoginName(loginUser.getLoginName());
		addUser.setJobCode(loginUser.getJobCode());

		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(loginUser.getId());
		roleUser.setUserName(loginUser.getUserName());
		roleUser.setLoginName(loginUser.getLoginName());
		roleUser.setJobCode(loginUser.getJobCode());

		//sysDefRoleUser.setRole(role);
		sysDefRoleUser.setDefType(defType);
		sysDefRoleUser.setAddTime(new Date());
		sysDefRoleUser.setIsValid(true);
		sysDefRoleUser.setAddUser(addUser);
		sysDefRoleUser.setRoleUser(roleUser);
		sysDefRoleUserDao.save(sysDefRoleUser);
		if(enterTimeStr.compareTo(currentTime) == 0){
			TeClock clock = new TeClock();

			TeIdNameCn defTypeCN = new TeIdNameCn();
			defTypeCN.setCid(defType.getDefTypeId());
			defTypeCN.setName(defType.getDefTypeName());
			defTypeCN.setCodeName(defType.getDefTypeCodeName());

			TeIdNameCn srcDefCN = new TeIdNameCn();
			srcDefCN.setCid(sysDefRoleUser.getSrcDef().getSrcDefId());
			srcDefCN.setName(sysDefRoleUser.getSrcDef().getSrcDefName());
			srcDefCN.setCodeName(sysDefRoleUser.getSrcDef().getSrcDefCodeName());

			TeIdNameCn defCN = new TeIdNameCn();
			ObjectId defId = sysDefRoleUser.getDefId();
			TeSysDef group = sysDefDao.getSysDefById(defId);
			defCN.setCid(group.getId());
			defCN.setName(group.getDefName());
			defCN.setCodeName(group.getCodeName());

			TeSysDefClickLog2User clickLog2User = new TeSysDefClickLog2User();
			clickLog2User.setUserId(loginUser.getId());
			clickLog2User.setUserName(loginUser.getUserName());
			clickLog2User.setJobCode(loginUser.getJobCode());
			clickLog2User.setLoginName(loginUser.getLoginName());


			Map resultMap = new HashMap();
			String actionUrl = "queryClockCnfgInfo.action";
			MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
			List<LinkedHashMap> clockCnfgList = new ArrayList<>();
			try {
				resultMap = restTemplate.postForObject(EurekaClientConstants.LINKUS_PRJ +"clockCnfgCtrl/"+actionUrl,paramMap,HashMap.class);
				clockCnfgList = (ArrayList<LinkedHashMap>)resultMap.get("data");
			} catch (Exception e) {
				clockCnfgList = new ArrayList<>();
				e.printStackTrace();
			}
			Map<String,Integer> prj2allowDeviateMinute = new HashMap<>();
			Map<String,String> prj2onDutyTime = new HashMap<>();
			Map<String,String> prj2offDutyTime = new HashMap<>();
			if(clockCnfgList != null && !clockCnfgList.isEmpty()){
				for (LinkedHashMap map:clockCnfgList){
					LinkedHashMap def = (LinkedHashMap)map.get("def");
					Integer allowDeviateMinute = (Integer) map.get("allowDeviateMinute");
					ArrayList<LinkedHashMap> allowDeviateMinuteList = (ArrayList<LinkedHashMap>) map.get("dutyTimes");


					String codeName = (String)def.get("codeName");
					prj2allowDeviateMinute.put(codeName,allowDeviateMinute);
					for (LinkedHashMap dutyTime:allowDeviateMinuteList){
						prj2onDutyTime.put(codeName,dutyTime.get("onDutyTime").toString());
						prj2offDutyTime.put(codeName,dutyTime.get("offDutyTime").toString());
					}
				}
			}
			clock.setValid(true);
			clock.setYmd(currentTime);
			clock.setIsOutWork(false);
			clock.setDefType(defTypeCN);
			clock.setSrcDef(srcDefCN);
			clock.setDef(defCN);
			clock.setClockUser(clickLog2User);
			clock.setAllowDeviateMinute(prj2allowDeviateMinute.get(srcDefCN.getCodeName()));
			clock.setOnDutyTime(prj2onDutyTime.get(srcDefCN.getCodeName()));
			clock.setOffDutyTime(prj2offDutyTime.get(srcDefCN.getCodeName()));
			clockDao.save(clock);
		}


	}

	@Override
	public void applyExitPrj(Date quitTime,TeSysUser loginUser,ObjectId prjId) {
		//校验退出日期和当前日期
		String currentTime = com.linkus.base.util.DateUtil.getCurrentTime(com.linkus.base.util.DateUtil.DATE_FORMAT);
		String quitTimeStr = com.linkus.base.util.DateUtil.formatDate2Str(quitTime, com.linkus.base.util.DateUtil.DATE_FORMAT);
		if(quitTimeStr != null && quitTimeStr.compareTo(currentTime)>0 ){
			throw BusinessException.initExc("退出日期必须小于等于当前日期");
		}
		//查出对应的数据
		List<IDbCondition> conds = new ArrayList<>();
		List<DbFieldName> fields = new ArrayList<>();
		conds.add(new DbEqualCondition(DFN.common_isValid,true));
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName),loginUser.getLoginName()));
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId),StringUtil.toObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID)));
		fields.add(DFN.sysDefRoleUser_enterTime);
		fields.add(DFN.common_isValid);
		fields.add(DFN.sysDefRoleUser__roleUser);
		fields.add(DFN.sysDefRoleUser_srcDef);
		fields.add(DFN.sysDefRoleUser_defType);
		List<TeSysDefRoleUser> currentUser = sysDefRoleUserDao.findByFieldAndConds(conds, fields);
		//校验退出日期和进入日期
		if(currentUser != null && currentUser.size()>0){
			Date enterTime = new Date();
			enterTime = currentUser.get(0).getEnterTime();
			String enterTimeStr = com.linkus.base.util.DateUtil.formatDate2Str(enterTime, com.linkus.base.util.DateUtil.DATE_FORMAT);
			if(quitTimeStr.compareTo(enterTimeStr)<0 ){
				throw BusinessException.initExc("退出日期必须大于等于进入日期");
			}
		}
		//更新数据
		List<UpdataData> updates = new ArrayList<>();
		updates.add(new UpdataData(DFN.sysDefRoleUser_quitTime,quitTime));
		updates.add(new UpdataData(DFN.common_isValid,false));
		sysDefRoleUserDao.updateByConds(conds,updates);

		conds.clear();
		List<Date> datesBetween = com.linkus.base.util.DateUtil.getDatesBetween(quitTime, new Date());
		List<String> dateBetweenList = new ArrayList<>();
		for (Date d:datesBetween){
			dateBetweenList.add(com.linkus.base.util.DateUtil.formatDate2Str(d, com.linkus.base.util.DateUtil.DATE_FORMAT));
		}
		conds.add(new DC_I<String>(DFN.clock_ymd,dateBetweenList));
		conds.add(new DC_E(DFN.clock_clockUser.dot(DFN.common_loginName),loginUser.getLoginName()));
		conds.add(new DC_E(DFN.clock_srcDef.dot(DFN.common_cid),prjId));
		updates.clear();
		updates.add(new UpdataData(DFN.common_isValid,false));
		clockDao.updateByConds(conds,updates);
	}

	@Override
	public List<Map<String,Object>> selectPrjForNotJoin(TeSysUser loginUser, String fuzzword) {
		//0.前期准备
		List<Map<String,Object>> result = new ArrayList<>();
		List<IDbCondition> conds = new ArrayList<>();
		List<DbFieldName> fields = new ArrayList<>();
		List<IDbCondition> orConds = new ArrayList<>();

		Map resultMap = new HashMap();
		List<String> prjIdList = new ArrayList<>();
		List<ObjectId> prjIdObjList = new ArrayList<>();

		//1.查询当前用户没有加入的项目,这些项目中要剔除没有设置考勤的项目
		String actionUrl = "queryClockCnfgPrjInfo.action";
		MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
		try {
			resultMap = restTemplate.postForObject(EurekaClientConstants.LINKUS_PRJ +"clockCnfgCtrl/"+actionUrl,paramMap,HashMap.class);
			prjIdList = (ArrayList)resultMap.get("data");
		} catch (Exception e) {
			prjIdList = new ArrayList<>();
			e.printStackTrace();
		}
		prjIdObjList = prjIdList.stream().map(s -> StringUtil.toObjectId(s)).collect(Collectors.toList());

		conds.clear();
		fields.clear();
		if(StringUtil.isNotNull(fuzzword)){
			orConds.add(new DbLikeCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefCn),fuzzword));
			orConds.add(new DbLikeCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefName),fuzzword));
			conds.add(new DbOrOperator(orConds));
		}
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId),new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID)));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName),loginUser.getLoginName(),true));
		conds.add(new DC_I(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjIdObjList));

		fields.add(DFN.common_isValid);
		fields.add(DFN.sysDefRoleUser_defType);
		fields.add(DFN.sysDefRoleUser__roleUser);
		fields.add(DFN.sysDefRoleUser_srcDef);
		//没有加入的项目
		List<TeSysDefRoleUser> notJoinPrjList = sysDefRoleUserDao.findByFieldAndConds(conds, fields);
		//查询已经加入申请加入的项目
		conds.clear();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId),new ObjectId(SysDefTypeConstants.PRJGROUP_DEF_ID)));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName),loginUser.getLoginName()));
		List<TeSysDefRoleUser> joinPrjList = sysDefRoleUserDao.findByFieldAndConds(conds, fields);
		Set<ObjectId> joinPrjIdSet = joinPrjList.stream().map(TeSysDefRoleUser::getSrcDef).map(TeSysDefRoleUser2SysDef::getSrcDefId).collect(Collectors.toSet());

		if(notJoinPrjList.isEmpty()){
			return null;
		}
		//获取已经放入result的prjId集合
		List<ObjectId> existPrj = new ArrayList<>();
		for (TeSysDefRoleUser prjInfo:notJoinPrjList){
			ObjectId prjId = prjInfo.getSrcDef().getSrcDefId();
			String prjName = prjInfo.getSrcDef().getSrcDefName();
			String prjCodeName = prjInfo.getSrcDef().getSrcDefCodeName();
			if(!existPrj.contains(prjId) && !joinPrjIdSet.contains(prjId)){
				Map<String,Object> map = new HashMap<>();
				map.put("prjId",prjId.toHexString());
				map.put("prjName",prjName);
				map.put("prjCode",prjCodeName);
				map.put("isPmOrPrjAdmin",false);
				result.add(map);
				existPrj.add(prjId);
			}else{
				continue;
			}
		}
		if(result.isEmpty()){
			return null;
		}
		//再次查询这些项目是否是项目管理员
		List<ObjectId> userIds = new ArrayList<>();
		List<ObjectId> roleIds = new ArrayList<>();
		userIds.add(loginUser.getId());
		roleIds.add(StringUtil.toObjectId(SysDefConstants.PRJADMIN_DEF_ID));
		roleIds.add(StringUtil.toObjectId(SysDefConstants.PM_DEF_ID));
		List<TeSysDefRoleUser> sysUserRole = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, existPrj, null);
		Set<String> isPmOrPrjAdminPrjList = new HashSet<>();
		for (TeSysDefRoleUser user:sysUserRole){
			isPmOrPrjAdminPrjList.add(user.getDefId().toHexString());
		}
		for (Map<String,Object> map :result){
			if(isPmOrPrjAdminPrjList.contains(map.get("prjId"))){
				map.put("isPmOrPrjAdmin",true);
			}
		}
		return result;
	}

	/**
	 * 如果已经在某个项目上，则需提供修改功能，即修改进入日期。
	 * 修改时，新的进入日期必须大于老的进入日期，提交后，系统提示“如确认修改，系统将自动清除自【老的进入日期】至【新的进入日期】（不含）期间的打卡记录！”。
	 * 修改时，还可以改项目小组。
	 * 确认提交后，即更新“sysDefRoleUser”中该记录的如下信息：
	 * enterTime：为新的进入日期
	 * def：为所选的项目小组的信息
	 * 同时，更新“sysDefRoleUser”中isValid为true下的且clockUser.userId为该用户ID下的且srcDef.srcDefId为该项目ID下的且ymd大于等于老的进入日期且小于新的进入日期下的该记录的如下信息：
	 * isValid：为false
	 * @param enterTime
	 * @param groupId
	 * @param loginName
	 */
	@Override
	public void updateEnterTimeAndGroup(String loginName, Date enterTime, ObjectId groupId, ObjectId prjId, ObjectId defPara,List<ObjectId> roleIds) {
		//0.前期准备
		List<IDbCondition> conds = new ArrayList<>();
		List<DbFieldName> fields = new ArrayList<>();
		List<UpdataData>  update = new ArrayList<>();
		List<String> dateBetween = new ArrayList<>();
		Date currentDate = new Date();
		currentDate = com.linkus.base.util.DateUtil.parseDate(com.linkus.base.util.DateUtil.formatDate2Str(currentDate,
				com.linkus.base.util.DateUtil.DATE_FORMAT), com.linkus.base.util.DateUtil.DATE_FORMAT);

		//0.根据办公方式ID查询办公方式
		TeSysDef workType = sysDefDao.findById(defPara);
		TeIdNameCn workTypePara = new TeIdNameCn();
		workTypePara.setCodeName(workType.getCodeName());
		workTypePara.setCid(workType.getId());
		workTypePara.setName(workType.getDefName());

		List<TeSysDef> roleDefs = null;
		if (roleIds != null){
			roleDefs = sysDefService.getTeSysDefsByIds(roleIds);
			if (roleDefs == null){
				throw BusinessException.initExc("所选角色不存在，请联系管理员");
			}
		}

		//1.查询出已经存在的这条记录，并对加入时间进行判断
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser_loginName),loginName));
		conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
		conds.add(new DbEqualCondition(DFN.common_isValid,true));
		fields.add(DFN.sysDefRoleUser_enterTime);
		fields.add(DFN.sysDefRoleUser__defId);
		fields.add(DFN.sysDefRoleUser__roleUser);
		List<TeSysDefRoleUser> recordList = sysDefRoleUserDao.findByFieldAndConds(conds, fields);
		if(null != recordList && recordList.size()>0){
			Date preEnterTime = recordList.get(0).getEnterTime();
//			if(com.linkus.base.util.DateUtil.compareDate(preEnterTime,enterTime)>=0){
//				throw BusinessException.initExc("新的进入日期必须大于老的进入日期");
//			}
			if(com.linkus.base.util.DateUtil.compareDate(currentDate,enterTime)>0){
				throw BusinessException.initExc("加入项目日期需大于等于当前日期");
			}

			//2.修改用户信息
			if (groupId != null){
				update.add(new UpdataData(DFN.sysDefRoleUser__defId,groupId));
			}
			update.add(new UpdataData(DFN.sysDefRoleUser_enterTime,enterTime));
			update.add(new UpdataData(DFN.sysDefRoleUser__defPara,workTypePara));
			if (!roleDefs.isEmpty()){
				List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
				for (TeSysDef roleDef : roleDefs) {
					TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
					role.setRoleId(roleDef.getId());
					role.setRoleName(roleDef.getDefName());
					role.setRoleCodeName(roleDef.getCodeName());
					roles.add(role);
				}
				update.add(new UpdataData(DFN.sysDefRoleUser__role,roles));
			}
			sysDefRoleUserDao.updateByConds(conds, update);
			//3.将打卡记录中大于等于老的进入日期小于新的进入日期下的记录置为false
			//计算preEnterTime和enterTime之间的天
			List<Date> datesBetween = com.linkus.base.util.DateUtil.getDatesBetween(preEnterTime, enterTime);
			for (Date date:datesBetween){
				if(!date.equals(enterTime)){
					String s = com.linkus.base.util.DateUtil.formatDate2Str(date, com.linkus.base.util.DateUtil.DATE_FORMAT);
					dateBetween.add(s);
				}

			}
			conds.clear();
			update.clear();
			conds.add(new DbEqualCondition(DFN.clock_srcDef.dot(DFN.common_cid),prjId));
			conds.add(new DbInCondition<String>(DFN.clock_ymd,dateBetween));
			update.add(new UpdataData(DFN.common_isValid,false));
			clockDao.updateByConds(conds,update);
		}
	}
	/**
	 * 校验当前登陆人是否是产品管理员
	 */
	@Override
	public boolean checkIsPrdAdmin(TeSysUser loginUser) {
		//查询产品管理员
		//当前登陆人
		List<ObjectId> userIds = new ArrayList<>();
		userIds.add(loginUser.getId());
		//管理员
		List<ObjectId> roleIds = new ArrayList<>();
		roleIds.add(SysDefConstants.DEF_ID_ROLE_PRD_ADMIN);
		//负责人
		roleIds.add(SysDefConstants.DEF_ID_ROLE_PRD_RESP);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds,null,SysDefTypeCodeName.PRDCTLG);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 校验当前登陆人是否是项目管理员
	 */
	@Override
	public boolean checkIsPrjAdmin(ObjectId prjId, TeSysUser loginUser) {
		//当前登陆人
		List<ObjectId> userIds = new ArrayList<>();
		userIds.add(loginUser.getId());
		//管理员
		List<ObjectId> roleIds = new ArrayList<>();
		roleIds.add(StringUtil.toObjectId(SysDefConstants.PRJADMIN_DEF_ID));
		List<ObjectId> defIds = new ArrayList<>();
		defIds.add(prjId);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds,defIds,SysDefTypeCodeName.PRJ);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 校验当前登陆人是否是该项目的成员
	 */
	@Override
	public boolean checkIsPrjMember(ObjectId prjId, TeSysUser loginUser) {
		TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeConstants.PRJGROUP_CODENAME);
		sysDefRoleUser.setDefType(defType);

		/*TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
		sysDefRoleUser.setRole(role);*/

		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(loginUser.getId());
		sysDefRoleUser.setRoleUser(roleUser);

		TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
		srcDef.setSrcDefId(prjId);
		sysDefRoleUser.setSrcDef(srcDef);

		sysDefRoleUser.setIsValid(true);
		List<TeSysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.findTeSysDefRoleUserByCondition(sysDefRoleUser);
		if (CollectionUtils.isEmpty(sysDefRoleUsers)){
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 校验当前登陆人是否是该部门的成员
	 */
	@Override
	public TeSysDef checkIsDeptMember(ObjectId deptId, TeSysUser loginUser) {
		//查出部门信息
		TeSysDef sysDef = sysDefService.getTeSysDefById(deptId);
		if (sysDef == null){
			throw new BaseException("当前部门不存在");
		}
		TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
		TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
		defType.setDefTypeCodeName(SysDefTypeConstants.DEPT_DEF_CODENAME);
		sysDefRoleUser.setDefType(defType);

		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(loginUser.getId());
		sysDefRoleUser.setRoleUser(roleUser);

		sysDefRoleUser.setDefId(deptId);

		sysDefRoleUser.setIsValid(true);
		List<TeSysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.findTeSysDefRoleUserByCondition(sysDefRoleUser);

		//判断是有效成员还是无效成员
		if (CollectionUtils.isEmpty(sysDefRoleUsers)){
			sysDef.setIsValid(false);
		}
		return sysDef;

	}

	/**
	 * 查询当前登陆人所有的部门
	 */
	@Override
	public List<SysDef> queryAllDept(TeSysUser loginUser) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.DEPT_DEF_CODENAME));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId),loginUser.getId()));
		conds.add(new DC_E(DFN.sysDefRoleUser__defId,null,true));

		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysDefRoleUser__defId);
		fieldNameList.add(DFN.common_isValid);
		//查询所有部门
		List<TeSysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.findByFieldAndConds(conds,fieldNameList);
		if (CollectionUtils.isEmpty(sysDefRoleUsers)){
			return null;
		}
		//有效的部门id
        List<TeSysDefRoleUser> isValidDef = sysDefRoleUsers.stream().filter(TeSysDefRoleUser::getIsValid).collect(Collectors.toList());
        List<ObjectId> isValidDefIds = null;
		if (!CollectionUtils.isEmpty(isValidDef)){
            isValidDefIds = isValidDef.stream().map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
        }
        //部门id
		List<ObjectId> deptIds = sysDefRoleUsers.stream().map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
		List<SysDef> deptList = sysDefService.getSysDefsByIds(deptIds);
		if (!CollectionUtils.isEmpty(deptList)){
		    for (SysDef dept : deptList){
		        if (CollectionUtils.isEmpty(isValidDefIds) || !isValidDefIds.contains(dept.getId())){
		            dept.setIsValid(false);
                }
            }
        }
		return deptList;
	}

	/**
	 * 校验当前登陆人是否是部门责任人
	 */
	@Override
	public boolean checkIsDeptAdmin(ObjectId deptId, TeSysUser loginUser) {
		//当前登陆人
		List<ObjectId> userIds = new ArrayList<>();
		userIds.add(loginUser.getId());
		//管理员
		List<ObjectId> roleIds = new ArrayList<>();
		roleIds.add(StringUtil.toObjectId(SysOmsConstants.ROLE_RESP_OBJID));
		List<ObjectId> defIds = new ArrayList<>();
		defIds.add(deptId);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds,defIds,SysDefTypeCodeName.DEPT);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return false;
		}else {
			return true;
		}
	}
	//查询bu项目管理员下的所有bu信息
	@Override
	public List<SysDef> queryAllBuByBuPrjAdmin(TeSysUser loginUser) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),
				SysDefTypeConstants.AI_BU_CODENAME));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId),
				StringUtil.toObjectId(SysDefConstants.BUPRJADMIN_DEF_ID)));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), loginUser.getId()));
		conds.add(new DC_E(DFN.sysDefRoleUser__defId,null,true));

		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysDefRoleUser__defId);
		List<TeSysDefRoleUser> roleUserDefs = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNameList);
		if (CollectionUtils.isEmpty(roleUserDefs)){
			return null;
		}
		List<ObjectId> buIdList = roleUserDefs.stream().map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
		return sysDefService.getSysDefsByIds(buIdList);
	}

    @Override
    public Boolean checkIsPrjManagerAndAdmin(ObjectId prjId, TeSysUser loginUser) {
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //管理员
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(StringUtil.toObjectId(SysDefConstants.PM_DEF_ID));
        roleIds.add(StringUtil.toObjectId(SysDefConstants.PRJADMIN_DEF_ID));
        List<ObjectId> defIds = new ArrayList<>();
        defIds.add(prjId);
        List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds,SysDefTypeCodeName.PRJ);
        if (CollectionUtils.isEmpty(sysDefRoleUserList)){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public Boolean checkIsBuPrjBudgetAdmin(ObjectId buId, TeSysUser loginUser){
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //管理员
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
        List<ObjectId> defIds = new ArrayList<>();
        defIds.add(buId);
        List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds,SysDefTypeCodeName.AI_BU);
        if (CollectionUtils.isEmpty(sysDefRoleUserList)){
            return false;
        }else {
            return true;
        }
    }

    @Override
	public List<SysDef> getBuByUserId(ObjectId userId){
		List<SysDef> sysDefs = new ArrayList<>();
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId), SysDefConstants.DEF_TYPE_ID_AI_BU));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), SysDefConstants.BUOMADMIN_DEF_ID));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), userId));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser__defId);
		List<TeSysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
		if(CollectionUtils.isEmpty(sysDefRoleUsers)){
			return sysDefs;
		}
		List<ObjectId> defIds = sysDefRoleUsers.stream().map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(defIds)){
			return sysDefs;
		}
		conds.clear();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_I(DFN.sysDef_id,  defIds ));
		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds,Sort.by(Sort.Direction.DESC, DFN.sysDef__defName.n()));
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
        return sysDefs;
	}

	@Override
	public Boolean checkIsPrjEffectEvalutionAdmin(ObjectId buId, TeSysUser loginUser) {
		List<ObjectId> userIds = new ArrayList<>();
		userIds.add(loginUser.getId());
		//管理员
		List<ObjectId> roleIds = new ArrayList<>();
		roleIds.add(StringUtil.toObjectId(SysDefConstants.EVALUATION_MANAGER_DEF_ID));
		List<ObjectId> defIds = new ArrayList<>();
		defIds.add(buId);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds,SysDefTypeCodeName.AI_BU);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return false;
		}else {
			return true;
		}
	}

    @Override
    public Boolean checkIsBuAdmin(ObjectId buId, TeSysUser loginUser){
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //管理员
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(SysDefConstants.DEF_ID_ROLE_ADMIN);
        List<ObjectId> defIds = new ArrayList<>();
        defIds.add(buId);
        List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, defIds,SysDefTypeCodeName.AI_BU);
        if (CollectionUtils.isEmpty(sysDefRoleUserList)){
            return false;
        }else {
            return true;
        }
    }

	@Override
	public void deleteByIds(List<ObjectId> ids) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_I<>(DFN.common__id, ids));
		List<UpdataData> updataDataList = new ArrayList<>();
		updataDataList.add(new UpdataData(DFN.common_isValid, false));
		sysDefRoleUserDao.updateByConds(conds, updataDataList);
	}

	@Override
	public void bulkUpdateTeamEmpRole(List<ObjectId> empIdList, ObjectId prjId, ObjectId prjGroupId, TeIdNameCn updateRole) {
		if (null == empIdList || empIdList.isEmpty() || null == prjId || null == prjGroupId || null == updateRole) {
			throw new BaseException("批量更新人员角色组记录，入参为空！");
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), SysDefTypeCodeName.PRJ_GROUP.getValue()));
		conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), prjId));
		conds.add(new DC_E(DFN.sysDefRoleUser__defId, prjGroupId));
		conds.add(new DC_I<>(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), empIdList));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common__id);
		fieldNames.add(DFN.sysDefRoleUser_srcDef);
		fieldNames.add(DFN.sysDefRoleUser__defId);
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.sysDefRoleUser__role);
		List<TeSysDefRoleUser> roleUserList = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
		if (null == roleUserList || roleUserList.isEmpty()) {
			return;
		}
		// 更新角色
		for (TeSysDefRoleUser def : roleUserList) {
			List<TeSysDefRoleUser2Role> roleList = def.getRole();
			if (null == roleList || roleList.isEmpty()) {
				continue;
			}
			boolean isPm = false;
			for (TeSysDefRoleUser2Role role : roleList) {
				// 排除项目经理
				if (SysDefConstants.PM_CODENAME.equals(role.getRoleCodeName())) {
					isPm = true;
					break;
				}
			}
			if (isPm) {
				continue;
			}
			List<TeSysDefRoleUser2Role> newRoleList = new ArrayList<>();
			TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
			role.setRoleId(updateRole.getCid());
			role.setRoleName(updateRole.getName());
			role.setRoleCodeName(updateRole.getCodeName());
			newRoleList.add(role);
			def.setRole(newRoleList);
		}

		List<DbFieldName> updateFields = new ArrayList<>();
		updateFields.add(DFN.sysDefRoleUser__role);
		sysDefRoleUserDao.updateByFileds(roleUserList, updateFields, null, DFN.common_id.n());
	}


	@Override
	public List<TeSysDef> getSysDefByCndtItemCids(String defTypeCode, String srcDefCode,List<ObjectId> cndtItemCids,Boolean auth,TeSysUser loginUser) {
		return this.getSysDefByCndtItemCids(defTypeCode, srcDefCode, cndtItemCids, null, auth, loginUser);
	}

    @Override
    public List<TeSysDef> getSysDefByCndtItemCids(String defTypeCode, String srcDefCode,List<ObjectId> cndtItemCids, List<ObjectId> itemIds, Boolean auth,TeSysUser loginUser) {
        List<ObjectId> bigRegionIds = new ArrayList<>();
        List<ObjectId> regionIds = new ArrayList<>();
        List<ObjectId> engDeptIds = new ArrayList<>();
        List<ObjectId> provIds = null;
        //表示查询省份运营管理员有权限的大区，区域，省份，根据省份倒推
        if (BooleanUtils.isTrue(auth)){
            //查询省份运营管理员有权限的省份
            List<ObjectId> roleIds = new ArrayList<>();
            roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
            List<ObjectId> userIds = new ArrayList<>();
            userIds.add(loginUser.getId());
            List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.ABP_PROV);
            if (CollectionUtils.isEmpty(operateAdmins)) {
                throw BusinessException.initExc("省份为空");
            }
            //获取省份id
            provIds = operateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(provIds)){
                throw BusinessException.initExc("省份为空");
            }
            //查询省份
            List<SysDef> provList = sysDefService.getSysDefsByIds(provIds);
            if (CollectionUtils.isEmpty(provList)){
                throw BusinessException.initExc("省份为空");
            }
            //获取大区和区域
            for (SysDef prov : provList){
                List<TeIdNameCn> cndtItems = prov.getCndtItems();
                if (!CollectionUtils.isEmpty(cndtItems)){
                    for (TeIdNameCn cn : cndtItems){
                        if (cn.getCid() != null){
                            //大区
                            if (SysDefTypeConstants.ABP_BIG_REGION_CODENAME.equals(cn.getCodeName())){
                                bigRegionIds.add(cn.getCid());
                            }
							//工程部
							if (SysDefTypeConstants.ABP_ENG_DEPT_CODENAME.equals(cn.getCodeName())){
								engDeptIds.add(cn.getCid());
							}
                            //区域
                            if (SysDefTypeConstants.ABP_REGION_CODENAME.equals(cn.getCodeName())){
                                regionIds.add(cn.getCid());
                            }
                        }
                    }
                }
            }
        }
        List<IDbCondition> conds = new ArrayList<>();
        if (BooleanUtils.isTrue(auth)){
            if (CollectionUtils.isEmpty(bigRegionIds)){
                throw BusinessException.initExc("大区为空");
            }
            if (CollectionUtils.isEmpty(regionIds)){
                throw BusinessException.initExc("区域为空");
            }
            if (SysDefTypeConstants.ABP_BIG_REGION_CODENAME.equals(defTypeCode)){
                conds.add(new DC_I<ObjectId>(DFN.common__id,bigRegionIds));
            }
            if (SysDefTypeConstants.ABP_REGION_CODENAME.equals(defTypeCode)){
                conds.add(new DC_I<ObjectId>(DFN.common__id,regionIds));
            }
			if (SysDefTypeConstants.ABP_ENG_DEPT_CODENAME.equals(defTypeCode)){
				conds.add(new DC_I<ObjectId>(DFN.common__id,engDeptIds));
			}
            if (SysDefTypeConstants.ABP_PROV_CODENAME.equals(defTypeCode)){
                conds.add(new DC_I<ObjectId>(DFN.common__id,provIds));
            }
        }
        if(StringUtil.isNotNull(defTypeCode)){
            conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCode));
        }
        if(StringUtil.isNotNull(srcDefCode)){
            conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), srcDefCode));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cndtItemCids)){
            conds.add(new DC_I<ObjectId>(DFN.sysDef_cndtItems.dot(DFN.common_cid), cndtItemCids));
        }else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemIds)){
            conds.add(new DC_ALL<>(DFN.sysDef_cndtItems.dot(DFN.common_cid), itemIds));
        }
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.ASC, DFN.sysDef__defName.n()));
        return teSysDefs;
    }

	@Override
	public List<ItfSysDefRoleUser> queryData(ItfSysDefRoleUserQuery req) {
		List<IDbCondition> conds = new ArrayList<>();
		if (req.getIsValid()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser_isValid, req.getIsValid()));
		}
		if (req.getDefTypeId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId),req.getDefTypeId()));
		}
		if (req.getRoleId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), req.getRoleId()));
		}
		if (req.getRoleUserId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), req.getRoleUserId()));;
		}
		if (CollectionUtils.isEmpty(conds)){
			return new ArrayList<>();
		}
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.sysDefRoleUser__defId);

		List<TeSysDefRoleUser> sysDefRoleUsers =sysDefRoleUserDao.findByFieldAndConds(conds,fieldNames);
		List<ItfSysDefRoleUser> list = new ArrayList<>();
		for (TeSysDefRoleUser sysDefRoleUser : sysDefRoleUsers) {
			ItfSysDefRoleUser user = new ItfSysDefRoleUser();
			BeanUtils.copyProperties(sysDefRoleUser,user);
			if (sysDefRoleUser.getRoleUser()!=null){
				TeUser teUser = new TeUser();
				teUser.setUserId(sysDefRoleUser.getRoleUser().getUserId());
				teUser.setUserName(sysDefRoleUser.getRoleUser().getUserName());
				teUser.setJobCode(sysDefRoleUser.getRoleUser().getJobCode());
				teUser.setLoginName(sysDefRoleUser.getRoleUser().getLoginName());
				user.setRoleUser(teUser);
			}

			if (sysDefRoleUser.getAddUser()!=null){
				TeUser teUser = new TeUser();
				teUser.setUserId(sysDefRoleUser.getAddUser().getUserId());
				teUser.setUserName(sysDefRoleUser.getAddUser().getUserName());
				teUser.setJobCode(sysDefRoleUser.getAddUser().getJobCode());
				teUser.setLoginName(sysDefRoleUser.getAddUser().getLoginName());
				user.setAddUser(teUser);
			}
			list.add(user);
		}
		return list;
	}

	@Override
	public Integer updateIsValid(ItfSysDefRoleUserQuery req, boolean flag) {
		List<IDbCondition> conds = new ArrayList<>();
		if (req.getIsValid()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser_isValid, req.getIsValid()));
		}
		if (req.getDefTypeId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeId),req.getDefTypeId()));
		}
		if (req.getRoleId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), req.getRoleId()));
		}
		if (req.getRoleUserId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), req.getRoleUserId()));
		}
		if (req.getDefId()!=null){
			conds.add(new DC_E(DFN.sysDefRoleUser__defId, req.getDefId()));
		}
		List<UpdataData> updataDataList = new ArrayList<>();
		updataDataList.add(new UpdataData(DFN.sysDefRoleUser_isValid,flag));
		return updateByConds(conds,updataDataList);
	}


	@Override
	public void save(ItfSysDefRoleUser user) {
		if (user==null){
			return;
		}
		TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
		BeanUtils.copyProperties(user,sysDefRoleUser);
		if (user.getRoleUser()!=null){
			TeSysDefRoleUser2User teUser = new TeSysDefRoleUser2User();
			teUser.setUserId(user.getRoleUser().getUserId());
			teUser.setUserName(user.getRoleUser().getUserName());
			teUser.setJobCode(user.getRoleUser().getJobCode());
			teUser.setLoginName(user.getRoleUser().getLoginName());
			sysDefRoleUser.setRoleUser(teUser);
		}

		if (user.getAddUser()!=null){
			TeSysDefRoleUser2User teUser = new TeSysDefRoleUser2User();
			teUser.setUserId(user.getAddUser().getUserId());
			teUser.setUserName(user.getAddUser().getUserName());
			teUser.setJobCode(user.getAddUser().getJobCode());
			teUser.setLoginName(user.getAddUser().getLoginName());
			sysDefRoleUser.setAddUser(teUser);
		}

		if (user.getDefType()!=null){
			TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
			defType.setDefTypeId(user.getDefType().getDefTypeId());
			defType.setDefTypeName(user.getDefType().getDefTypeName());
			defType.setDefTypeCodeName(user.getDefType().getDefTypeCodeName());
			sysDefRoleUser.setDefType(defType);
		}
		insert(sysDefRoleUser);
	}

	private void includeSamePrdRoleUser(List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList, List<TeSysDefRoleUser> needAddRoleUserList) {
		// 新增角色人员为空，不处理
		if (CollectionUtils.isEmpty(needAddRoleUserList)){
			return;
		}
		alreadyHaveSysDefRoleUserList = alreadyHaveSysDefRoleUserList == null ? new ArrayList<>() : alreadyHaveSysDefRoleUserList;

		// 已存在的人员权限列表
		Set<String> roleUserFlagSet = new HashSet();
		for (TeSysDefRoleUser sysDefRoleUser : alreadyHaveSysDefRoleUserList) {
			if (sysDefRoleUser.getSrcDef() == null || sysDefRoleUser.getSrcDef().getSrcDefId() == null
					|| CollectionUtils.isEmpty(sysDefRoleUser.getRole()) || sysDefRoleUser.getDefId() == null
					|| sysDefRoleUser.getRoleUser() == null || sysDefRoleUser.getRoleUser().getUserId() == null){
				continue;
			}
			for (TeSysDefRoleUser2Role role : sysDefRoleUser.getRole()) {
				if (role.getRoleId() == null){
					continue;
				}
                String roleUserFlag = sysDefRoleUser.getRoleUser().getUserId().toHexString() + "_" +
                        sysDefRoleUser.getSrcDef().getSrcDefId().toHexString() + "_" +
                        sysDefRoleUser.getDefId().toHexString() + "_" +
                        role.getRoleId().toHexString();
				roleUserFlagSet.add(roleUserFlag);
			}
		}

		List<TeSysDefRoleUser> newRoleUserList = new ArrayList();
		for (TeSysDefRoleUser  needAddRoleUser : needAddRoleUserList) {
			if (needAddRoleUser.getSrcDef() == null || needAddRoleUser.getSrcDef().getSrcDefId() == null
					|| CollectionUtils.isEmpty(needAddRoleUser.getRole()) || needAddRoleUser.getDefId() == null
					|| needAddRoleUser.getRoleUser() == null || needAddRoleUser.getRoleUser().getUserId() == null){
				continue;
			}
			boolean isExist = false;
			// 对角色进行遍历，如果一个权限已经存在，则不添加
			for (TeSysDefRoleUser2Role role : needAddRoleUser.getRole()) {
				if (role.getRoleId() == null){
					continue;
				}
                String roleUserFlag = needAddRoleUser.getRoleUser().getUserId().toHexString() + "_" +
                        needAddRoleUser.getSrcDef().getSrcDefId().toHexString() + "_" +
                        needAddRoleUser.getDefId().toHexString() + "_" +
                        role.getRoleId().toHexString();
				if (roleUserFlagSet.contains(roleUserFlag)){
					isExist = true;
					break;
				}
			}
			if (!isExist) {
				newRoleUserList.add(needAddRoleUser);
			}
		}
		if (!CollectionUtils.isEmpty(newRoleUserList)) {
			alreadyHaveSysDefRoleUserList.addAll(newRoleUserList);
		}
	}

	private void excludeSamePrdRoleUser(List<TeSysDefRoleUser> alreadyHaveSysDefRoleUserList, List<TeSysDefRoleUser> needAddRoleUserList) {
		Map<ObjectId, Set<ObjectId>> alreadyHaveRoleUserId2RoleSrcDefIdsMap = new HashMap<>();
		for(TeSysDefRoleUser roleUser : alreadyHaveSysDefRoleUserList){
			if(roleUser.getRoleUser() == null || roleUser.getRoleUser().getUserId() == null
					|| roleUser.getSrcDef() == null || roleUser.getSrcDef().getSrcDefId() == null){
				continue;
			}
			ObjectId roleUserId = roleUser.getRoleUser().getUserId();
			ObjectId roleSrcDefId = roleUser.getSrcDef().getSrcDefId();
			Set<ObjectId> roleSrcDefIdSet = alreadyHaveRoleUserId2RoleSrcDefIdsMap.getOrDefault(roleUserId, new HashSet<>());
			roleSrcDefIdSet.add(roleSrcDefId);
			alreadyHaveRoleUserId2RoleSrcDefIdsMap.put(roleUserId, roleSrcDefIdSet);
		}

		// 如果传入的角色人员中，该用户已经拥有该来源（eg：产品）的权限，不再创建临时权限
		for(TeSysDefRoleUser needAddRoleUser : needAddRoleUserList){
			if (needAddRoleUser.getRoleUser() == null || needAddRoleUser.getRoleUser().getUserId() == null
					|| needAddRoleUser.getSrcDef() == null || needAddRoleUser.getSrcDef().getSrcDefId() == null){
				continue;
			}
			Set<ObjectId> srcDefIdList = alreadyHaveRoleUserId2RoleSrcDefIdsMap.get(needAddRoleUser.getRoleUser().getUserId());
			if(!CollectionUtils.isEmpty(srcDefIdList) && srcDefIdList.contains(needAddRoleUser.getSrcDef().getSrcDefId())){
				continue;
			}
			alreadyHaveSysDefRoleUserList.add(needAddRoleUser);
		}
	}

	private List<ObjectId> listParentPrdCtlgId(List<ObjectId> prdCtlgIdList) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), StringUtil.toObjectId(SysDefTypeConstants.PRDCTLG_DEF_ID)));
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_I<>(DFN.common__id, prdCtlgIdList));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common__id);
		List<TeSysDef> prdCtlgList = sysDefDao.findByFieldAndConds(conds, fieldNames);
		if (CollectionUtils.isEmpty(prdCtlgList)){
			return Collections.emptyList();
		}
		Set<ObjectId> parent2SelfIdList = new HashSet<>();
		for (TeSysDef prdCtlg : prdCtlgList) {
			parent2SelfIdList.addAll(prdCtlg.getParent2SelfIds());
		}
		return new ArrayList<>(parent2SelfIdList);
	}

	private List<TeSysDef> listMatchPrdCtlg(List<String> ccOrBuIdList, Collection<ObjectId> prdIdList,
											Collection<ObjectId> prdCtlgIdList, Boolean isContainParentPrdCtlg) {
		if(CollectionUtils.isEmpty(ccOrBuIdList)){
			return Collections.emptyList();
		}

		if (BooleanUtils.isTrue(isContainParentPrdCtlg) && !CollectionUtils.isEmpty(prdCtlgIdList)){
			prdCtlgIdList = listParentPrdCtlgId(new ArrayList<>(prdCtlgIdList));
		}

		List<IDbCondition> orConds = new ArrayList<>();
		for(String ccOrBuId : ccOrBuIdList){
			orConds.add(new DC_L(DFN.sysDef__ccList, ccOrBuId));
		}

		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), StringUtil.toObjectId(SysDefTypeConstants.PRDCTLG_DEF_ID)));
		conds.add(new DC_E(DFN.common_isValid,true));
		if (!CollectionUtils.isEmpty(prdIdList)) {
			conds.add(new DC_I<>(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), new ArrayList<>(prdIdList)));
		}
		if (!CollectionUtils.isEmpty(prdCtlgIdList)) {
			conds.add(new DC_I<>(DFN.common__id, new ArrayList<>(prdCtlgIdList)));
		}
		conds.add(new DbOrOperator(orConds));
		return sysDefDao.findByFieldAndConds(conds,null);
	}

	private void handleCcBuUserInfo(String ccOrBuId, TeSysUser sysUser, List<String> ccOrBuIdList, Map<String, List<TeSysUser>> ccOrBuId2UserMap) {
		if(StringUtil.isNull(ccOrBuId) ){
			return;
		}
		ccOrBuIdList.add(ccOrBuId);
		List<TeSysUser> userList = ccOrBuId2UserMap.getOrDefault(ccOrBuId, new ArrayList<>());
		if (userList.contains(sysUser)){
			return;
		}
		userList.add(sysUser);
		ccOrBuId2UserMap.put(ccOrBuId, userList);
	}

	private TeSysDefRoleUser createNewSysDefRoleUser(TeSysDef matchPrdCtlg, TeSysUser sysUser) {
		//srcDef
		TeSrcDef srcDef = matchPrdCtlg.getSrcDef();
		TeSysDefRoleUser2SysDef teSysDefRoleUser2SysDef = new TeSysDefRoleUser2SysDef();
		BeanUtils.copyProperties(srcDef,teSysDefRoleUser2SysDef);
		//defType
		TeDefType defType = matchPrdCtlg.getDefType();
		TeSysDefRoleUser2DefType teSysDefRoleUser2DefType = new TeSysDefRoleUser2DefType();
		BeanUtils.copyProperties(defType,teSysDefRoleUser2DefType);
		//role:设置成 成员
		List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
		TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
		role.setRoleId(StringUtil.toObjectId(SysDefConstants.DEF_ID_ROLE_PRD_MEMBER));
		role.setRoleCodeName(SysDefConstants.MEMBER_CODENAME);
		role.setRoleName(SysDefConstants.DEF_NAME_MEMBER);
		roles.add(role);
		// roleUser
		TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
		roleUser.setUserId(sysUser.getId());
		roleUser.setJobCode(sysUser.getJobCode());
		roleUser.setLoginName(sysUser.getLoginName());
		roleUser.setUserName(sysUser.getUserName());

		TeSysDefRoleUser newSysDefRoleUser = new TeSysDefRoleUser();
		newSysDefRoleUser.setSrcDef(teSysDefRoleUser2SysDef);
		newSysDefRoleUser.setDefType(teSysDefRoleUser2DefType);
		newSysDefRoleUser.setRole(roles);
		newSysDefRoleUser.setIsValid(true);
		newSysDefRoleUser.setDefId(matchPrdCtlg.getId());
		newSysDefRoleUser.setRoleUser(roleUser);
		return newSysDefRoleUser;
	}

	private List<TeSysUser> listCcOrBuNotNullUser(List<ObjectId> userIds) {
		if (CollectionUtils.isEmpty(userIds)) {
			return Collections.emptyList();
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_I<ObjectId>(DFN.common__id, userIds));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common_id);
		fieldNames.add(DFN.common_jobCode);
		fieldNames.add(DFN.common_loginName);
		fieldNames.add(DFN.common_userName);
		fieldNames.add(DFN.sysUser_ccId);
		fieldNames.add(DFN.sysUser_sbuId);
		List<TeSysUser> userList = sysUserDao.findByFieldAndConds(conds, fieldNames, null);
		if (CollectionUtils.isEmpty(userList)) {
			return Collections.emptyList();
		}
		List<TeSysUser> ccOrBuNotNullUserList = new ArrayList<>();
		for (TeSysUser user : userList) {
			if(StringUtils.isEmpty(user.getCcId()) && StringUtils.isEmpty(user.getSbuId())){
				continue;
			}
			ccOrBuNotNullUserList.add(user);
		}
		return ccOrBuNotNullUserList;
	}

	/**
	 * 确认是否为业务管理员
	 */
	@Override
	public boolean isBizManager(ObjectId prdId, TeSysUser loginUser) {

		// 查询
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), SysDefTypeCodeName.PRDCTLG.getValue()));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), SysDefConstants.DEF_ID_ROLE_PRD_BIZ_ADMIN));
		conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), prdId));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), loginUser.getId()));
		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysDefRoleUser__roleUser);
		List<TeSysDefRoleUser> dataList = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNameList);

		if (!CollectionUtils.isEmpty(dataList)) {
			return true;
		}
		return false;
	}

	@Override
	public List<ObjectId> listQualityTrackerProvId(ObjectId userId) {
		if (userId == null) {
			return Collections.emptyList();
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), SysDefConstants.BU_QUALITY_SYSTEM_TRACKER_DEF_OBJ_ID));
		conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), userId));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser__defId);
		List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(roleUsers)) {
			return Collections.emptyList();
		}
		return roleUsers.stream()
				.map(TeSysDefRoleUser::getDefId)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	}

}
