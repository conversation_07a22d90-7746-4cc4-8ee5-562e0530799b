package com.linkus.sysuser.dao.impl;

import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.*;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.mongo.MongoDaoSupport;
import com.linkus.base.exception.BaseException;
import com.linkus.base.util.StringUtil;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sysuser.contans.SysUserConstants;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Repository("SysDefRoleUserDaoImpl")
public class SysDefRoleUserDaoImpl extends MongoDaoSupport<TeSysDefRoleUser> implements ISysDefRoleUserDao {
	@Autowired
	private ISysUserDao sysUserDao;

	@Autowired
	private SysDefDao sysDefDao;

	@Override
	public List<TeSysDefRoleUser> findByUserAndDefIdAndDefType(TeSysDefRoleUser teSysDefRoleUser) {
		Criteria criteria = new Criteria();
		criteria.and("defId").is(teSysDefRoleUser.getDefId())
				.and("defType.defTypeCodeName").is(teSysDefRoleUser.getDefType().getDefTypeCodeName())
				.and("roleUser.loginName").is(teSysDefRoleUser.getRoleUser().getLoginName())
				.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		return this.find(query);
	}

	/**
	 * 根据 defId || defType.defTypeCodeName || role.roleCodeName || roleUser.userName 查询用户信息
	 * @param sysDefRoleUser
	 */
	@Override
	public List<TeSysDefRoleUser> querySysUserDistinctColl(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		criteria.and("isValid").is(true);

		if (null != sysDefRoleUser.getDefId()) {
			criteria.orOperator(Criteria.where("defId").is(sysDefRoleUser.getDefId())
					,Criteria.where("srcDef.srcDefId").is(sysDefRoleUser.getDefId()));
		}
		
		if (null != sysDefRoleUser.getDefType()) {
			criteria.and("defType.defTypeCodeName").is(sysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		
		if (null != sysDefRoleUser.getRole() && !sysDefRoleUser.getRole().isEmpty()) {
			List<TeSysDefRoleUser2Role> role = sysDefRoleUser.getRole();
			List<String> roleCodes = role.stream().map(TeSysDefRoleUser2Role::getRoleCodeName).filter(r-> null != r).collect(Collectors.toList());
			if (!roleCodes.isEmpty()){

				criteria.and("role.roleCodeName").in(roleCodes);
			}
		}
		
		if (null != sysDefRoleUser.getRole() && !sysDefRoleUser.getRole().isEmpty()) {
			List<TeSysDefRoleUser2Role> role = sysDefRoleUser.getRole();
			List<ObjectId> roleIds = role.stream().map(TeSysDefRoleUser2Role::getRoleId).filter(r-> null != r).collect(Collectors.toList());
			if (!roleIds.isEmpty()){
				criteria.and("role.roleId").in(roleIds);
			}
		}
		
		if (null != sysDefRoleUser.getRoleUser() && StringUtil.isNotNull(sysDefRoleUser.getRoleUser().getUserName())) {
			Pattern pattern = Pattern.compile("^.*" + sysDefRoleUser.getRoleUser().getUserName() + ".*$", Pattern.CASE_INSENSITIVE);
			criteria.and("roleUser.userName").regex(pattern);
		}
		if (null != sysDefRoleUser.getRoleUser() && StringUtil.isNotNull(sysDefRoleUser.getRoleUser().getLoginName())) {
			criteria.and("roleUser.loginName").is(sysDefRoleUser.getRoleUser().getLoginName());
		}
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> findByDefTypeAndRoleAndUser(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		if(sysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(sysDefRoleUser.getDefId());
		}
		if(sysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(sysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(null != sysDefRoleUser.getSrcDef() && null != sysDefRoleUser.getSrcDef().getSrcDefId()){
			criteria.and("srcDef.srcDefId").is(sysDefRoleUser.getSrcDef().getSrcDefId());
		}
		if (null != sysDefRoleUser.getRole() && !sysDefRoleUser.getRole().isEmpty()) {
			List<TeSysDefRoleUser2Role> role = sysDefRoleUser.getRole();
			List<String> roleCodes = role.stream().map(TeSysDefRoleUser2Role::getRoleCodeName).filter(r-> null != r).collect(Collectors.toList());
			if (!roleCodes.isEmpty()){

				criteria.and("role.roleCodeName").in(roleCodes);
			}
		}
		if (null != sysDefRoleUser.getRole() && !sysDefRoleUser.getRole().isEmpty()) {
			List<TeSysDefRoleUser2Role> role = sysDefRoleUser.getRole();
			List<ObjectId> roleIds = role.stream().map(TeSysDefRoleUser2Role::getRoleId).filter(r-> null != r).collect(Collectors.toList());
			if (!roleIds.isEmpty()){
				criteria.and("role.roleId").in(roleIds);
			}
		}
		if(sysDefRoleUser.getRoleUser()!=null){
			if("".equals(sysDefRoleUser.getRoleUser().getLoginName())) {
				// 这是一个bug规避，其实我感觉不加也没什么问题，主要是数据本身的问题不是业务的问题，数据本身的规避不够全面。
				criteria.and("roleUser.userId").is(sysDefRoleUser.getRoleUser().getUserId());
			}else {
				criteria.and("roleUser.loginName").is(sysDefRoleUser.getRoleUser().getLoginName());
			}
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> findByUserAndDefTypeIsNull(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		if(sysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.loginName").is(sysDefRoleUser.getRoleUser().getLoginName());
		}
		criteria.and("defType.defTypeCodeName").is(null);
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}
	
	@Override
	public List<TeSysDefRoleUser> findByDefTypeAndDefId(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		if(sysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(sysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(sysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(sysDefRoleUser.getDefId());
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		query.with(Sort.by(new Order(Direction.ASC, "defId")).and(Sort.by(Direction.ASC,"roleUser.userId")));
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> findByTeSysDefRoleUserAndDefIds(TeSysDefRoleUser sysDefRoleUser, List<ObjectId> defIds) {
		
		Criteria criteria = new Criteria();
		
		if (null != sysDefRoleUser) {
			if(sysDefRoleUser.getDefType()!=null){
				criteria.and("defType.defTypeCodeName").is(sysDefRoleUser.getDefType().getDefTypeCodeName());
			}
			if(sysDefRoleUser.getRole()!=null){
				List<TeSysDefRoleUser2Role> role = sysDefRoleUser.getRole();
				List<String> roleCodes = role.stream().map(TeSysDefRoleUser2Role::getRoleCodeName).collect(Collectors.toList());
				criteria.and("role.roleCodeName").in(roleCodes);
			}
			if(sysDefRoleUser.getRoleUser()!=null){
				criteria.and("roleUser.loginName").is(sysDefRoleUser.getRoleUser().getLoginName());
			}
		}
		
		if(null!=defIds && defIds.size()>0){
			criteria.and("defId").in(defIds);
		}
		
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		query.with(Sort.by(new Order(Direction.ASC, "defId")).and(Sort.by(Direction.ASC,"roleUser.userId")));
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}
	
	@Override
	public List<TeSysDefRoleUser> queryGroupRespByGroup(String codeName, List<ObjectId> ids, TeSysDefRoleUser2Role roleGroupResp) {
		
		Criteria criteria = new Criteria();
		String groupRspCodeName = "";
		
		if (null != roleGroupResp) {
			groupRspCodeName = roleGroupResp.getRoleCodeName();
		}
		if(StringUtil.isNotNull(codeName)){
			criteria.and("defType.defTypeCodeName").is(codeName);
		}
		if(StringUtil.isNotNull(ids)){
			criteria.and("defId").in(ids);
		}
		if(StringUtil.isNotNull(groupRspCodeName)){
			criteria.and("role.roleCodeName").is(groupRspCodeName);
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
        return teSysDefRoleUsers;
		
	}

	@Override
	public List<TeSysDefRoleUser> findByDefIdAndUser(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		if(sysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(sysDefRoleUser.getDefId());
		}
		if(sysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.userId").is(sysDefRoleUser.getRoleUser().getUserId());
		}
		if(null != sysDefRoleUser.getRole()){
			List<ObjectId> roleIds = sysDefRoleUser.getRole().stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
			criteria.and("role.roleId").in(roleIds);
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> findByDefIdAndUser(ObjectId defId,
			ObjectId userId, List<ObjectId> roleIds) {
		Criteria criteria = new Criteria();
		if(defId!=null){
			criteria.and("defId").is(defId);
		}
		if(userId!=null){
			criteria.and("roleUser.userId").is(userId);
		}
		if(null != roleIds && roleIds.size() >0){
			criteria.and("role.roleId").in(roleIds);
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> queryRoleUserByUserIdAndType(TeSysDefRoleUser2DefType defType, TeSysUser teSysUser) {
		Criteria criteria = new Criteria();
		criteria.and("defType.defTypeCodeName").is(defType.getDefTypeCodeName()).and("roleUser.userId").is(teSysUser.getId());
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> queryRoleUserBySrcAndType(TeSysDefRoleUser2DefType defType, List<ObjectId> idList) {
		Criteria criteria = new Criteria();
		criteria.and("defType.defTypeCodeName").is(defType.getDefTypeCodeName()).and("srcDef.srcDefId").in(idList);
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> queryAdminBydefIdsAndRoleUser(List<ObjectId> idList,
			TeSysDefRoleUser2Role role, TeSysDefRoleUser2User roleUser) {
		Criteria criteria = new Criteria();
		if(null != idList && idList.size() > 0){
			criteria.and("defId").in(idList);
		}
		if(null != role){
			criteria.and("role.roleId").is(role.getRoleId());
		}
		if(StringUtil.isNotNull(roleUser)){
			criteria.and("roleUser.userId").is(roleUser.getUserId());
		}
		criteria.and("isValid").is(true);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}
	
	@Override
	public List<TeSysDefRoleUser> queryIsGroupRespOfGroupInfo(List<ObjectId> groupIdList, String groupRespCodeName, ObjectId loginUserId) {
		
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    
	    if (null!=groupIdList && !groupIdList.isEmpty()) {
	    	criteria.and("defId").in(groupIdList);
	    }
	    
	    if (StringUtil.isNotNull(groupRespCodeName)) {
	    	criteria.and("role.roleCodeName").is(groupRespCodeName);
	    }
	    
	    if (null != loginUserId) {
	    	criteria.and("roleUser.userId").is(loginUserId);
	    }
		criteria.and("prj").is(null);
	    query.addCriteria(criteria); 
	    return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}

	@Override
	public void removeSysRoleUser(TeSysDefRoleUser teSysDefRoleUser) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		if(teSysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(teSysDefRoleUser.getDefId());
		}
		if(teSysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(teSysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(teSysDefRoleUser.getRole()!=null){
			List<String> roleIds = teSysDefRoleUser.getRole().stream().map(TeSysDefRoleUser2Role::getRoleCodeName).collect(Collectors.toList());
			criteria.and("role.roleCodeName").in(roleIds);
		}
		if(teSysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.loginName").is(teSysDefRoleUser.getRoleUser().getLoginName());
		}
		Update update = Update.update("isValid", false);
		query.addCriteria(criteria);
		mongoTemplate.updateMulti(query, update, TeSysDefRoleUser.class);
	}
	
	@Override
	public void removeTeSysDefRoleUser(TeSysDefRoleUser teSysDefRoleUser) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		if(teSysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(teSysDefRoleUser.getDefId());
		}
		if(teSysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(teSysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(teSysDefRoleUser.getRole()!=null){
			List<String> roleNames = teSysDefRoleUser.getRole().stream().map(TeSysDefRoleUser2Role::getRoleName).collect(Collectors.toList());
			criteria.and("role.roleName").in(roleNames);
		}
		if(teSysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.loginName").is(teSysDefRoleUser.getRoleUser().getLoginName());
		}
		Update update = Update.update("isValid", false);
		query.addCriteria(criteria);
		mongoTemplate.updateMulti(query, update, TeSysDefRoleUser.class);
	}
	@Override
	public List<TeSysDefRoleUser> queryRoleUserByRoleAndDefId(TeSysDefRoleUser2Role role, ObjectId defId) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		if(defId!=null){
			criteria.and("defId").is(defId);
		}
		if(role!=null){
			criteria.and("role.roleCodeName").is(role.getRoleCodeName());
		}
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}
	@Override
	public List<TeSysDefRoleUser> queryRoleUserByRoleAndDefIds(String roleId, ObjectId userId,List<ObjectId> defIds) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		if(null != defIds && defIds.size()>0){
			criteria.and("defId").in(defIds);
		}
		if(null != userId){
			criteria.and("roleUser.userId").is(userId);
		}
		if(null != roleId){
			criteria.and("role.roleId").is(new ObjectId(roleId));
		}
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}
	@Override
	public void bachSaveTeSysDefRoleUser(List<TeSysDefRoleUser> teSysDefRoleUserlist) {
		this.batchSave(teSysDefRoleUserlist);		
	}

	@Override
	public List<TeSysDefRoleUser> findTeSysDefRoleUserByCondition(
			TeSysDefRoleUser teSysDefRoleUser) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		if (teSysDefRoleUser.getId()!=null) {
			criteria.and("_id").is(teSysDefRoleUser.getId());
		}
		if(teSysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(teSysDefRoleUser.getDefId());
		}
		if(teSysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(teSysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(teSysDefRoleUser.getSrcDef()!=null){
			criteria.and("srcDef.srcDefId").is(teSysDefRoleUser.getSrcDef().getSrcDefId());
		}
		if(teSysDefRoleUser.getRole()!=null){
			List<String> roleCodes = teSysDefRoleUser.getRole().stream().map(TeSysDefRoleUser2Role::getRoleCodeName).collect(Collectors.toList());

			criteria.and("role.roleCodeName").in(roleCodes);
		}
		if(teSysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.userId").is(teSysDefRoleUser.getRoleUser().getUserId());
		}
		if (teSysDefRoleUser.getIsValid()!=null) {
			criteria.and("isValid").is(teSysDefRoleUser.getIsValid());
		}
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	@Override
	public List<TeSysDefRoleUser> findTeSysDefRoleUserByList(List<SysDef> sysDefList) {
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    List<ObjectId> defList=new ArrayList<ObjectId>();
	    List<String> defCodeNameList=new ArrayList<String>();
	    for(SysDef sys:sysDefList){
	    	defList.add(sys.getId());
	    	defCodeNameList.add(sys.getCodeName());
	    }
	    if (!defList.isEmpty()) {
			criteria.and("defId").in(defList);
		}
	    if (!defCodeNameList.isEmpty()) {
	 			criteria.and("defType.defTypeCodeName").is(SysDefTypeConstants.PRJGROUP_CODENAME);
	 		}
	    query.addCriteria(criteria); 
	    return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}

	@Override
	public void removeTeSysRoleUserByTypeDefIdList(String defTypeCodeName, List<ObjectId> defIdList) {
		// TODO Auto-generated method stub
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    criteria.and("defId").in(defIdList);
	    criteria.and("defType.defTypeCodeName").is(defTypeCodeName);
		query.addCriteria(criteria);
		this.deleteByQueryHandle(query);
	}
	
	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(ObjectId defTypeId,  List<ObjectId> defIds,ObjectId roleId){
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    if( null != defTypeId){
	    	criteria.and("defType.defTypeId").is(defTypeId);
	    }
	    if(null != defIds){
	    	criteria.and("defId").in(defIds);
	    }
	    if(null != roleId){
	    	criteria.and("role.roleId").is(roleId);
	    }
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}
	
	@Override
	public List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIdes(ObjectId defTypeId,  List<ObjectId> defIds,List<ObjectId> roleId){
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    if( null != defTypeId){
	    	criteria.and("defType.defTypeId").is(defTypeId);
	    }
	    if(null != defIds){
	    	criteria.and("defId").in(defIds);
	    }
	    if(null != roleId){
	    	criteria.and("role.roleId").in(roleId);
	    }
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}
	
	@Override
	public List<TeSysDefRoleUser> findByIds(List<ObjectId> ids){
		if ( null == ids) {
			throw new BaseException("参数校验：ids为null");
		}
		Query query = new Query(); 
	    Criteria criteria = Criteria.where("isValid").is(true);
	    criteria.and("_id").in(ids);
		query.addCriteria(criteria);
		return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}

	@Override
	public List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, 
			Collection<ObjectId> roleIds, Collection<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName) {
		
		Query query = new Query();
		Criteria criteria = Criteria.where("isValid").is(true);
		
		if (userIds != null && !userIds.isEmpty()) {
			criteria.and("roleUser.userId").in(userIds);
		}
		if (roleIds != null && !roleIds.isEmpty()) {
			criteria.and("role.roleId").in(roleIds);
		}
		if (defIds != null && !defIds.isEmpty()) {
			criteria.and("defId").in(defIds);
		}
		if (defTypeCodeName != null) {
			criteria.and("defType.defTypeCodeName").is(defTypeCodeName.getValue());
		}
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUserList = find(query);

		return teSysDefRoleUserList;
	}


	@Override
	public List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds,
			Collection<ObjectId> roleIds, Collection<ObjectId> defIds,
			SysDefTypeCodeName defTypeCodeName, Boolean isStd) {
		Query query = new Query();
		Criteria criteria = Criteria.where("isValid").is(true);
		
		if (userIds != null && !userIds.isEmpty()) {
			criteria.and("roleUser.userId").in(userIds);
		}
		if (roleIds != null && !roleIds.isEmpty()) {
			criteria.and("role.roleId").in(roleIds);
		}
		if (defIds != null && !defIds.isEmpty()) {
			criteria.and("defId").in(defIds);
		}
		if (defTypeCodeName != null) {
			criteria.and("defType.defTypeCodeName").is(defTypeCodeName.getValue());
		}
        if( isStd != null){
        	criteria.and("isStd").is(isStd);
        }
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUserList = find(query);

		return teSysDefRoleUserList;
	}

	@Override
	public List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds, Collection<ObjectId> defIds, Collection<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName) {
		return getSysUserRole(userIds, roleIds, defIds, srcDefIds, defTypeCodeName, null);
	}

	@Override
	public List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds, Collection<ObjectId> defIds, Collection<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName, Boolean isStd) {
		Query query = new Query();
		Criteria criteria = Criteria.where("isValid").is(true);

		if (CollectionUtils.isNotEmpty(userIds)) {
			criteria.and("roleUser.userId").in(userIds);
		}
		if (CollectionUtils.isNotEmpty(roleIds)) {
			criteria.and("role.roleId").in(roleIds);
		}
		if (CollectionUtils.isNotEmpty(defIds)) {
			criteria.and("defId").in(defIds);
		}
		if (CollectionUtils.isNotEmpty(srcDefIds)) {
			criteria.and("srcDef.srcDefId").in(srcDefIds);
		}
		if (defTypeCodeName != null) {
			criteria.and("defType.defTypeCodeName").is(defTypeCodeName.getValue());
		}
		if (isStd != null) {
			criteria.and("isStd").is(isStd);
		}
		query.addCriteria(criteria);
		return find(query);
	}

	@Override
	public List<TeSysDefRoleUser> getSysUserRoleByName(String userName,
			Collection<ObjectId> roleIds, Collection<ObjectId> defIds,
			SysDefTypeCodeName defTypeCodeName) {
		Query query = new Query();
		Criteria criteria = Criteria.where("isValid").is(true);
		if (StringUtil.isNotNull(userName)) {
			criteria.and("roleUser.userName").regex(userName);
		}

		if (roleIds != null && !roleIds.isEmpty()) {
			criteria.and("role.roleId").in(roleIds);
		}

		if (defIds != null && !defIds.isEmpty()) {
			criteria.and("defId").in(defIds);
		}

		if (defTypeCodeName != null) {
			criteria.and("defType.defTypeCodeName").is(defTypeCodeName.getValue());
		}
		criteria.and("prj").is(null);
		query.addCriteria(criteria);
		return find(query);
	}
	@Override
	public void removeSysUserRoles(List<ObjectId> userIds, List<ObjectId> roleIds, List<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName) {
		Query query = new Query();
		Criteria criteria = Criteria.where("isValid").is(true);
		if (userIds != null && !userIds.isEmpty()) {
			criteria.and("roleUser.userId").in(userIds);
		}

		if (roleIds != null && !roleIds.isEmpty()) {
			criteria.and("role.roleId").in(roleIds);
		}

		if (defIds != null && !defIds.isEmpty()) {
			criteria.and("defId").in(defIds);
		}

		if (defTypeCodeName != null) {
			criteria.and("defType.defTypeCodeName").is(defTypeCodeName.getValue());
		}
		query.addCriteria(criteria);
		mongoTemplate.remove(query, TeSysDefRoleUser.class);
	}
	@Override
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, SysDefTypeCodeName codeName) {
		if ((null==defIds || defIds.size()==0) && codeName == null) {
			throw new BaseException("定义id和定义类型不能全部为null");
		}
		return this.getSysUserRole(null, roleIds, defIds, codeName);
	}

	@Override
	public TeSysDefRoleUser queryPomAuthByLoginName(String loginName) {
		Query query = new Query();
		Criteria criteria = Criteria.where("defId").is(null)
									.and("role.roleCodeName").is("pomAdmin")
									.and("roleUser.loginName").is(loginName)
									.and("_id").ne("ObjectId()");
		query.addCriteria(criteria);
		return findOne(query);
	}

	@Override
	public TeSysDefRoleUser queryByUserIdAndRoleId(ObjectId userId, ObjectId objectId) {
		Query query = new Query();
		Criteria criteria = Criteria.where("defId").is(null)
									.and("roleUser.userId").is(userId)
									.and("role.roleId").is(objectId)
									.and("isValid").is(true)
									.and("_id").ne("ObjectId()");
		query.addCriteria(criteria);
		return findOne(query);
	}
	
	@Override
	public List<TeSysDefRoleUser> findSysDefRoleUserByCondition(List<ObjectId> defIds,ObjectId srcDefId,String defTypeCodeName,ObjectId roleId){
		List<IDbCondition> conds = new ArrayList<>();
		if(null != defIds && !defIds.isEmpty()){
			conds.add(new DC_I<ObjectId>(DbFieldName.sysDefRoleUser__defId, defIds));
		}
		if(null != srcDefId){
			conds.add(new DbEqualCondition(DbFieldName.sysDefRoleUser_srcDef.dot(DbFieldName.sysDefRoleUser_srcDefId), srcDefId));
		}
		if(StringUtil.isNotNull(defTypeCodeName)){
			conds.add(new DbEqualCondition(DbFieldName.sysDefRoleUser_defType.dot(DbFieldName.sysDefRoleUser_defTypeCodeName), defTypeCodeName));		
		}
		if(null != roleId){
			conds.add(new DbEqualCondition(DbFieldName.sysDefRoleUser__role.dot(DbFieldName.sysDefRoleUser__roleId), roleId));
		}
		conds.add(new DbEqualCondition(DFN.common_isValid, true));
		conds.add(new DbEqualCondition(DFN.sysDefRoleUser__prj, null));

		return findByConds(conds, null);
	}

	@Override
	public List<TeSysDefRoleUser> findTeSysDefRoleUserByListAndSort(List<SysDef> sysDefList) {
		Query query = new Query();
		//Criteria criteria = Criteria.where("isValid").is(true);
		Criteria criteria = new Criteria();
		List<ObjectId> defList=new ArrayList<ObjectId>();
		List<String> defCodeNameList=new ArrayList<String>();
		for(SysDef sys:sysDefList){
			defList.add(sys.getId());
			defCodeNameList.add(sys.getCodeName());
		}
		if (!defList.isEmpty()) {
			criteria.and("defId").in(defList);
		}
		if (!defCodeNameList.isEmpty()) {
			criteria.and("defType.defTypeCodeName").is(SysDefTypeConstants.PRJGROUP_CODENAME);
		}
		query.addCriteria(criteria);
		query.with(Sort.by(Direction.ASC, "defId"));
		return mongoTemplate.find(query, TeSysDefRoleUser.class);
	}

	/**
	 * 根据组织树，查询该组织树下所有的人员，包括历史人员,
	 * 如果isValid为false，有两种情况，一种是退出，一种是删除，我们只要退出，即quitTime必须存在，
	 * 如果isValid为true,说明还在项目组，这个也要统计
	 *
	 * @param sysDefList
	 * @return
	 */
	@Override
	public List<TeSysDefRoleUser> findHiSysDefRoleUserByList(List<SysDef> sysDefList) {
		List<IDbCondition> conds   = new ArrayList<>();
		List<IDbCondition> orConds = new ArrayList<>();
		List<IDbCondition> andConds_validFalse= new ArrayList<>();
		List<IDbCondition> andConds_validTrue= new ArrayList<>();
		andConds_validFalse.add(new DbEqualCondition(DFN.common_isValid,false));
		andConds_validFalse.add(new DbEqualCondition(DFN.sysDefRoleUser_quitTime,null,true));
		orConds.add(new DbAndOperator(andConds_validFalse));
		andConds_validTrue.add(new DbEqualCondition(DFN.common_isValid,true));
		andConds_validTrue.add(new DbExistCondition(DFN.sysDefRoleUser_enterTime));
		orConds.add(new DbAndOperator(andConds_validTrue));
		conds.add(new DbOrOperator(orConds));
		List<ObjectId> defList=new ArrayList<ObjectId>();
		List<String> defCodeNameList=new ArrayList<String>();
		for(SysDef sys:sysDefList){
			defList.add(sys.getId());
			defCodeNameList.add(sys.getCodeName());
		}
		if (!defList.isEmpty()) {
			conds.add(new DbInCondition<ObjectId>(DFN.sysDefRoleUser__defId,defList));
		}
		if (!defCodeNameList.isEmpty()) {
			conds.add(new DbEqualCondition(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
		}

		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.sysDefRoleUser_enterTime);
		fieldNames.add(DFN.sysDefRoleUser_quitTime);
		fieldNames.add(DFN.sysDefRoleUser__role);
		fieldNames.add(DFN.sysDefRoleUser__defId);
		fieldNames.add(DFN.sysDefRoleUser__defPara);

		List<TeSysDefRoleUser> result = findByFieldAndConds(conds, fieldNames);
		
		return result;
	}

	/**
	 * 根据defId当前项目组织的人员，包括历史人员
	 *
	 * @param teSysDefRoleUser
	 * @return
	 */
	@Override
	public List<TeSysDefRoleUser> findByDefTypeAndDefId4StructureAndStaff(TeSysDefRoleUser teSysDefRoleUser) {
		Criteria criteria = new Criteria();
		Criteria validCriteria = new Criteria();
		validCriteria.and("isValid").is(true).and("enterTime").ne(null);
		Criteria notValidCriteria = new Criteria();
		notValidCriteria.and("isValid").is(false).and("quitTime").ne(null);
		criteria.orOperator(validCriteria,notValidCriteria);
		if(teSysDefRoleUser.getDefType()!=null){
			criteria.and("defType.defTypeCodeName").is(teSysDefRoleUser.getDefType().getDefTypeCodeName());
		}
		if(teSysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(teSysDefRoleUser.getDefId());
		}
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		query.with(Sort.by(new Order(Direction.ASC, "defId")).and(Sort.by(Direction.ASC,"roleUser.userId")));
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

	/**
	 * 查询在组中已经退出的人员
	 *
	 * @param sysDefRoleUser
	 * @return
	 */
	@Override
	public List<TeSysDefRoleUser> findExpiredUserByDefIdAndUser(TeSysDefRoleUser sysDefRoleUser) {
		Criteria criteria = new Criteria();
		if(sysDefRoleUser.getDefId()!=null){
			criteria.and("defId").is(sysDefRoleUser.getDefId());
		}
		if(sysDefRoleUser.getRoleUser()!=null){
			criteria.and("roleUser.userId").is(sysDefRoleUser.getRoleUser().getUserId());
		}
		if(null != sysDefRoleUser.getRole()){
			List<ObjectId> roleIds = sysDefRoleUser.getRole().stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
			criteria.and("role.userId").in(roleIds);
		}
		criteria.and("isValid").is(false);
		criteria.and("prj").is(null);
		Query query = Query.query(criteria);
		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
		return teSysDefRoleUsers;
	}

//	/**
//	 * 获取BU管理员清单
//	 * @return
//	 */
//	public List<TeSysDefRoleUser2User> findBuManagers() {
//		Criteria criteria = new Criteria();
//		criteria.and("isValid").is(true)
//				.and("defId").is("")
//				.and("role.roleId").is(new ObjectId("5aa77b1e29974f7cabe911bb"));
//		Query query = Query.query(criteria);
//		List<TeSysDefRoleUser> teSysDefRoleUsers = this.find(query);
//		List<TeSysDefRoleUser2User> roleUserList = new ArrayList<>();
//		for (TeSysDefRoleUser user : teSysDefRoleUsers) {
//			roleUserList.add(user.getRoleUser());
//		}
//		return roleUserList;
//	}

	@Override
	public Boolean validateIsAbpAdmin(ObjectId buId, ObjectId userId) {
		List<IDbCondition> cons = new ArrayList<>();
		cons.add(new DC_E(DbFieldName.common_isValid, true));
		cons.add(new DC_E(DbFieldName.sysDefRoleUser__role.dot(DbFieldName.sysDefRoleUser__roleId), SysUserConstants.ROLE_ABP_ADMIN_ID));
		cons.add(new DC_E(DbFieldName.sysDefRoleUser__roleUser.dot(DbFieldName.sysDefRoleUser__userId), userId));
		cons.add(new DC_E(DbFieldName.sysDefRoleUser__defId, buId));
		cons.add(new DC_E(DbFieldName.sysDefRoleUser_defType.dot(DbFieldName.sysDefRoleUser_defTypeCodeName), SysDefTypeCodeName.AI_BU.getValue()));
		return countByConds(cons) > 0;
	}

	@Override
	public List<TeSysDefRoleUser> queryByRcd(ObjectId id) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DbFieldName.common_isValid, true));
		conds.add(new DC_E(DbFieldName.sysDefRoleUser_rcd.dot(DbFieldName.common_cid), id));
		List<DbFieldName > fieldNames= new ArrayList<>();
		fieldNames.add(DbFieldName.common__id);
		fieldNames.add(DbFieldName.sysDefRoleUser__role);
		fieldNames.add(DbFieldName.sysDefRoleUser__roleUser);
		fieldNames.add(DbFieldName.sysDefRoleUser__defId);
		return findByFieldAndConds(conds,fieldNames);
	}

	@Override
	public void removeByRcd(ObjectId bizId) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DbFieldName.common_isValid, true));
		conds.add(new DC_E(DbFieldName.sysDefRoleUser_rcd.dot(DbFieldName.common_cid), bizId));
		deleteByConds(conds);
	}

}
