package com.linkus.sysuser.dao;

import com.linkus.base.db.mongo.MongoDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2DefType;
import com.linkus.sysuser.model.TeSysDefRoleUser2Role;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import java.util.Collection;
import java.util.List;

public interface ISysDefRoleUserDao extends MongoDao<TeSysDefRoleUser>{
	
	/**
	 * 
	 * @param teSysDefRoleUser
	 * @return
	 */
	public List<TeSysDefRoleUser> findByUserAndDefIdAndDefType(TeSysDefRoleUser teSysDefRoleUser);

	/**
	 * 根据 defId || defType.defTypeCodeName || role.roleCodeName || roleUser.userName 查询用户信息
	 * @param sysDefRoleUser
	 */
	public List<TeSysDefRoleUser> querySysUserDistinctColl(TeSysDefRoleUser sysDefRoleUser);
	
	/**
	 * findByDefTypeAndRoleAndUser 通过DefType（定义类型）、Role（角色）、User（用户）查询
	 * 适用范围：
	 * 1、根据用户，				     查询用户角色
	 * 2、根据用户、角色，给定类型    删除小组下成员
	 * 
	 * @param sysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findByDefTypeAndRoleAndUser(TeSysDefRoleUser sysDefRoleUser);
	
	/**
	 * findByUserAndDefTypeIsNull 根据User（用户）查询，DefType（isNull）
	 * 适用范围：
	 * 1、查询用户是否为系统管理员或者Bu管理员
	 * 
	 * @param sysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findByUserAndDefTypeIsNull(TeSysDefRoleUser sysDefRoleUser);
	
	/**
	 * findByDefTypeAndDefId 通过DefType（定义类型）、DefId（定义id）查询
	 * 适用范围：
	 * 1、根据小组查询成员
	 * 
	 * @param sysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findByDefTypeAndDefId(TeSysDefRoleUser sysDefRoleUser);
	
	/**
	 * findByDefTypeAndDefId 通过DefType（定义类型）、DefIds（定义id）查询
	 * 适用范围：
	 * 1、根据小组查询成员
	 * 
	 * @param sysDefRoleUser
	 * @param defIds
	 * @return
	 */
	List<TeSysDefRoleUser> findByTeSysDefRoleUserAndDefIds(TeSysDefRoleUser sysDefRoleUser, List<ObjectId> defIds);
	
	/**
	 * queryGroupRespByGroup 查询组长
	 * 
	 * @param codeName
	 * @param ids
	 * @param roleGroupResp
	 * @return
	 */
	List<TeSysDefRoleUser> queryGroupRespByGroup(String codeName,List<ObjectId> ids,TeSysDefRoleUser2Role roleGroupResp);
	
	/**
	 * findByDefIdAndUser 通过DefId（定义id）、User（用户）查询
	 * 适用范围：
	 * 1、根据用户和小组id，查询小组成员重复判断
	 * 
	 * @param sysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findByDefIdAndUser(TeSysDefRoleUser sysDefRoleUser);
	
	List<TeSysDefRoleUser> findByDefIdAndUser(ObjectId defId, ObjectId userId,List<ObjectId> roleIds);
	
	List<TeSysDefRoleUser> queryRoleUserByUserIdAndType(TeSysDefRoleUser2DefType defType,TeSysUser teSysUser);
	
	List<TeSysDefRoleUser> queryRoleUserBySrcAndType(TeSysDefRoleUser2DefType defType,List<ObjectId> idList);
	
	List<TeSysDefRoleUser> queryAdminBydefIdsAndRoleUser(List<ObjectId> idList,TeSysDefRoleUser2Role role,TeSysDefRoleUser2User roleUser);
	
	void removeSysRoleUser(TeSysDefRoleUser teSysDefRoleUser);
	
	void removeTeSysDefRoleUser(TeSysDefRoleUser teSysDefRoleUser);
	
	List<TeSysDefRoleUser> queryRoleUserByRoleAndDefId(TeSysDefRoleUser2Role role,ObjectId defId);
	
	List<TeSysDefRoleUser> queryRoleUserByRoleAndDefIds(String kmsEditorDefId, ObjectId userId,List<ObjectId> defIds); 
	
	public List<TeSysDefRoleUser> queryIsGroupRespOfGroupInfo(List<ObjectId> groupIdList, String groupRespCodeName, ObjectId loginUserId);

	public void bachSaveTeSysDefRoleUser(List<TeSysDefRoleUser> teSysDefRoleUserlist);
	
	List<TeSysDefRoleUser> findTeSysDefRoleUserByCondition(TeSysDefRoleUser teSysDefRoleUser);
	
	List<TeSysDefRoleUser> findTeSysDefRoleUserByList(List<SysDef> sysDefList);
	
	void removeTeSysRoleUserByTypeDefIdList(String defTypeCodeName, List<ObjectId> defIdList);
	
	/**
	 * 通过定义类型ID,定义ID集合和角色ID查询人员角色
	 * @param defTypeId
	 * @param defIds
	 * @param roleId
	 * @return
	 */
	List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIds(ObjectId defTypeId, List<ObjectId> defIds,ObjectId roleId);
	
	List<TeSysDefRoleUser> querySysRoleUserByDefTypeIdAndDefIdes(ObjectId defTypeId, List<ObjectId> defIds,List<ObjectId> roleId);
	
	/**
	 * 通过ID集合查询人员角色
	 * @param ids
	 * @return
	 */
	List<TeSysDefRoleUser> findByIds(List<ObjectId> ids);
	/**
	 * 查询用户角色记录
	 * 
	 * @param userIds
	 *            用户id
	 * @param roleIds
	 *            角色id
	 * @param defIds
	 *            定义id
	 * @param defTypeCodeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
			Collection<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName);
	/**
	 * 查询用户角色记录
	 * 
	 * @param userIds
	 *            用户id
	 * @param roleIds
	 *            角色id
	 * @param defIds
	 *            定义id
	 * @param defTypeCodeName
	 *            定义类型
	 * @return
	 */
	List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds,
			Collection<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName,Boolean isStd);


	/**
	 * 查询用户角色记录
	 * @param userIds
	 * @param roleIds
	 * @param defIds
	 * @param srcDefIds
	 * @param defTypeCodeName
	 * @return
	 */
	List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds, Collection<ObjectId> defIds, Collection<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName);


	/**
	 * 查询用户角色记录
	 * @param userIds
	 * @param roleIds
	 * @param defIds
	 * @param srcDefIds
	 * @param defTypeCodeName
	 * @param isStd
	 * @return
	 */
	List<TeSysDefRoleUser> getSysUserRole(Collection<ObjectId> userIds, Collection<ObjectId> roleIds, Collection<ObjectId> defIds, Collection<ObjectId> srcDefIds, SysDefTypeCodeName defTypeCodeName, Boolean isStd);



	/**
	 * 根据用户名查询用户角色记录
	 * @param userName
	 * @param roleIds
	 * @param defIds
	 * @param defTypeCodeName
	 * @return
	 */
	List<TeSysDefRoleUser> getSysUserRoleByName(String userName, Collection<ObjectId> roleIds,
			Collection<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName);
	
	void removeSysUserRoles(List<ObjectId> userIds, List<ObjectId> roleIds, List<ObjectId> defIds, SysDefTypeCodeName defTypeCodeName);
	
	public List<TeSysDefRoleUser> getUsers(List<ObjectId> roleIds, List<ObjectId> defIds, SysDefTypeCodeName codeName);

	/**
	 * 验证是否具有项目运营管理权限
	 * */
	public TeSysDefRoleUser queryPomAuthByLoginName(String loginName);

	/**
	 * 根据用户Id和roleId查询
	 * @param userId
	 * @param objectId
	 * @return
	 */
	public TeSysDefRoleUser queryByUserIdAndRoleId(ObjectId userId, ObjectId objectId);
	
	
	public List<TeSysDefRoleUser> findSysDefRoleUserByCondition(List<ObjectId> defIds,ObjectId srcDefId,String defTypeCodeName,ObjectId roleId);

	/*public void getCCRoleUsers(Collection<ObjectId> roleIds,Collection<ObjectId> userIds,List<TeSysDefRoleUser> teSysDefRoleUsers);*/

	List<TeSysDefRoleUser> findTeSysDefRoleUserByListAndSort(List<SysDef> sysDefList);

	/**
	 * 根据组织树，查询所有的人员，包括历史人员
	 * @param sysDefList
	 * @return
	 */
	List<TeSysDefRoleUser> findHiSysDefRoleUserByList(List<SysDef> sysDefList);

	/**
	 * 根据defId当前项目组织的人员，包括历史人员
	 * @param teSysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findByDefTypeAndDefId4StructureAndStaff(TeSysDefRoleUser teSysDefRoleUser);

	/**
	 * 查询在组中已经退出的人员
	 * @param teSysDefRoleUser
	 * @return
	 */
	List<TeSysDefRoleUser> findExpiredUserByDefIdAndUser(TeSysDefRoleUser teSysDefRoleUser);

//	/**
//	 * 获取BU管理员清单
//	 * @return
//	 */
//	List<TeSysDefRoleUser2User> findBuManagers();

	/**
	 * ABP管理员清单
	 * @param buId
	 * @param userId
	 * @return
	 */
	Boolean validateIsAbpAdmin(ObjectId buId, ObjectId userId);

	List<TeSysDefRoleUser> queryByRcd(ObjectId id);

	void removeByRcd(ObjectId bizId);
}
