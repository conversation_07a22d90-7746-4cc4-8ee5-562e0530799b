package com.linkus.sys.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.DbEqualCondition;
import com.linkus.base.db.base.condition.impl.DbFuzzyCondition;
import com.linkus.base.db.base.condition.impl.DbInCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_F;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_L;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.sys.dao.SysDefCnfgDao;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.dao.SysDefTypeDao;
import com.linkus.sys.model.*;
import com.linkus.sys.model.bo.Parent2SelfPrdCtlgBO;
import com.linkus.sys.model.bo.SysDefCndtItemTreeBO;
import com.linkus.sys.model.po.*;
import com.linkus.sys.model.vo.SysDefCndtItemTreeVO;
import com.linkus.sys.model.vo.SysDefDbcPortalVo;
import com.linkus.sys.service.ISysDefCnfgService;
import com.linkus.sys.service.ISysDefService;
import com.mongodb.client.DistinctIterable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Service("SysDefServiceImpl")
public class SysDefServiceImpl implements ISysDefService {
	@Resource
	private MongoTemplate mongoTemplate;
	@Resource
	private SysDefDao sysDefDao;
	@Resource
	private SysDefTypeDao sysDefTypeDao;
	@Autowired
	private SysDefCnfgDao sysDefCnfgDao;

	private static final String COMMA = ",";

	private static final String OPERATE_TYPE_SUBMIT = "submit";
	private static final String OPERATE_TYPE_MODIFY = "modify";

	@Override
	public SysDef getSysDefById(ObjectId id) {
		if (id == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		TeSysDef teDef = sysDefDao.getSysDefById(id);
		if (teDef == null) {
			return null;
		}
		return SysDef.converSysDef(teDef);
	}

	@Override
	public TeSysDef getTeSysDefById(ObjectId id) {
		if (id == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		return sysDefDao.getSysDefById(id);
	}
	
	@Override
	public TeSysDef getTeSysDefByIdIgnoreIsValid(ObjectId id) {
		return sysDefDao.findById(id);
	}

	@Override
	public List<SysDef> getSysDefsByIds(List<ObjectId> ids) {
		if (ids == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByIds(ids);
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsBySrc(ObjectId srcId, SysDefTypeCodeName codeName) {
		SysDefQuerySortKey querySortKey = new SysDefQuerySortKey(SysDefQuerySortKey.Key.ID, true);
		List<SysDefQuerySortKey> querySortKeys = new ArrayList<SysDefQuerySortKey>();
		querySortKeys.add(querySortKey);
		return getSysDefsBySrc(srcId, codeName, querySortKeys);
	}

	@Override
	public List<SysDef> getSysDefsBySrc(List<ObjectId> srcIdList, SysDefTypeCodeName codeName) {
		SysDefQuerySortKey querySortKey = new SysDefQuerySortKey(SysDefQuerySortKey.Key.FIELD_NO, true);
		List<SysDefQuerySortKey> querySortKeys = new ArrayList<SysDefQuerySortKey>();
		querySortKeys.add(querySortKey);
		return getSysDefsBySrc(srcIdList, codeName, querySortKeys);
	}

	@Override
	public List<SysDef> getSysDefsBySrcAndCndtItem(ObjectId srcId,
			SysDefTypeCodeName codeName, List<ObjectId> cndtItemIds) {
		// TODO Auto-generated method stub
		SysDefQuerySortKey querySortKey = new SysDefQuerySortKey(SysDefQuerySortKey.Key.ID, true);
		List<SysDefQuerySortKey> querySortKeys = new ArrayList<SysDefQuerySortKey>();
		querySortKeys.add(querySortKey);
		return getSysDefsBySrc(srcId, codeName, querySortKeys,cndtItemIds);
	}

	@Override
	public List<TeSysDef> listSysDefByCndtItem(SysDefTypeCodeName codeName, List<ObjectId> cndtItemIds) {
		if (codeName == null) {
			throw BusinessException.initExc("参数校验：codeName为null");
		}
		/*if (CollectionUtils.isEmpty(cndtItemIds)){
			throw BusinessException.initExc("参数校验：cndtItemIds为null或空");
		}*/
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), codeName.getId()));
		if(CollectionUtils.isNotEmpty(cndtItemIds)) {
			conds.add(new DC_I<>(DFN.sysDef_cndtItems.dot(DFN.common_cid), cndtItemIds));
		}
		return sysDefDao.findByFieldAndConds(conds, null, Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n()));
	}

	@Override
	public void updateTeSysDefs(List<TeSysDef> sysDefs) {
		for (TeSysDef sysDef : sysDefs) {
			sysDefDao.update(sysDef);
		}
	}

	@Override
	public List<SysDef> getSysDefsBySrc(ObjectId srcId, SysDefTypeCodeName codeName,
			List<SysDefQuerySortKey> querySortKeys) {
		if (srcId == null) {
			throw new BaseException("参数校验：srcId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcId(srcId, defType.getId(), querySortKeys);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	public List<SysDef> getSysDefsBySrc(List<ObjectId> srcIdList, SysDefTypeCodeName codeName,
										List<SysDefQuerySortKey> querySortKeys) {
		if (srcIdList == null || srcIdList.size() == 0) {
			throw new BaseException("参数校验：srcIdList为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcId(srcIdList, defType.getId(), querySortKeys);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	public List<SysDef> getSysDefsBySrc(ObjectId srcId, SysDefTypeCodeName codeName,
			List<SysDefQuerySortKey> querySortKeys,List<ObjectId> cndtItemIds) {
		if (srcId == null) {
			throw new BaseException("参数校验：srcId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if(null == cndtItemIds || cndtItemIds.size() == 0){
			throw new BaseException("参数校验：cndtItemIds为null或为空");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcId(srcId, defType.getId(), querySortKeys,cndtItemIds);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}
	@Override
	public List<SysDef> getSysDefsBySrc(ObjectId srcId, SysDefTypeCodeName codeName,
			List<SysDefQuerySortKey> querySortKeys, Boolean isInvisible) {
		if (srcId == null) {
			throw new BaseException("参数校验：srcId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcId(srcId, defType.getId(), querySortKeys, isInvisible);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<SysDef> queryProvince(ObjectId id, String provCodeName) {
		List<SysDef> sysDefs = new ArrayList<>();
		com.mongodb.client.MongoCollection<org.bson.Document> collection = mongoTemplate.getCollection("sysDefRoleUser");

		org.bson.Document query = new org.bson.Document();
		query.put("isValid", true);
		query.put("defType.defTypeCodeName", SysDefTypeConstants.PROV_CODENAME);
		if (StringUtil.isNotNull(id)) {
			query.put("roleUser.userId", id);
		}

		DistinctIterable<ObjectId> distinct = collection.distinct("defId", query, ObjectId.class);

		for (ObjectId defId : distinct) {
			if (defId == null) {
				continue;
			}
			TeSysDef sysDefById = sysDefDao.getSysDefById(defId);
			if (null == sysDefById){
				continue;
			}
			if (StringUtil.isNotNull(provCodeName)) {
				if (sysDefById.getCodeName().equals(provCodeName)) {
					sysDefs.add(SysDef.converSysDef(sysDefById));
				}
			} else {
				sysDefs.add(SysDef.converSysDef(sysDefById));
			}
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> queryBigRegion(List<ObjectId> provIdList) {
		List<SysDef> sysDefs = new ArrayList<>();
		Map<String, TeSysDef> map = new HashMap<>();
		for (ObjectId objectId : provIdList) {
			TeSysDef sysDefById = sysDefDao.getSysDefById(objectId);
			map.put(sysDefById.getDefName(), sysDefById);
		}
		for (String s : map.keySet()) {
			sysDefs.add(SysDef.converSysDef(map.get(s)));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> queryRegion(List<ObjectId> provIdList) {
		List<SysDef> sysDefs = new ArrayList<>();
		Map<String, TeSysDef> map = new HashMap<>();
		for (ObjectId objectId : provIdList) {
			TeSysDef sysDefById = sysDefDao.getSysDefById(objectId);
			if (sysDefById != null && null != sysDefById.getCndtItems()) {
				List<TeIdNameCn> cndtItems = sysDefById.getCndtItems();
				for (TeIdNameCn cndtItem : cndtItems) {
					if (null != cndtItem && null != cndtItem.getCid()) {
						TeSysDef region = sysDefDao.getSysDefById(cndtItem.getCid());
						map.put(region.getDefName(), region);
					}
				}
			}
		}
		for (String s : map.keySet()) {
			sysDefs.add(SysDef.converSysDef(map.get(s)));
		}
		return sysDefs;
	}
	@Override
	public List<SysDef> getSysDefsBySrcs(List<ObjectId> srcIds, SysDefTypeCodeName codeName) {
		if (srcIds == null || srcIds.isEmpty()) {
			throw new BaseException("参数校验：srcIds为null或者为空");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcIds(srcIds, defType.getId());
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsBySrcs(List<ObjectId> srcIds, SysDefTypeCodeName codeName, List<SysDefQuerySortKey> querySortKeys) {
		if (srcIds == null || srcIds.isEmpty()) {
			throw new BaseException("参数校验：srcIds为null或者为空");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcIds(srcIds, defType.getId(),querySortKeys);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsBySrcsIsCheck(List<ObjectId> srcIds, SysDefTypeCodeName codeName, boolean isCheck) {
		if (srcIds == null || srcIds.isEmpty()) {
			throw new BaseException("参数校验：srcIds为null或者为空");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcIdsIsCheck(srcIds, defType.getId(), isCheck);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsBySrcsCodeNameIsCheck(List<ObjectId> srcIds, SysDefTypeCodeName defTypeCodeName,
			List<String> codeNames, boolean isCheck) {
		if (srcIds == null || srcIds.isEmpty()) {
			throw new BaseException("参数校验：srcIds为null或者为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，defTypeCodeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsBySrcIdsCodeNameIsCheck(srcIds, defType.getId(), codeNames, isCheck);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByDefType(SysDefTypeCodeName codeName) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefType(sysDefType.getId());
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		// 按照addTime排序
		Collections.sort(sysDefs, new Comparator<SysDef>() {
			@Override
			public int compare(SysDef o1, SysDef o2) {
				if(null == o1 && null == o2 ) {
					return 0;
				}
				if(null == o1 && null != o2) {
					return -1;
				}
				if(null != o1 && null == o2) {
					return 1;
				}
				Date o1addTime = o1.getAddTime();
				Date o2addTime = o2.getAddTime();
				if (o1addTime == null && o2addTime == null) {
					return 0;
				}
				if (o1addTime == null && o2addTime != null) {
					return -1;
				}
				if (o1addTime != null && o2addTime == null) {
					return 1;
				}
				return o1addTime.compareTo(o2addTime);
			}
		});
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByDefTypeAndName(SysDefTypeCodeName codeName) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId),sysDefType.getId()));
		Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defName.getName());

		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds,sort);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}



	@Override
	public List<SysDef> getSysDefsByDefType(SysDefTypeCodeName codeName, List<SysDefQuerySortKey> querySortKeys) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefType(sysDefType.getId(), querySortKeys);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByDefTypeAndStd(SysDefTypeCodeName codeName) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefTypeAndStd(sysDefType.getId());
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByDefTypeAndName(SysDefTypeCodeName codeName, String name) {
		return getSysDefsByDefTypeAndName(codeName, name, false);
	}

	@Override
	public List<SysDef> getSysDefsByDefTypeAndName(SysDefTypeCodeName codeName, String name, boolean useLike) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if (name == null) {
			throw new BaseException("参数校验：name为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}

		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefTypeAndName(sysDefType.getId(), name, useLike);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByParentAndName(ObjectId parentId, SysDefTypeCodeName codeName, String name) {

		if (parentId == null) {
			throw new BaseException("参数校验：parentId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if (StringUtil.isNull(name)) {
			throw new BaseException("参数校验：name为null");
		}

		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}

		SysDef parentDef = getSysDefById(parentId);
		if (parentDef == null) {
			throw new BaseException("父定义不存在，parentId：" + parentId.toHexString());
		}

		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByParentAndName(parentId, defType.getId(), name);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByParent(ObjectId parentId, SysDefTypeCodeName codeName) {
		return getSysDefsByParent(parentId, codeName, null);
	}

	@Override
	public List<SysDef> getSysDefsByParents(List<ObjectId> parentIds, SysDefTypeCodeName defTypeCodeName) {
		if (parentIds == null || parentIds.isEmpty()) {
			throw new BaseException("参数校验：parentIds为null或者为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}

		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}

		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByParents(parentIds, defType.getId());
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<TeSysDef> getTeSysDefsByParents(List<ObjectId> parentIds, SysDefTypeCodeName defTypeCodeName) {
		if (parentIds == null || parentIds.isEmpty()) {
			throw new BaseException("参数校验：parentIds为null或者为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		long step1 = System.currentTimeMillis();
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		long step2 = System.currentTimeMillis();
		System.out.println("getTeSysDefsByParents 的 getSysDefTypeByCodeName方法"+(step2-step1));
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}

		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByParents(parentIds, defType.getId());
		if(teSysDefs == null){
			return new ArrayList<>();
		}
		return teSysDefs;
	}

	@Override
	public List<SysDef> getSysDefsByParent(ObjectId parentId, SysDefTypeCodeName codeName,
			List<SysDefQuerySortKey> querySortKeys) {
		if (parentId == null) {
			throw new BaseException("参数校验：parentId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}

		return getDefsByParent(parentId, codeName.getValue(), querySortKeys);

	}

	@Override
	public List<TeSysDef> getTeSysDefsByIds(List<ObjectId> ids) {
		if (ids == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByIds(ids);
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		return teSysDefs;
	}

	@Override
	public SysDef getSysDefByDefCode(String defCode, SysDefTypeCodeName defTypeCodeName) {
		if (defCode == null) {
			throw new BaseException("参数校验：defCode为null");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefByDefCode(defCode, defType.getId());
		if (teSysDefs == null || teSysDefs.size() == 0) {
			return null;
		}
		return SysDef.converSysDef(teSysDefs.get(0));
	}

	@Override
	public SysDef getSysDefByCodeName(String codeName, SysDefTypeCodeName defTypeCodeName) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		TeSysDef teSysDef = sysDefDao.getSysDefByCodeName(codeName, defType.getId());
		if (teSysDef == null) {
			return null;
		}
		return SysDef.converSysDef(teSysDef);
	}

	@Override
	public List<SysDef> getSysDefsByCodeNames(List<String> codeNames, SysDefTypeCodeName defTypeCodeName) {
		if (codeNames == null || codeNames.isEmpty()) {
			throw new BaseException("参数校验：codeNames为null或者codeNames为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByCodeNames(codeNames, defType.getId());
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefByDefNames(List<String> defNames,
			SysDefTypeCodeName defTypeCodeName) {
		if (defNames == null || defNames.isEmpty()) {
			throw new BaseException("参数校验：names为null或者codeNames为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefNames(defNames, defType.getId());
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	/**
	 * 创建定义
	 *
	 * @param codeName
	 * @param srcDefId
	 * @param prdCtlgName
	 * @param parentId
	 * @param whitePaperLevelId
	 * @param defNo
	 * @param addUser
	 * @return
	 */
	@Override
	public SysDef createDef4ParentId(SysDefTypeCodeName codeName, ObjectId srcDefId, String prdCtlgName, ObjectId parentId,
									 ObjectId whitePaperLevelId, Integer defNo, TeUser addUser){
		SysDef srcDef = getSysDefById(srcDefId);
		SysDefType sysDefType = new SysDefType();
		sysDefType.setCodeName(SysDefTypeCodeName.PRDCTLG.name());
		SysDef sysDef = new SysDef();
		sysDef.setSrcDef(srcDef);
		sysDef.setDefName(prdCtlgName);
		sysDef.setParentDefId(parentId);
		sysDef.setDefNo(defNo);
		sysDef.setIsStd(false);
		sysDef.setIsValid(true);
		sysDef.setAddUser(addUser);
		sysDef.setAddTime(new Date());

		if(whitePaperLevelId != null) {
			SysDef whitePaperLevel = getSysDefById(whitePaperLevelId);
			List<TeIdNameCn> cndtItems = new ArrayList<>();
			TeIdNameCn cndtItem = new TeIdNameCn();
			cndtItem.setCid(whitePaperLevel.getId());
			cndtItem.setCodeName(whitePaperLevel.getCodeName());
			cndtItem.setName(whitePaperLevel.getDefName());
			cndtItems.add(cndtItem);
			sysDef.setCndtItems(cndtItems);
		}
		sysDef = createSysDef(codeName, sysDef);
		TeSysDef def = getTeSysDefById(sysDef.getId());
		if(StringUtil.isNotNull(def.getParentDefPath())){
			if(codeName.equals(SysDefTypeCodeName.PRDCTLG)){
				def.setCodeName(def.getParentDefPath() + def.getId().toHexString() + ",");
			}
			def.setParent2SelfIds(StringUtil.transIds2List(def.getParentDefPath() + def.getId().toHexString(), ",", ObjectId.class));
		} else {
			if(codeName.equals(SysDefTypeCodeName.PRDCTLG)) {
				def.setCodeName("," + def.getId() + ",");
			}
			def.setParent2SelfIds(Collections.singletonList(def.getId()));
		}
		updateSysDef(def);
		return SysDef.converSysDef(def);
	}

	@Override
	public SysDef createSysDef(SysDefTypeCodeName codeName, SysDef sysDef) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if (sysDef == null) {
			throw new BaseException("参数校验：sysDef为null");
		}
		TeSysDefType type = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (type == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		TeDefType SysDefType = new TeDefType();
		SysDefType.setDefTypeId(type.getId());
		SysDefType.setDefTypeName(type.getDefTypeName());
		SysDefType.setDefTypeCodeName(type.getCodeName());

		TeSysDef teSysDef = sysDef.toTeSysDef();
		teSysDef.setDefType(SysDefType);

		// 如果存在父定义，将整个树的节点路径更新
		if (StringUtil.isNotNull(teSysDef.getParentDefId())) {
			TeSysDef parentDef = this.getTeSysDefById(teSysDef.getParentDefId());
			if (parentDef == null) {
				throw new BaseException("父定义不存在或者已经失效");
			}

			// 获取当前的路径
			String currentPath = this.generateTreePath(parentDef);
			teSysDef.setParentDefPath(currentPath);

			if(Objects.isNull(teSysDef.getDefNo())){
				List<SysDefQuerySortKey> querySortKeys = new ArrayList<>();
				SysDefQuerySortKey defNoSort = new SysDefQuerySortKey(SysDefQuerySortKey.Key.FIELD_NO, false);
				querySortKeys.add(defNoSort);
				List<TeSysDef> c = getTeSysDefsByParent(teSysDef.getParentDefId(), codeName.getValue(), querySortKeys);
				if(CollectionUtils.isEmpty(c)){
					teSysDef.setDefNo(1);
				} else {
					Integer defNo = c.get(0).getDefNo();
					defNo = Objects.nonNull(defNo) ? defNo + 1 : 1;
					teSysDef.setDefNo(defNo);
				}
			}
		}

		sysDefDao.save(teSysDef);
		return SysDef.converSysDef(teSysDef);
	}

	@Override
	public List<SysDef> createSysDefs(SysDefTypeCodeName codeName, List<SysDef> sysDefs) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		if (sysDefs == null || sysDefs.isEmpty()) {
			throw new BaseException("参数校验：sysDefs为null或者为空");
		}

		TeSysDefType type = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (type == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}

		TeDefType defTypeMap = new TeDefType();

		defTypeMap.setDefTypeId(type.getId());
		defTypeMap.setDefTypeName(type.getDefTypeName());
		defTypeMap.setDefTypeCodeName(type.getCodeName());

		// Map<String, Object> defTypeMap = SysDefType.toSysDefMap(new
		// SysDefType(type));
		List<TeSysDef> teSysDefs = new ArrayList<>();
		for (SysDef def : sysDefs) {
			TeSysDef teSysDef = def.toTeSysDef();
			// 如果存在父定义，将整个树的节点路径更新
			if (StringUtil.isNotNull(teSysDef.getParentDefId())) {
				TeSysDef parentDef = this.getTeSysDefById(teSysDef.getParentDefId());
				if (parentDef == null) {
					throw new BaseException("父定义不存在或者已经失效");
				}
				// 将父节点路径强制更新
				// this.renovateTreePathsByDef(parentDef);

				// 获取当前的路径
				String currentPath = this.generateTreePath(parentDef);
				teSysDef.setParentDefPath(currentPath);
			}

			teSysDef.setDefType(defTypeMap);
			teSysDefs.add(teSysDef);
		}

		sysDefDao.bulkSave(teSysDefs);

		List<SysDef> result = new ArrayList<>();
		for (TeSysDef teSysDef : teSysDefs) {
			result.add(SysDef.converSysDef(teSysDef));
		}
		return result;
	}

	@Override
	public void removeSysDef(ObjectId id) {
		if (id == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		sysDefDao.remove(id);
	}

	@Override
	public void removeSysDefs(List<ObjectId> ids) {
		if (ids == null || ids.isEmpty()) {
			throw new BaseException("参数校验：定义id为null或者为空");
		}
		sysDefDao.remove(ids);
	}

	@Override
	public void updateSysDef(TeSysDef def) {
		if (def == null || def.getId() == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		// 更新时将树形刷新一遍

		// 如果存在父定义，将整个树的节点路径更新
		if (StringUtil.isNotNull(def.getParentDefId())) {
			TeSysDef parentDef = this.getTeSysDefById(def.getParentDefId());
			if (parentDef == null) {
				throw new BaseException("父定义不存在或者已经失效");
			}
			// 将父节点路径强制更新
			// this.renovateTreePathsByDef(parentDef);

			// 获取当前的路径
			String currentPath = this.generateTreePath(parentDef);
			def.setParentDefPath(currentPath);

		}

		sysDefDao.update(def);
	}

	@Override
	public TeSysDef moveSysDef(ObjectId nodeId, ObjectId parentId, ObjectId targetNodeId, String action,
                             SysDefServiceImpl.UpdateParentCallBack callBack){
		TeSysDef node = getTeSysDefById(nodeId);
		SysDef target = getSysDefById(targetNodeId);
		Integer targetDefNo = "add".equals(action) || target.getDefNo() == null ?   0 : target.getDefNo();
		if(Objects.isNull(node.getDefType()) || StringUtil.isNull(node.getDefType().getDefTypeCodeName())){
			throw new BaseException("节点类型错误");
		}
		List<TeSysDef> sysDefList = getTeSysDefsByParent(parentId, node.getDefType().getDefTypeCodeName(), null);
		if(null != sysDefList && !sysDefList.isEmpty()){
			for(TeSysDef sysDef : sysDefList){
				//移动到目标之前,将大于等于目标的目录的defNo都加一
				if("before".equals(action)){
					if(sysDef.getDefNo() != null && sysDef.getDefNo() >= targetDefNo){
						sysDef.setDefNo(sysDef.getDefNo() + 1);
						updateSysDef(sysDef);
					}
				}
				//移动到目标之后,将大于目标的目录的defNo都加一
				else if("after".equals(action)){
					if(sysDef.getDefNo() != null && sysDef.getDefNo() > targetDefNo){
						sysDef.setDefNo(sysDef.getDefNo() + 1);
						updateSysDef(sysDef);
					}
				}
				else if("add".equals(action)){
					if(sysDef.getDefNo() != null && sysDef.getDefNo() > targetDefNo){
						targetDefNo = sysDef.getDefNo();
					}
				}
			}
			if("before".equals(action)){
				node.setDefNo(targetDefNo);
			}else{
				node.setDefNo(targetDefNo + 1);
			}
		}else{
			node.setDefNo(0);
		}
		sysDefDao.update(node);

		node.setParentDefId(parentId);

		updateDefParentPath(node, callBack);

		return node;
	}

	private void updateDefParentPath(TeSysDef def, SysDefServiceImpl.UpdateParentCallBack callBack) {
		if (def == null || def.getId() == null) {
			throw new BaseException("参数校验：定义id为null");
		}

		// 获取当前的路径
		String currentPath = "";
		// 如果存在父定义，将整个树的节点路径更新
		if (StringUtil.isNotNull(def.getParentDefId())) {
			TeSysDef parentDef = this.getTeSysDefById(def.getParentDefId());
			if (parentDef == null) {
				throw new BaseException("父定义不存在或者已经失效");
			}

			// 获取当前的路径
			currentPath = this.generateTreePath(parentDef);
		}

		if(currentPath.equals(def.getParentDefPath())){
			return;
		}

		String codeName = "";
		if (StringUtil.isNull(currentPath)) {
			codeName = COMMA + def.getId().toString() + COMMA;
		} else {
			codeName = currentPath + def.getId().toString() + COMMA;
		}

		def.setParentDefPath(currentPath);
		if(Objects.nonNull(def.getDefType()) && SysDefTypeConstants.PRDCTLG_CODENAME.equals(def.getDefType().getDefTypeCodeName())){
			def.setCodeName(codeName);
		}
		List<ObjectId> parent2SelfIds = StringUtil.transIds2List(codeName, ",", ObjectId.class);
		def.setParent2SelfIds(parent2SelfIds);
		sysDefDao.update(def);
		if(Objects.nonNull(callBack)){
			callBack.call(def);
		}

		// 更新子目录
		List<TeSysDef> sysDefList = this.getTeSysDefsByParent(def.getId(), def.getDefType().getDefTypeCodeName(), null);
		if(null !=sysDefList && sysDefList.size()>0){
			for (TeSysDef sd : sysDefList) {
				updateDefParentPath(sd, callBack);
			}
		}
	}

	@Override
	public void updateSysDefs(List<TeSysDef> sysDefs) {
		if (sysDefs == null || sysDefs.isEmpty()) {
			throw new BaseException("参数校验：定义为null或者空");
		}
		for (TeSysDef sysDef : sysDefs) {
			if (sysDef.getId() == null) {
				throw new BaseException("参数校验：定义id为null");
			}
		}
		List<TeSysDef> teSysDefs = new ArrayList<>();
		for (TeSysDef def : sysDefs) {

			// 更新时将树形刷新一遍

			// 如果存在父定义，将整个树的节点路径更新
			if (StringUtil.isNotNull(def.getParentDefId())) {
				TeSysDef parentDef = this.getTeSysDefById(def.getParentDefId());
				if (parentDef == null) {
					throw new BaseException("父定义不存在或者已经失效");
				}
				// 将父节点路径强制更新
				// this.renovateTreePathsByDef(parentDef);

				// 获取当前的路径
				String currentPath = this.generateTreePath(parentDef);
				def.setParentDefPath(currentPath);

			}

			teSysDefs.add(def);
		}
		sysDefDao.bulkUpdate(teSysDefs);
	}

	@Override
	public void updateSysDefName(ObjectId id, String defName) {
		if (id == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		if (defName == null) {
			throw new BaseException("参数校验：定义名称为null");
		}
		sysDefDao.updateSysDefName(id, defName);
	}

	@Override
	public void updateSrcDef(ObjectId id, TeSrcDef srcDef) {
		if (id == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		if (srcDef == null) {
			throw new BaseException("参数校验：定义名称为null");
		}
		sysDefDao.updateSrcDef(id, srcDef);
	}

	@Override
	public List<SysDef> getSysDefTreeRootsBySrc(ObjectId srcId, SysDefTypeCodeName codeName) {
		return getSysDefTreeRootsBySrc(srcId, codeName, null);
	}

	@Override
	public List<SysDef> getSysDefTreeRootsBySrc(ObjectId srcId, SysDefTypeCodeName codeName,
			List<SysDefQuerySortKey> querySortKeys) {
		if (srcId == null) {
			throw new BaseException("参数校验：srcId为null");
		}

		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}

		// 获取定义类型
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}

		List<TeSysDef> teSysDefs = sysDefDao.getSysDefRootsBySrcId(srcId, defType.getId(), querySortKeys);
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}

		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	/**
	 * 当前顺序为从下向上(包含当前的节点)
	 */
	@Override
	public List<TeSysDef> getTeDefParentList(ObjectId defId) {
		List<TeSysDef> rets = new ArrayList<TeSysDef>();

		while (null != defId) {
			TeSysDef def = this.getTeSysDefById(defId);
			if (null == def)
				break;

			rets.add(def);
			defId = def.getParentDefId();
		}
		return rets;
	}

	//	@Override
	//	public SysDefTree getSysDefTree(ObjectId rootId) {
	//		return getSysDefTree(rootId, null);
	//	}

	//	@Override
	//	public List<SysDef> getAllDefListByRootId(ObjectId rootId, Sort sort) {
	//		if (null == rootId) {
	//			throw new BaseException("参数校验：rootId为null");
	//		}
	//
	//		// 获取所有节点
	//		List<TeSysDef> teSysDefs = getAllDefByRootId(rootId, sort);
	//
	//		List<SysDef> sysDefs = new ArrayList<>();
	//		for (TeSysDef teDef : teSysDefs) {
	//			sysDefs.add(new SysDef(teDef));
	//		}
	//		return sysDefs;
	//	}

	//	@Override
	//	public SysDefTree getSysDefTreeBySrc(ObjectId srcId, ObjectId rootId, SysDefTypeCodeName codeName,
	//			List<SysDefQuerySortKey> querySortKeys) {
	//		if (srcId == null) {
	//			throw new BaseException("参数校验：srcId为null");
	//		}
	//
	//		if (rootId == null) {
	//			throw new BaseException("参数校验：rootId为null");
	//		}
	//
	//		if (codeName == null) {
	//			throw new BaseException("参数校验：codeName为null");
	//		}
	//
	//		// 获取定义类型
	//		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
	//		if (defType == null) {
	//			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
	//		}
	//
	//		// 获取所有节点
	//		List<TeSysDef> teSysDefs = getAllCurrentAndChildrenByDefId(rootId, srcId, defType.getId(), querySortKeys);
	//		/* sysDefDao.getSysDefsBySrcId(srcId, defType.getId(), querySortKeys); */
	//
	//		// TODO 将所有节点的树路径刷新一遍
	//		if (teSysDefs.isEmpty()) {
	//			return null;
	//		}
	//
	//		// 获取根节点
	//		SysDef rootDef = null;
	//		Iterator<TeSysDef> iter = teSysDefs.iterator();
	//		while (iter.hasNext()) {
	//			TeSysDef teDef = iter.next();
	//			if (teDef.getId().toHexString().equals(rootId.toHexString())) {
	//				rootDef = new SysDef(teDef);
	//				iter.remove();
	//				break;
	//			}
	//		}
	//
	//		if (rootDef == null) {
	//			throw new BaseException("无法获取到树根节点，rootId：" + rootId.toHexString());
	//		}
	//
	//		SysDefTree tree = new SysDefTree(rootDef);
	//		buildDefTreeWithAllNodes(teSysDefs, tree);
	//		return tree;
	//	}

	private void buildDefTreeWithAllNodes(List<TeSysDef> allSysDefNodes, SysDefTree tree) {
		SysDef current = tree.getCurrent();
		if (current == null) {
			return;
		}
		if (allSysDefNodes.isEmpty()) {
			return;
		}
		ObjectId id = current.getId();
		List<SysDefTree> children = null;

		Iterator<TeSysDef> iter = allSysDefNodes.iterator();
		while (iter.hasNext()) {
			TeSysDef teDef = iter.next();
			ObjectId pId = teDef.getParentDefId();
			String parentDefPath = teDef.getParentDefPath();
			if (pId != null && pId.toHexString().equals(id.toHexString())) {
				if (children == null) {
					children = new ArrayList<>();
				}
				SysDefTree subTree = new SysDefTree(SysDef.converSysDef(teDef));
				subTree.setParent(current);
				subTree.setParentDefPath(parentDefPath);
				children.add(subTree);
				iter.remove();
			}
		}
		if (children != null) {
			tree.setChildren(children);
			tree.setExpandable(true);

			for (SysDefTree child : tree.getChildren()) {
				buildDefTreeWithAllNodes(allSysDefNodes, child);
			}
		}
	}


	@Override
	public SysDefTree getSysDefTreeById(ObjectId id) {
		return getSysDefTreeById(id, null);
	}

	@Override
	public SysDefTree getSysDefTreeByParentId(ObjectId treeId,String sbuId,Sort sort) {
		if (null == treeId) {
			throw new BaseException("参数校验：树根节点为null");
		}

		TeSysDef root = this.getTeSysDefById(treeId);
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef_parent2SelfIds,treeId));
		if (StringUtil.isNotNull(sbuId)){
			conds.add(new DC_E(DFN.sysDef_cndtItems.dot(DFN.common_cn),sbuId));
		}
		conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
		List<TeSysDef> allNodes = sysDefDao.findByConds(conds, sort);
		allNodes.add(0,root);
		if (CollectionUtils.isEmpty(allNodes)) {
			return null;
		}

		// 获取根节点
		SysDef rootDef = null;
		Iterator<TeSysDef> iter = allNodes.iterator();
		while (iter.hasNext()) {
			TeSysDef def = iter.next();
			if (def.getId().equals(treeId)) {
				rootDef = SysDef.converSysDef(def);
				iter.remove();
				break;
			}
		}

		if (null == rootDef) {
			throw new BaseException("无法获取到树根节点，rootId：" + treeId.toHexString());
		}

		SysDefTree tree = new SysDefTree(rootDef);
		buildDefTreeWithAllNodes(allNodes, tree);
		return tree;
	}

	@Override
	public SysDefTree getSysDefTreeById(ObjectId treeId, Sort sort) {
		if (null == treeId) {
			throw new BaseException("参数校验：树根节点为null");
		}

		// 获取所有节点
		List<TeSysDef> allNodes = __getAllDefByRootId(treeId, sort);

		// TODO 将所有节点的树路径刷新一遍
		if (CollectionUtils.isEmpty(allNodes)) {
			return null;
		}

		// 获取根节点
		SysDef rootDef = null;
		Iterator<TeSysDef> iter = allNodes.iterator();
		while (iter.hasNext()) {
			TeSysDef def = iter.next();
			if (def.getId().equals(treeId)) {
				rootDef = SysDef.converSysDef(def);
				iter.remove();
				break;
			}
		}

		if (null == rootDef) {
			throw new BaseException("无法获取到树根节点，rootId：" + treeId.toHexString());
		}

		SysDefTree tree = new SysDefTree(rootDef);
		buildDefTreeWithAllNodes(allNodes, tree);
		return tree;

		//return getSysDefTreeBySrc(srcId, treeId, codeName, querySortKeys);
	}

	@Override
	public SysDefTree getLevel2SysDefTreeById(ObjectId treeId, Sort sort) {
		if (null == treeId) {
			throw new BaseException("参数校验：树根节点为null");
		}
		//获取根节点
		List<IDbCondition> rootConds = new ArrayList<>();
		rootConds.add(new DC_E(DFN.common__id,treeId));
		List<TeSysDef> rootNodes = sysDefDao.findByConds(rootConds, null);

		if (rootNodes.isEmpty()) {
			return null;
		}

		// 获取根节点
		SysDef rootDef = SysDef.converSysDef(rootNodes.get(0));
		SysDefTree tree = new SysDefTree(rootDef);
		ObjectId rootParentId = rootDef.getParentDefId();
		if(rootParentId != null){
			SysDef rootParentDef = getSysDefById(rootParentId);
			if(rootParentDef == null){
				throw new BaseException("无法获取到根节点的父节点，rootId：" + treeId.toHexString() + "、rootParentId：" + rootParentId.toHexString());
			}
			tree.setParent(rootParentDef);
			tree.setParentDefPath(rootDef.getParentDefPath());
		}

		String regex = "," + treeId.toHexString() + ",([A-Za-z0-9]+,){0,1}$";
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeConstants.PRDCTLG_CODENAME));
		conds.add(new DC_L(DFN.sysDef__parentDefPath, regex));
		Sort subSort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.getName());
		List<TeSysDef> allNodes = sysDefDao.findByConds(conds, subSort);

		if(allNodes != null && !allNodes.isEmpty()){
			buildDefTreeWithAllNodes(allNodes, tree);
		}

		//查询第三集是否存在，如果存在，给第二级设置Expandable = true
		List<SysDefTree> secondTree = tree.getChildren();
		if(CollectionUtils.isNotEmpty(secondTree)){
			//获取第二级树的id
			for(SysDefTree sysDefTree:secondTree){
				List<SysDefTree> threeTree = sysDefTree.getChildren();
				if(threeTree == null || threeTree.isEmpty()){
					sysDefTree.setExpandable(false);
					sysDefTree.setExpanded(false);
				}else if(threeTree.size() >0){
					sysDefTree.setExpandable(false);
					sysDefTree.setExpanded(false);
					sysDefTree.setIsLeaf(false);
					sysDefTree.setChildren(null);
				}
			}
		}


		return tree;

		//return getSysDefTreeBySrc(srcId, treeId, codeName, querySortKeys);
	}

	@Override
	public SysDefTree getLevel2SysDefTreeById(ObjectId treeId, Sort sort, String defTypeCodeName) {
		if (null == treeId) {
			throw new BaseException("参数校验：树根节点为null");
		}
		//获取根节点
		List<IDbCondition> rootConds = new ArrayList<>();
		rootConds.add(new DC_E(DFN.common__id,treeId));
		List<TeSysDef> rootNodes = sysDefDao.findByConds(rootConds, null);

		if (rootNodes.isEmpty()) {
			return null;
		}

		// 获取根节点
		SysDef rootDef = SysDef.converSysDef(rootNodes.get(0));
		SysDefTree tree = new SysDefTree(rootDef);
		ObjectId rootParentId = rootDef.getParentDefId();
		if(rootParentId != null){
			SysDef rootParentDef = getSysDefById(rootParentId);
			if(rootParentDef == null){
				throw new BaseException("无法获取到根节点的父节点，rootId：" + treeId.toHexString() + "、rootParentId：" + rootParentId.toHexString());
			}
			tree.setParent(rootParentDef);
			tree.setParentDefPath(rootDef.getParentDefPath());
		}

		String regex = "," + treeId.toHexString() + ",([A-Za-z0-9]+,){0,1}$";
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCodeName));
		conds.add(new DC_L(DFN.sysDef__parentDefPath, regex));
		Sort subSort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.getName());
		List<TeSysDef> allNodes = sysDefDao.findByConds(conds, subSort);

		if(allNodes != null && !allNodes.isEmpty()){
			buildDefTreeWithAllNodes(allNodes, tree);
		}

		//查询第三集是否存在，如果存在，给第二级设置Expandable = true
		List<SysDefTree> secondTree = tree.getChildren();
		if(CollectionUtils.isNotEmpty(secondTree)){
			//获取第二级树的id
			for(SysDefTree sysDefTree:secondTree){
				List<SysDefTree> threeTree = sysDefTree.getChildren();
				if(threeTree == null || threeTree.isEmpty()){
					sysDefTree.setExpandable(false);
					sysDefTree.setExpanded(false);
				}else if(threeTree.size() >0){
					sysDefTree.setExpandable(false);
					sysDefTree.setExpanded(false);
					sysDefTree.setIsLeaf(false);
					sysDefTree.setChildren(null);
				}
			}
		}


		return tree;

		//return getSysDefTreeBySrc(srcId, treeId, codeName, querySortKeys);
	}
	
	@Override
	public SysDefTree getVisibleSysDefTreeById(ObjectId id) {
		return getVisibleSysDefTreeById(id, null);
	}

	@Override
	public SysDefTree getVisibleSysDefTreeById(ObjectId treeId, Sort sort) {
		if (null == treeId) {
			throw new BaseException("参数校验：树根节点为null");
		}

		// 获取所有节点
		List<TeSysDef> allNodes = getAllVisibleDefByRootId(treeId, sort);

		if (allNodes.isEmpty()) {
			return null;
		}

		// 获取根节点
		SysDef rootDef = null;
		Iterator<TeSysDef> iter = allNodes.iterator();
		while (iter.hasNext()) {
			TeSysDef def = iter.next();
			if (def.getId() != null && def.getId().equals(treeId)) {
				rootDef = SysDef.converSysDef(def);
				iter.remove();
				break;
			}
		}

		if (null == rootDef) {
			throw new BaseException("无法获取到树根节点，rootId：" + treeId.toHexString());
		}

		SysDefTree tree = new SysDefTree(rootDef);
		buildDefTreeWithAllNodes(allNodes, tree);
		return tree;

		//return getSysDefTreeBySrc(srcId, treeId, codeName, querySortKeys);
	}
	
	private List<TeSysDef> getAllVisibleDefByRootId(ObjectId rootId, Sort sort) {
		TeSysDef _root = this.getTeSysDefById(rootId);
		if (_root == null) {
			throw new BaseException(DEF_NOTEXIST_ERROR);
		}

		String treePath = generateTreePath(_root);

		List<TeSysDef> rets = new ArrayList<>();
		rets.add(_root);

		List<TeSysDef> allChildList = sysDefDao.getVisbleDefListByParentPathRegex3(treePath, sort);
		if (null != allChildList && allChildList.size() > 0) {
			rets.addAll(allChildList);
		}
		return rets;
	}

	@Override
	public List<SysDefVo> getSysDefsForRpt()
	{
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsForRpt();
		List<SysDefVo> sysDefs = new ArrayList<>();
		for(TeSysDef teSysDef : teSysDefs)
		{
			sysDefs.add(new SysDefVo(SysDef.converSysDef(teSysDef)));
		}

		return sysDefs;
	}

	/**
	 * 从"sysDef"中取出defType.
	 * defTypeCodeName为entity下的，且isRptTopicEntity为true的，且isValid为true下的清单
	 */
	@Override
	public List<SysDefVo> getRptTopics() {
		List<TeSysDef> teSysDefs = sysDefDao.getRptTopics();
		List<SysDefVo> sysDefs = new ArrayList<>();
		for (TeSysDef teSysDef : teSysDefs) {
			sysDefs.add(new SysDefVo(SysDef.converSysDef(teSysDef)));
		}

		return sysDefs;
	}

	@Override
	public List<SysDef> getRecSysDefsByParent(ObjectId parentId, SysDefTypeCodeName codeName) {
		List<SysDef> returnCtlgs = new ArrayList<>();
		List<SysDef> ctlgs = this.getSysDefsByParent(parentId, codeName);
		if (null == ctlgs || ctlgs.size() == 0) {
			return null;
		} else {
			returnCtlgs.addAll(ctlgs);
			for (SysDef ctlg : ctlgs) {
				List<SysDef> newCtlgs = this.getSysDefsByParent(ctlg.getId(), codeName);
				if (null != newCtlgs) {
					returnCtlgs.addAll(newCtlgs);
				}
			}
		}
		return returnCtlgs;
	}

	@Override
	/**
	 * 马晓峰要求使用parentDefPath构建当前定义的所有上层定义
	 */
	public List<SysDef> getDefParentList(ObjectId defId) {
		List<SysDef> rets = new ArrayList<SysDef>();
		List<SysDef> sortRets = new ArrayList<SysDef>();
		Map<ObjectId, SysDef> sortMap = new HashMap<ObjectId, SysDef>();
		SysDef sysDef = this.getSysDefById(defId);
		if (null != sysDef) {
			List<ObjectId> parentDefIds = StringUtil.transIds2List(sysDef.getParentDefPath(), ",", ObjectId.class);
			if (null == parentDefIds) {
				parentDefIds = new ArrayList<ObjectId>();
			}
			parentDefIds.add(defId);
			rets = this.getSysDefsByIds(parentDefIds);
			//将查出的结果按parentDefPath顺序结果输出
			for (int i = 0; i < rets.size(); i++) {
				SysDef ret = rets.get(i);
				sortMap.put(ret.getId(), ret);
			}
			for (ObjectId oId : parentDefIds) {//将查出的结果按parentDefIds顺序结果输出
				sortRets.add(sortMap.get(oId));
			}
		}
		return sortRets;
	}

	@Override
	public List<SysDef> getStdSysDefsByDefType(SysDefTypeCodeName codeName) {
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (sysDefType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getStdSysDefsByDefType(sysDefType.getId());
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public TeSysDef getStdSysDefByCodeName(String codeName, SysDefTypeCodeName defTypeCodeName) {
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		Query query = new Query();
		Criteria criteria = Criteria.where("defType.defTypeId").is(defType.getId()).and("codeName").is(codeName)
				.and("isValid").is(true).and("isStd").is(true);
		query.addCriteria(criteria);
		return mongoTemplate.findOne(query, TeSysDef.class);
	}

	@Override
	public TeSysDef getStdSysDefByCodeName(String codeName, String defTypeCodeName) {
		if (StringUtil.isNull(defTypeCodeName)) {
			throw new BaseException("定义类型不能为null ");
		}
		Query query = new Query();
		Criteria criteria = Criteria.where("defType.defTypeCodeName").is(defTypeCodeName).and("codeName").is(codeName)
				.and("isValid").is(true).and("isStd").is(true);
		query.addCriteria(criteria);
		return mongoTemplate.findOne(query, TeSysDef.class);
	}

	@Override
	public List<SysDef> getSysDefByParentDefId(ObjectId parentDefId) {
		if (parentDefId == null) {
			throw new BaseException("ParentDefId 不能为空");
		}
		Query query = new Query();
		Criteria criteria = Criteria.where("parentDefId").is(parentDefId).and("isValid").is(true);
		query.addCriteria(criteria);
		List<TeSysDef> teSysDef = mongoTemplate.find(query, TeSysDef.class);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef tesysDef : teSysDef) {
			sysDefs.add(SysDef.converSysDef(tesysDef));
		}
		return sysDefs;
	}

	/**
	 * 根据定义生成当前节点下的路径结构，用以查询节点下的数据
	 * 
	 * @param def
	 * @return
	 */
	private String generateTreePath(TeSysDef def) {

		if (null == def) {
			return null;
		}
		if (null == def.getParentDefId()) {
			return COMMA + def.getId().toString() + COMMA;
		} else {
			return def.getParentDefPath() + def.getId().toString() + COMMA;
		}
	}

	/**
	 * 根据当前定义强制刷新当前节点的路径结构(定义需为原始数据，不能进行set，否则会更新进数据库)
	 * 
	 * @param def
	 * @return
	 */
	@SuppressWarnings("unused")
	private void renovateTreePathByDef(TeSysDef def) {

		if (null == def) {
			return;
		}
		List<TeSysDef> parentDef = getTeDefParentList(def.getId());

		// 反转集合，顺序更新为从根节点到子节点
		Collections.reverse(parentDef);

		// size=0的情况为当前定义没有(这种情况不存在)，size=1的情况为当前定义没有父节点，即为根节点
		if (parentDef.size() == 0 || parentDef.size() == 1) {
			def.setParentDefPath(null);
			sysDefDao.update(def);
		} else {

			// 把当前定义从集合中剔除
			parentDef.remove(parentDef.size() - 1);
			StringBuffer treePath = new StringBuffer(COMMA);
			for (TeSysDef _def : parentDef) {
				treePath.append(_def.getId().toString()).append(COMMA);
			}
			def.setParentDefPath(treePath.toString());
			sysDefDao.update(def);
		}
	}

	/**
	 * 根据当前定义强制刷新所有上层节点的路径结构(定义需为原始数据，不能进行set，否则会更新进数据库)
	 * 
	 * @param def
	 * @return
	 */
	private void renovateTreePathsByDef(TeSysDef def) {

		if (null == def) {
			return;
		}
		List<TeSysDef> parentDef = getTeDefParentList(def.getId());

		// 反转集合，顺序更新为从根节点到子节点
		Collections.reverse(parentDef);
		List<TeSysDef> updateDefs = new ArrayList<>();

		// size=0的情况为当前定义没有(这种情况不存在)，size=1的情况为当前定义没有父节点，即为根节点
		if (parentDef.size() == 0 || parentDef.size() == 1) {
			def.setParentDefPath(null);
			updateDefs.add(def);
		} else {

			// 循环进行设置path
			for (int i = 0; i < parentDef.size(); i++) {
				// 根节点
				if (i == 0) {
					TeSysDef rootDef = parentDef.get(i);
					rootDef.setParentDefPath(null);
					updateDefs.add(rootDef);

				} else {
					StringBuffer treePath = new StringBuffer(COMMA);
					for (int j = 0; j < i; j++) {
						treePath.append(parentDef.get(j).getId().toString()).append(COMMA);
					}
					TeSysDef currentDef = parentDef.get(i);
					currentDef.setParentDefPath(treePath.toString());
					updateDefs.add(currentDef);

					// 当前传入参数的值也进行更新，为最后一个节点
					if (i == parentDef.size() - 1) {
						def.setParentDefPath(treePath.toString());
					}

				}
			}
		}

		this.updateTeSysDefs(updateDefs);
	}

	/**
	 * 根据多个节点获取下面所有子节点
	 * 
	 * @return
	 */
	@Override
	public List<TeSysDef> getSubTreeByDefId(List<ObjectId> defIds) {
		List<TeSysDef> defs = this.getTeSysDefsByIds(defIds);
		if (defs == null || defs.size() == 0) {
			throw new BaseException("定义不存在或者已经失效");
		}

		// 获取treePath的正则表达式
		StringBuffer treePath = new StringBuffer("^(");
		for (TeSysDef def : defs) {
			treePath.append(generateTreePath(def)).append("|");
		}

		// 删除最后一个'|'字符
		treePath.deleteCharAt(treePath.length() - 1);
		// 拼接最后一个括号
		treePath.append(")");

		return sysDefDao.getDefListByParentPathRegex(treePath.toString());
	}

	@Override
	public List<TeSysDef> getSubTreeByP2sIds(List<ObjectId> defIds) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_I<ObjectId>(DFN.common_p2sIds, defIds));
		return sysDefDao.findByConds(conds, null);
	}
	
	@Override
	public List<TeSysDef> getSubTreeByP2sIds(List<ObjectId> defIds, List<DbFieldName> projectFields) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_I<ObjectId>(DFN.common_p2sIds, defIds));
		return sysDefDao.findByFieldAndConds(conds, projectFields, null);
	}

	/**
	 * 根据当前节点获取当前及其下面所有子节点
	 * @param rootId
	 * @param sort
	 * @return
	 */
	@Override
	public List<TeSysDef> __getAllDefByRootId(ObjectId rootId, Sort sort) {
		TeSysDef _root = this.getTeSysDefById(rootId);
		if (_root == null) {
			return null;
		}

		String treePath = generateTreePath(_root);

		List<TeSysDef> rets = new ArrayList<>();
		rets.add(_root);

		List<TeSysDef> allChildList = sysDefDao.getDefListByParentPathRegex3(treePath, sort);
		if (null != allChildList) {
			rets.addAll(allChildList);
		}
		return rets;
	}

	@Override
	public List<SysDef> getAllDefByRootId(ObjectId rootId, Sort sort) {
		List<SysDef> rets = new ArrayList<SysDef>();

		List<TeSysDef> defs = __getAllDefByRootId(rootId, sort);
		if (null == defs) {
			return rets;
		}

		for (TeSysDef def : defs) {
			if (null == def)
				continue;
			rets.add(SysDef.converSysDef(def));
		}
		return rets;
	}

	/**
	 * 根据当前节点获取下面所有子节点
	 * 
	 * @return
	 */
	@Override
	public List<TeSysDef> getAllChildrenByDefId(ObjectId defId) {
		TeSysDef currentDef = this.getTeSysDefById(defId);
		if (currentDef == null) {
			throw new BaseException(DEF_NOTEXIST_ERROR);
		}
		String treePath = generateTreePath(currentDef);

		return sysDefDao.getDefListByParentPathRegex2(treePath.toString());
	}

	//	/**
	//	 * 根据当前节点获取所有平级节点
	//	 */
	//	private List<TeSysDef> getSiblingsByDefId(ObjectId defId) {
	//		List<TeSysDef> list = new ArrayList<>();
	//		TeSysDef currentDef = this.getTeSysDefById(defId);
	//		if (currentDef == null) {
	//			throw new BaseException(DEF_NOTEXIST_ERROR);
	//		}
	//		String treePath = currentDef.getParentDefPath();
	//		if (StringUtil.isNull(treePath)) {
	//
	//			// 当前节点为根节点时，只返回当前定义
	//			list.add(currentDef);
	//			return list;
	//		}
	//
	//		list = sysDefDao.getDefListByParentPath(treePath.toString());
	//		return list;
	//	}

	/**
	 * 根据当前节点获取当前及上层所有的节点
	 */
	@Override
	public List<TeSysDef> getAncestorsByDefId(ObjectId defId) {

		List<TeSysDef> list = new ArrayList<>();
		TeSysDef currentDef = this.getTeSysDefById(defId);
		if (currentDef == null) {
			throw new BaseException(DEF_NOTEXIST_ERROR);
		}
		String treePath = currentDef.getParentDefPath();
		// 根节点只返回当前数据
		if (StringUtil.isNull(treePath)) {
			list.add(currentDef);
			return list;
		}
		String[] arrs = treePath.split(COMMA);
		List<ObjectId> ids = new ArrayList<>();
		for (String arr : arrs) {
			if (StringUtil.isNotNull(arr)) {
				ids.add(new ObjectId(arr));
			}
		}
		List<TeSysDef> _list = getTeSysDefsByIds(ids);

		// 根据树的深度进行排序
		for (ObjectId _id : ids) {
			for (TeSysDef _def : _list) {
				if (_id.equals(_def.getId())) {
					list.add(_def);
					break;
				}
			}
		}
		list.add(currentDef);
		return list;
	}

	/**
	 * 强制刷新数据库定义中所有的树
	 */
	@Override
	public void renovateTreePaths() {
		Query query = new Query();
		query.addCriteria(Criteria.where("isValid").is(true).and("parentDefId").ne(null));
		List<TeSysDef> defs = mongoTemplate.find(query, TeSysDef.class);
		for (TeSysDef def : defs) {
			// 有些数据id与parentDefId相同，则不进行处理
			if (def.getId().equals(def.getParentDefId())) {
				continue;
			}
			this.renovateTreePathsByDef(def);
		}
	}

	@Override
	public List<TeSysDef> getSysDefsBySrcAndSort(ObjectId srcId, List<SysDefQuerySortKey> querySortKeys) {
		if (StringUtil.isNull(srcId)) {
			throw new BaseException(DEF_NOTEXIST_ERROR);
		}
		if (StringUtil.isNull(querySortKeys)) {
			throw new BaseException("排序定义字段为空");
		}
		return sysDefDao.getSysDefsBySrcAndSort(srcId, querySortKeys);
	}

	@Override
	public List<TeSysDef> getChildrenByDefId(String id) {
		TeSysDef currentDef = this.getTeSysDefById(new ObjectId(id));
		if (currentDef == null) {
			throw new BaseException(DEF_NOTEXIST_ERROR);
		}
		String treePath = generateTreePath(currentDef);
		return sysDefDao.getDefListByParentPath(treePath.toString());
	}

	@Override
	public List<TeSysDef> fuzzyQueryByNameOrCodeName(SysDefTypeCodeName defTypeCn, String keyword) {
		if (null == defTypeCn || null == keyword) {
			return Collections.emptyList();
		}

		List<IDbCondition> conds = new ArrayList<>();

		IDbCondition defTypeCnCond = new DbEqualCondition(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName),
				defTypeCn.getValue());
		conds.add(defTypeCnCond);

		List<DbFieldName> fs = new ArrayList<DbFieldName>();
		fs.add(DFN.sysDef__codeName);
		fs.add(DFN.sysDef__defName);
		conds.add(new DbFuzzyCondition(fs, keyword.toString()));

		List<TeSysDef> defs = sysDefDao.findByConds(conds, null, new Pager(0, 20));
		return defs;

		//		Map<Object, TeSysDef> uniqueMap = new HashMap<Object, TeSysDef>();
		//		for(TeSysDef teSysDefs : codeNameTeSysDefs){
		//			uniqueMap.put(teSysDefs.getId(), teSysDefs);
		//		}
		//		condValues.clear();
		//		//合同名称模糊查询
		//		List<IDbCondition> defNameConds = new ArrayList<>();
		//		DbLikeCondition defNameCond = new DbLikeCondition(DbFieldName.sysDef__codeName, keyWord.get("defName").toString());
		//		defNameConds.add(defTypeCodeNameCond);
		//		defNameConds.add(defNameCond);
		//		List<TeSysDef> defNameTeSysDefs = sysDefDao.findByConds(defNameConds, new ArrayList<Sort>());//1530
		//		//去重
		//		for(TeSysDef teSysDefs : defNameTeSysDefs){
		//			uniqueMap.put(teSysDefs.getId(), teSysDefs);
		//		}
		//		
		//		List<Object> prjCode=new ArrayList<Object>();
		//		for(Object key : uniqueMap.keySet()){
		//			TeSysDef teSysDef = uniqueMap.get(key);
		//			if(StringUtil.isNotNull(teSysDef)){
		//				prjCode.add(teSysDef.getCodeName());
		//			}
		//		}
		//		return prjCode;
	}

	@Override
	public List<TeSysDef> getSysByDefTypeCodeNameAndSrcDefCodeName(String prj_quality_para, String sbuId) {
		
		List<IDbCondition> conds = new ArrayList<>();
		
		if (StringUtil.isNotNull(prj_quality_para)) {
			DbEqualCondition defTypeCodeNameCond = new DbEqualCondition(
					DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), prj_quality_para);
			conds.add(defTypeCodeNameCond);
		}
		
		if (StringUtil.isNotNull(sbuId)) {
			DbEqualCondition srcDefCodeNameCond = new DbEqualCondition(
					DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefCodeName), sbuId);
			conds.add(srcDefCodeNameCond);
		}
		
		DbEqualCondition isValidCond = new DbEqualCondition(DbFieldName.sysDef__isValid, true);
		conds.add(isValidCond);

		Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysDef__defNo.n());
		return sysDefDao.findByConds(conds, sort);
	}

	@Override
	public List<TeSysDef> getSysDefByDefTypeCodeNameAndSrcDefId(SysDefTypeCodeName sysDefTypeCodeName, String prjId,
			String verId) {
		List<IDbCondition> conds = new ArrayList<>();
		DbEqualCondition defTypeCodeNameCond = new DbEqualCondition(
				DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), sysDefTypeCodeName.getValue());
		DbEqualCondition srcDefIdCond = new DbEqualCondition(
				DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefId), new ObjectId(prjId));
		DbEqualCondition verIdCond = new DbEqualCondition(DbFieldName.sysDef__verMgtId, new ObjectId(verId));
		DbEqualCondition isValidCond = new DbEqualCondition(DbFieldName.sysDef__isValid, true);
		conds.add(defTypeCodeNameCond);
		conds.add(srcDefIdCond);
		conds.add(verIdCond);
		conds.add(isValidCond);
		Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n());
		return sysDefDao.findByConds(conds, sort);
	}

	@Override
	public List<TeSysDef> getRootSysDefByDefTypeCodeNameAndSrcDefId(SysDefTypeCodeName sysDefTypeCodeName, String prjId,
																	String verId) {
		List<IDbCondition> conds = new ArrayList<>();
		DbEqualCondition defTypeCodeNameCond = new DbEqualCondition(
				DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), sysDefTypeCodeName.getValue());
		DbEqualCondition srcDefIdCond = new DbEqualCondition(
				DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefId), new ObjectId(prjId));
		DbEqualCondition verIdCond = new DbEqualCondition(DbFieldName.sysDef__verMgtId, new ObjectId(verId));
		DbEqualCondition isValidCond = new DbEqualCondition(DbFieldName.sysDef__isValid, true);
		DbEqualCondition parentDefIdCond = new DbEqualCondition(DbFieldName.sysDef__parentDefId, null);
		conds.add(defTypeCodeNameCond);
		conds.add(srcDefIdCond);
		conds.add(verIdCond);
		conds.add(isValidCond);
		conds.add(parentDefIdCond);
		return sysDefDao.findByConds(conds, null);
	}

	@Override
	public List<SysDef> getSysDefsByVerMgtId(ObjectId verMgtId) {
		List<IDbCondition> conds = new ArrayList<>();
		DbEqualCondition verIdCond = new DbEqualCondition(DbFieldName.sysDef__verMgtId, verMgtId);
		DbEqualCondition isValidCond = new DbEqualCondition(DbFieldName.sysDef__isValid, true);
		conds.add(verIdCond);
		conds.add(isValidCond);
		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, null);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef tesysDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(tesysDef));
		}
		return sysDefs;
	}

	@Override
	public void bachsave(List<TeSysDef> copys) {
		sysDefDao.batchSave(copys);
	}

	@Override
	public void save(TeSysDef sysDef) {
		sysDefDao.save(sysDef);
	}

	@Override
	public List<TeSysDef> getAllTeSysDefsByIds(ObjectId defId) {
		if (defId == null) {
			throw new BaseException("参数校验：定义id为null");
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefTpyeIds(defId);
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		return teSysDefs;
	}
	
	@Override
	public List<SysDef> getSysDefByAncestors(List<ObjectId> ancestorIds){
		
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefByAncestors(ancestorIds);
		List<SysDef> sysDefs = new ArrayList<>();
		for(TeSysDef teSysDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teSysDef));
		}
		
		return sysDefs;
	}

	/**
	 * 按层级获取目录树
	 * @param rootId 子树的根Id，它可能并不是整颗树的根，是子树的根
	 * @param level 以rootId为第0层，向下获取第level层的所有有权限的节点
	 * @param authedNodeIdSet 所有有权限的节点ID集合，一般是整颗树的有权限的节点ID集合
	 * @param defTypeCodeName 定义类型
	 * @return 目录树
	 */
	@Override
	public SysDefTree getSubTree(ObjectId rootId, Integer level, Set<ObjectId> authedNodeIdSet, String defTypeCodeName){

		if(rootId == null){
			throw new BaseException("rootId不能为null");
		}
		if(authedNodeIdSet == null || authedNodeIdSet.isEmpty()){
			return null;
		}
		if(level == null || level <= 0){
			level = 1;
		}

		// 获取子根节点
		SysDef rootDef = getSysDefById(rootId);
		if(rootDef == null){
			throw new BaseException("无法获取到根节点，rootId：" + rootId.toHexString());
		}

		SysDefTree tree = new SysDefTree(rootDef);
		ObjectId rootParentId = rootDef.getParentDefId();
		if(rootParentId != null){
			SysDef rootParentDef = getSysDefById(rootParentId);
			if(rootParentDef == null){
				throw new BaseException("无法获取到根节点的父节点，rootId：" + rootId.toHexString() + "、rootParentId：" + rootParentId.toHexString());
			}
			tree.setParent(rootParentDef);
			tree.setParentDefPath(rootDef.getParentDefPath());
		}

		String regex = "," + rootId.toHexString() + ",([A-Za-z0-9]+,){0," + level + "}$";
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCodeName));
		conds.add(new DC_L(DFN.sysDef__parentDefPath, regex));
		Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.getName());
		List<TeSysDef> allNodes = sysDefDao.findByConds(conds, sort);

		if(allNodes != null && !allNodes.isEmpty()){
			buildDefTreeWithAllNodes(allNodes, tree);
		}

		Set<ObjectId> authedNodeAllParentIdSet = new HashSet<>();
		conds.clear();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_I<>(DFN.sysDef_id, new ArrayList<>(authedNodeIdSet)));
		List<TeSysDef> authedNodes = sysDefDao.findByConds(conds, sort);
		if (authedNodes != null && !authedNodes.isEmpty()) {
			for (TeSysDef authedNode : authedNodes) {
				List<ObjectId> authedNodeParentIds = StringUtil.transIds2List(authedNode.getParentDefPath(), ",", ObjectId.class);
				if (authedNodeParentIds != null && !authedNodeParentIds.isEmpty()) {
					authedNodeAllParentIdSet.addAll(authedNodeParentIds);
				}
			}
		}

		buildAuthedTree(tree, authedNodeIdSet, authedNodeAllParentIdSet, 0, level);
		return tree;
	}

	private SysDefTree buildAuthedTree(SysDefTree node, Set<ObjectId> authedNodeIdSet,
		Set<ObjectId> authedNodeAllParentIdSet, int currentLevel, int maxLevel){
		if( null == node ){
			return null;
		}
		List<SysDefTree> visitedChildren = new ArrayList<SysDefTree>();
		if(node.getChildren() != null){
			for(SysDefTree child : node.getChildren() ){
				SysDefTree retChild = buildAuthedTree(child, authedNodeIdSet, authedNodeAllParentIdSet, currentLevel + 1, maxLevel);
				// 这个孩子节点返回非空，即说明该孩子节点至少有可见性。
				if( null != retChild ){
					visitedChildren.add(retChild);
				}
			}
		}

		// 判断当前节点是否有权限：
		boolean thisNodeAuthed = checkNodeAuthed(node, authedNodeIdSet);
		// 判断当前节点是否可见：
		// 即判断是否存在配置有权限的节点在当前节点下面，
		// 也就是判断 当前节点是否在任意一个有权限的节点的parentPath路径上。如果是，则为当前节点下面有有权限的节点，那当前节点为可见
		boolean thisNodeVisible = authedNodeAllParentIdSet.contains(node.getCurrent().getId());

		if(thisNodeAuthed || thisNodeVisible){
			// 如果当前层级已经是maxLevel 以下层级了，则不需要改节点的任何属性，只要告诉上层递归调用：该孩子节点可见，节点需要被展开即可
			if(currentLevel > maxLevel){
				return node;
			}
			// 如果当前层级已经是maxLevel 了，则不需要 设置孩子节点，只需要设置是否可以展开的标识
			else if(currentLevel ==  maxLevel){
				node.setChildren(null);
				node.setExpandable(!visitedChildren.isEmpty());
			}
			else{
				node.setChildren(visitedChildren.isEmpty() ? null : visitedChildren);
			}
			node.setAuthed(thisNodeAuthed);
			return node;
		}

		return null;
	}

	/**
	 * 获取节点是否有权限
	 * @param node
	 * @param authedNodeIdSet
	 * @return 是否有权限
	 */
	private boolean checkNodeAuthed(SysDefTree node,  Set<ObjectId> authedNodeIdSet){
		// 检查当前节点的parentPath中的节点以及当前节点本身 中的任意一个 是否包含有在authedNodeIdSet集合中，如果是，则说明当前节点有权限。
		if(node == null){
			return false;
		}

		if(authedNodeIdSet == null || authedNodeIdSet.isEmpty()){
			return false;
		}

		SysDef current = node.getCurrent();
		if(current == null){
			return false;
		}
		if(authedNodeIdSet.contains(current.getId())){
			return true;
		}

		List<ObjectId> parentDefIds = StringUtil.transIds2List(current.getParentDefPath(), ",", ObjectId.class);
		if(parentDefIds == null || parentDefIds.isEmpty()){
			return false;
		}

		for (ObjectId parentId : parentDefIds){
			if(authedNodeIdSet.contains(parentId)){
				return true;
			}
		}

		return false;
	}

	@Override
	public List<SysDef> getSysDefByCndtItemCode(String defTypeCode, String srcDefCode, String cndtItemCode){
		List<IDbCondition> conds = new ArrayList<>();
		if(StringUtil.isNotNull(defTypeCode)) {
			conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCode));
		}
		if(StringUtil.isNotNull(srcDefCode)) {
			conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), srcDefCode));
		}
		if (StringUtil.isNotNull(cndtItemCode)){
			conds.add(new DC_E(DFN.sysDef_cndtItems.dot(DFN.sysDef__codeName), cndtItemCode));
		}
		conds.add(new DC_E(DFN.sysDef__isValid, true));
		
		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, null);
		List<SysDef> sysDefs = new ArrayList<>();
		if(teSysDefs != null && teSysDefs.size() > 0) {
			for(TeSysDef teSysDef : teSysDefs) {
				sysDefs.add(SysDef.converSysDef(teSysDef));
			}
		}
		return sysDefs;
	}

	@Override
	public List<SysDef> getSysDefByCndtItemCodes(String defTypeCode, String srcDefCode, List<String> cndtItemCodes) {
		List<IDbCondition> conds = new ArrayList<>();
		if(StringUtil.isNotNull(defTypeCode)) {
			conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCode));
		}
		if(StringUtil.isNotNull(srcDefCode)) {
			conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), srcDefCode));
		}
		if (null != cndtItemCodes && cndtItemCodes.size() > 0){
			conds.add(new DC_I<String>(DFN.sysDef_cndtItems.dot(DFN.sysDef__codeName), cndtItemCodes));
		}
		conds.add(new DC_E(DFN.sysDef__isValid, true));

		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n()));
		List<SysDef> sysDefs = new ArrayList<>();
		if(teSysDefs != null && teSysDefs.size() > 0) {
			for(TeSysDef teSysDef : teSysDefs) {
				sysDefs.add(SysDef.converSysDef(teSysDef));
			}
		}
		return sysDefs;
	}

	/*
	 * 根据CndtItemCode查询数据，CndtItemCode为null或传递的参数
	 */
	@Override
	public List<SysDef> getSysDefByCndtItemCodeAndNull(String defTypeCode, String srcDefCode, String cndtItemCode) {
		List<IDbCondition> conds = new ArrayList<>();
		if(StringUtil.isNotNull(defTypeCode)) {
			conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCode));
		}
		if(StringUtil.isNotNull(srcDefCode)) {
			conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), srcDefCode));
		}
		List<String> cndtItemCodes = new ArrayList<>();
		cndtItemCodes.add(null);
		if (StringUtil.isNotNull(cndtItemCode)){
			cndtItemCodes.add(cndtItemCode);
		}
		conds.add(new DC_I<String>(DFN.sysDef_cndtItems.dot(DFN.sysDef__codeName), cndtItemCodes));
		conds.add(new DC_E(DFN.sysDef__isValid, true));

		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysDef__defName);
		fieldNameList.add(DFN.sysDef__parentDefId);
		fieldNameList.add(DFN.sysDef__parentDefPath);
		fieldNameList.add(DFN.sysDef__codeName);

		List<TeSysDef> teSysDefs = sysDefDao.findByFieldAndConds(conds, fieldNameList);
		List<SysDef> sysDefs = new ArrayList<>();
		List<SysDef> result = new ArrayList<>();
		if(teSysDefs != null && teSysDefs.size() > 0) {
			for(TeSysDef teSysDef : teSysDefs) {
				sysDefs.add(SysDef.converSysDef(teSysDef));
			}
			List<SysDef> parentDefs = sysDefs.stream().filter(sysDef -> sysDef.getParentDefId() == null).collect(Collectors.toList());
			for (SysDef parent : parentDefs){
				convertTree(parent,sysDefs);
				result.add(parent);
			}
		}
		return result;
	}

	private void convertTree(SysDef parentDef,List<SysDef> sysDefs){
		List<SysDef> childrens = new ArrayList<>();
		for (SysDef def : sysDefs){
			if (parentDef.getId().equals(def.getParentDefId())){
				childrens.add(def);
			}
		}

		if (CollectionUtils.isEmpty(childrens)){
			return;
		}
		parentDef.setChildren(childrens);
		for (SysDef children : childrens){
			convertTree(children,sysDefs);
		}
	}

	@Override
	public List<ObjectId> getSubDefIdsByParentDefId(ObjectId parentId){
		List<ObjectId> subSysDefIds = new ArrayList<ObjectId>();
		List<TeSysDef> outSysDefs = this.__getAllDefByRootId(parentId,null);
		if (CollectionUtils.isNotEmpty(outSysDefs)){
			for (TeSysDef teSysDef : outSysDefs) {
				subSysDefIds.add(teSysDef.getId());
			}
		}
	   return subSysDefIds;
	}
	
	@Override
	public List<TeSysDef> getSysDefBySrcAndName(String srcDefCode, String defName){
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), srcDefCode));
		conds.add(new DC_E(DFN.sysDef__defName, defName));
		conds.add(new DC_E(DFN.common_isValid, true));
		
		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, null);
		return teSysDefs;
	}
	
	@Override
	public List<SysDef> getSysDefByDefNameKeyWords(String keyWords, SysDefTypeCodeName defTypeCodeName) {
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：defTypeCodeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByDefNameKeyWords(keyWords, defType.getId(), 10);
		if (teSysDefs.isEmpty()) {
			return new ArrayList<>();
		}
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}

	@Override
	public List<SysDefTree> getSysDefTreeByNode(ObjectId nodeId, Sort sort) {
		SysDefTree tree = getLevel2SysDefTreeById(nodeId, null);
		List<SysDefTree> children = tree.getChildren();
		for(SysDefTree sysDefTree:children){
			sysDefTree.setAllowed(true);
		}
		return children;
	}
	@Override
	public List<SysDefTree> getSysDefTreeByNode(ObjectId nodeId, Sort sort, String defTypeCodeName) {
		SysDefTree tree = getLevel2SysDefTreeById(nodeId, null, defTypeCodeName);
		List<SysDefTree> children = tree.getChildren();
		for(SysDefTree sysDefTree:children){
			sysDefTree.setAllowed(true);
		}
		return children;
	}
	
	@Override
	public List<TeSysDef> getSysDefsWithHangUpCtlgByCodeNames(List<ObjectId> defIds){
		if(defIds == null || defIds.size() == 0) {
			return null;
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_I<ObjectId>(DFN.common_p2sIds, defIds));
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__hangedCtlgs.dot(DFN.common_cid), null, true));
		/*for(ObjectId defId : defIds) {
			codeNameConds.add(new DC_L(DFN.sysDef__codeName, defId.toHexString()));
		}*/
		return sysDefDao.findByConds(conds, null);
	}

	/**
	 * 根据源定义查询定义集合id列表
	 *
	 * @param srcIds   源定义id
	 * @param codeName 指定的定义类型
	 * @return
	 */
	@Override
	public List<ObjectId> getSysDefIdsBySrcs(List<ObjectId> srcIds, SysDefTypeCodeName codeName) {
		if (srcIds == null || srcIds.isEmpty()) {
			throw new BaseException("参数校验：srcIds为null或者为空");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<org.bson.Document> sql = new ArrayList<>();
		org.bson.Document sysDefMatch = new org.bson.Document();
		sysDefMatch.put(DFN.sysDef__isValid.n(), true);
		sysDefMatch.put(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n(), new org.bson.Document("$in", srcIds));
		sysDefMatch.put(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId).n(), defType.getId());
		sql.add(new org.bson.Document("$match", sysDefMatch));
		// 查询数据
		com.mongodb.client.MongoCursor<org.bson.Document> cursor = mongoTemplate.getCollection(DBT.SYS_DEF.n())
				.aggregate(sql).allowDiskUse(true).cursor();
		List<ObjectId> sysDefIds = new ArrayList<>();
		if (null != cursor) {
			while (cursor.hasNext()) {
				org.bson.Document next = cursor.next();
				Object sysDefId = next.get(DFN.sysDef_id.n());
				if (null != sysDefId && sysDefId instanceof ObjectId) {
					sysDefIds.add((ObjectId)sysDefId);
				}
			}
		}
		return sysDefIds;
	}

	@Override
	public Map<String,String> getParentDefPath(List<ObjectId> sysDefId){
		Map<String,String> result = new HashMap<String, String>();

		List<IDbCondition> sysDefConds = new ArrayList<>();
		sysDefConds.add(new DC_I<ObjectId>(DFN.sysDef_id,sysDefId));
		List<TeSysDef> defs = sysDefDao.findByConds(sysDefConds, null);

		List<ObjectId> parentDefListPathList = new ArrayList<ObjectId>();
		Map<ObjectId,String> parentPathMap = new HashMap<>();
		Map<ObjectId,List<ObjectId>> sysDef2ParentDefPathMap = new HashMap<>();
		//1.判断def的大小,如果为0,说明有定义
		if(!defs.isEmpty()){
			for(TeSysDef def:defs){
				String parentDefPathStr = def.getParentDefPath();
				//2.获取第一条数据,然后判断如果有parentDefPath说明不是第一级,如果没有说明已经是第一级目录了
				if(StringUtil.isNotNull(parentDefPathStr)){
					List<ObjectId> parentDefPathList = StringUtil.transIds2List(parentDefPathStr,",",ObjectId.class);
					parentDefPathList.add(def.getId());
					sysDef2ParentDefPathMap.put(def.getId(),parentDefPathList);
					parentDefListPathList.addAll(parentDefPathList);

				}else{
					sysDef2ParentDefPathMap.put(def.getId(),Arrays.asList(def.getId()));
					parentDefListPathList.add(def.getId());
				}
				sysDefConds.clear();
				sysDefConds.add(new DbInCondition<ObjectId>(DFN.sysDef_id,parentDefListPathList));
				List<TeSysDef> parentDefList = sysDefDao.findByConds(sysDefConds, null);
				//将查询到的所有的父类组成的集合放到Map中
				for(TeSysDef teSysDef:parentDefList){
					parentPathMap.put(teSysDef.getId(),teSysDef.getDefName());
				}
			}
			//组装result
			for(ObjectId objectId:sysDef2ParentDefPathMap.keySet()){
				List<ObjectId> parentPathId = sysDef2ParentDefPathMap.get(objectId);
				String parentFullPath = "";
				for(ObjectId parentId:parentPathId){
					parentFullPath += "/"+ parentPathMap.get(parentId);
				}
				result.put(objectId.toHexString(),parentFullPath);
			}
		}

		return result;
	}

	@Override
	public List<TeSysDef> getSysDefByFuzzy(String defName, SysDefTypeCodeName codeName) {
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defType.getCodeName()));
		List<DbFieldName> fs = new ArrayList<DbFieldName>();
		fs.add(DFN.sysDef__defName);
		fs.add(DFN.sysDef__codeName);
		if (StringUtil.isNotNull(defName)){
			conds.add(new DC_F(fs, defName));
		}
		return sysDefDao.findByConds(conds,null);
	}

	public interface UpdateParentCallBack{
		void call(TeSysDef def);
	}

	private List<TeSysDef> getTeSysDefsByParent(ObjectId parentId, String codeName,
																   List<SysDefQuerySortKey> querySortKeys) {
		if (parentId == null) {
			throw new BaseException("参数校验：parentId为null");
		}
		if (codeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}

		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName);
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName);
		}

		SysDef parentDef = getSysDefById(parentId);
		if (parentDef == null) {
			throw new BaseException("父定义不存在，parentId：" + parentId.toHexString());
		}

		return sysDefDao.getSysDefsByParent(parentId, defType.getId(), querySortKeys);
	}

	private List<SysDef> getDefsByParent(ObjectId parentId, String codeName,
										 List<SysDefQuerySortKey> querySortKeys) {

		List<TeSysDef> teSysDefs = getTeSysDefsByParent(parentId, codeName,	querySortKeys);
		List<SysDef> sysDefs = new ArrayList<>();
		for (TeSysDef teDef : teSysDefs) {
			sysDefs.add(SysDef.converSysDef(teDef));
		}
		return sysDefs;
	}
	
	@Override
	public List<ObjectId> querySysDefIds(ObjectId defTypeId, ObjectId srcDefId) {
		Assert.notNull(defTypeId, "defTypeId不能为null");
		Assert.notNull(srcDefId, "srcDefId不能为null");
		List<IDbCondition> conds = new ArrayList<>(3);
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), defTypeId));
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), srcDefId));
		conds.add(new DC_E(DFN.sysDef__isValid, true));
		List<DbFieldName> fieldNames = new ArrayList<>(1);
		fieldNames.add(DFN.sysDef_id);
		List<TeSysDef> sysDefs = sysDefDao.findByFieldAndConds(conds, fieldNames);
		if (org.springframework.util.CollectionUtils.isEmpty(sysDefs)) {
			return Collections.EMPTY_LIST;
		}
		List<ObjectId> sysDefIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
		return sysDefIds;
	}

	@Override
	public List<TeSysDef> querySysDefs(ObjectId defTypeId, ObjectId srcDefId, List<String> defNames) {
		if (defTypeId == null && srcDefId == null && CollectionUtils.isEmpty(defNames)) {
			return Collections.emptyList();
		}
		List<IDbCondition> conds = new ArrayList<>();
		if (defTypeId != null) {
			conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), defTypeId));
		}
		if (srcDefId != null) {
			conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), srcDefId));
		}
		if (CollectionUtils.isNotEmpty(defNames)) {
			conds.add(new DC_I<String>(DFN.sysDef__defName, defNames));
		}
		conds.add(new DC_E(DFN.sysDef__isValid, true));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDef_id);
		fieldNames.add(DFN.sysDef__defName);
		fieldNames.add(DFN.sysDef__codeName);
		fieldNames.add(DFN.sysDef__ccList);
		fieldNames.add(DFN.sysDef_parent2SelfIds);
		fieldNames.add(DFN.sysDef_cndtItems);
		fieldNames.add(DFN.sysDef_defDesc);
		return sysDefDao.findByFieldAndConds(conds, fieldNames);
	}

	@Override
	public Map<ObjectId, String> queryPrdCtlgId2PrdCtlgPathNameMapping(List<ObjectId> sysDefIds) {
		if (CollectionUtils.isEmpty(sysDefIds)) {
			return Collections.emptyMap();
		}
		Map<ObjectId, String> prdCtlgId2PrdCtlgPathNameMapping = new HashMap<>();
		// 1、根据产品目录id查出所有产品目录
		List<TeSysDef> sysDefs = getTeSysDefsByIds(sysDefIds);
		// 2、查出产品目录的parent2SelfIds
		List<ObjectId> allPrdCltgIds = sysDefs.stream().filter(s -> s.getParent2SelfIds() != null).map(TeSysDef::getParent2SelfIds)
				.flatMap(Collection::stream).distinct().collect(Collectors.toList());
		// 3、查询id为parent2SelfIds的所有产品目录
		List<TeSysDef> allSysDefs = getTeSysDefsByIds(allPrdCltgIds);
		Map<ObjectId, TeSysDef> idToSysDefMapping = allSysDefs.stream().collect(Collectors.toMap(TeSysDef::getId, Function.identity()));
		sysDefIds.stream().filter(p -> idToSysDefMapping.get(p) != null && idToSysDefMapping.get(p).getParent2SelfIds() != null).forEach(prdCtlgId -> {
			TeSysDef sysDef = idToSysDefMapping.get(prdCtlgId);
			StringBuilder prdCtlgPathName = new StringBuilder();
			List<ObjectId> parent2SelfIds = sysDef.getParent2SelfIds();
			parent2SelfIds.stream().forEach(p -> {
				TeSysDef sysDefObj = idToSysDefMapping.get(p);
				prdCtlgPathName.append(sysDefObj.getDefName()).append("/");
			});
			if (prdCtlgPathName.length() > 0) {
				String prdCtlgPathNameStr = prdCtlgPathName.substring(0, prdCtlgPathName.length() - 1);
				prdCtlgId2PrdCtlgPathNameMapping.put(prdCtlgId, prdCtlgPathNameStr);
			}
		});
		return prdCtlgId2PrdCtlgPathNameMapping;
	}

	@Override
    public void createDbcPortal(SysDefDbcPortalVo sysDef, TeUser addUser){
	    Assert.notNull(sysDef.getSrcDefId(), "类型不能为空");
	    Assert.notNull(sysDef.getDefName(), "名称不能为空");
        TeSysDef srcDef = sysDefDao.getSysDefById(sysDef.getSrcDefId());
        Assert.notNull(srcDef, "类型不存在");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), sysDef.getSrcDefId()));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.USER_CUST_PARA_DEFTYPE_ID));
        conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
        conds.add(new DC_E(DFN.sysDef__defName, sysDef.getDefName()));
        long count = sysDefDao.countByConds(conds);
        if(count > 0){
            throw BusinessException.initExc("名称已存在");
        }

        Integer defNo = 0;
        conds.remove(conds.size()-1);
        conds.remove(conds.size()-2);
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.sysDef__defNo);
        List<TeSysDef> maxDefNoDef = sysDefDao.findByFieldAndConds(conds, fieldNames,
                Sort.by(Sort.Direction.DESC, DFN.sysDef__defNo.n()), new Pager(0, 1));
        if(CollectionUtils.isNotEmpty(maxDefNoDef)){
            defNo = maxDefNoDef.get(0).getDefNo() + 1;
        }

        TeSysDef portal = new TeSysDef();
        portal.setIsValid(true);
        portal.setAddTime(DateUtil.now());
        portal.setAddUser(addUser);
        portal.setDefType(new TeDefType(SysDefConstants.USER_CUST_PARA_DEFTYPE_ID,
                SysDefConstants.USER_CUST_PARA_DEFTYPE_NAME,
                SysDefConstants.USER_CUST_PARA_DEFTYPE_CODENAME));
        portal.setSrcDef(new TeSrcDef(srcDef.getId(), srcDef.getDefName(), srcDef.getCodeName()));
        portal.setDefName(sysDef.getDefName());
        portal.setIcon(sysDef.getIcon());
        portal.setValue(sysDef.getValue());
        portal.setDefDesc(sysDef.getDefDesc());
        portal.setDefNo(defNo);
        sysDefDao.save(portal);
    }

    @Override
    public void updateDbcPortal(SysDefDbcPortalVo sysDef){
        Assert.notNull(sysDef.getId(), "id不能为空");
        Assert.notNull(sysDef.getSrcDefId(), "类型不能为空");
        TeSysDef srcDef = sysDefDao.getSysDefById(sysDef.getSrcDefId());
        Assert.notNull(srcDef, "类型不存在");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), sysDef.getSrcDefId()));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.USER_CUST_PARA_DEFTYPE_ID));
        conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
        conds.add(new DC_E(DFN.common__id, sysDef.getId()));
        List<TeSysDef> portalList = sysDefDao.findByFieldAndConds(conds, null);
        Assert.notEmpty(portalList, "配置数据不存在");
        List<UpdataData> updates = new ArrayList<>();
        if(StringUtils.isNotEmpty(sysDef.getDefName())){
            updates.add(new UpdataData(DFN.sysDef__defName, sysDef.getDefName()));
        }
        if(StringUtils.isNotEmpty(sysDef.getIcon())){
            updates.add(new UpdataData(DFN.sysDef_icon, sysDef.getIcon()));
        }
        if(StringUtils.isNotEmpty(sysDef.getValue())){
            updates.add(new UpdataData(DFN.sysDef__value, sysDef.getValue()));
        }
        if(StringUtils.isNotEmpty(sysDef.getDefDesc())){
            updates.add(new UpdataData(DFN.sysDef__defDesc, sysDef.getDefDesc()));
        }
        if(CollectionUtils.isNotEmpty(updates)) {
            sysDefDao.updateById(sysDef.getId(), updates);
        }
    }

    @Override
    public void deleteDbcPortal(SysDefDbcPortalVo sysDef){
        Assert.notNull(sysDef.getId(), "id不能为空");
        Assert.notNull(sysDef.getSrcDefId(), "类型不能为空");
        TeSysDef srcDef = sysDefDao.getSysDefById(sysDef.getSrcDefId());
        Assert.notNull(srcDef, "类型不存在");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), sysDef.getSrcDefId()));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.USER_CUST_PARA_DEFTYPE_ID));
        conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
        conds.add(new DC_E(DFN.common__id, sysDef.getId()));
        List<TeSysDef> portalList = sysDefDao.findByFieldAndConds(conds, null);
        Assert.notEmpty(portalList, "配置数据不存在");
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.common_isValid, false));
        sysDefDao.updateById(sysDef.getId(), updates);
    }

    @Override
    public List<SysDefDbcPortalVo> queryDbcPortal(ObjectId srcDefId){
        Assert.notNull(srcDefId, "类型不能为空");
        TeSysDef srcDef = sysDefDao.getSysDefById(srcDefId);
        Assert.notNull(srcDef, "类型不存在");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), srcDefId));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.USER_CUST_PARA_DEFTYPE_ID));
        conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.common__id);
        fieldNames.add(DFN.common_isValid);
        fieldNames.add(DFN.sysDef__srcDef);
        fieldNames.add(DFN.sysDef__defName);
        fieldNames.add(DFN.sysDef_icon);
        fieldNames.add(DFN.sysDef__value);
        fieldNames.add(DFN.sysDef__defDesc);
        fieldNames.add(DFN.sysDef__defNo);
        List<TeSysDef> portalList = sysDefDao.findByFieldAndConds(conds, fieldNames,
                Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n()));
        if(CollectionUtils.isEmpty(portalList)){
            return Collections.emptyList();
        }
        List<SysDefDbcPortalVo> list = new ArrayList<>();
        for (TeSysDef def : portalList) {
            SysDefDbcPortalVo portal = new SysDefDbcPortalVo();
            BeanUtils.copyProperties(def, portal);
            portal.setSrcDefId(def.getSrcDef().getSrcDefId());
            list.add(portal);
        }
        return list;
    }

	@Override
	public List<SysDefTree> querySubSysMenu(ObjectId srcDefId,String sbuId) {
		List<SysDefTree> defTrees = new ArrayList<>();
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefTypeConstants.MENU_DEF_ID));
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), srcDefId));
		conds.add(new DC_E(DFN.sysDef__parentDefId,null));
		conds.add(new DC_E(DFN.common_isValid, Boolean.TRUE));
		List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n()));
		Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n());
		for (TeSysDef teSysDef : teSysDefs) {
			SysDefTree tree = getSysDefTreeByParentId(teSysDef.getId(),sbuId,sort);
			defTrees.add(tree);
		}
		return defTrees;
	}

	@Override
	public List<TeSysDef> listTeSysDefByParent2SelfIds(List<ObjectId> parentIds, SysDefTypeCodeName defTypeCodeName) {
		if (CollectionUtils.isEmpty(parentIds)) {
			throw new BaseException("参数校验：parentIds为null或者为空");
		}
		if (defTypeCodeName == null) {
			throw new BaseException("参数校验：codeName为null");
		}
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(defTypeCodeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + defTypeCodeName.getValue());
		}
		List<TeSysDef> teSysDefs = sysDefDao.listTeSysDefsByParent2SelfIds(parentIds, defType.getId());
		if(teSysDefs == null){
			return new ArrayList<>();
		}
		return teSysDefs;
	}

	@Override
	public Parent2SelfPrdCtlgBO getParent2SelfPrdCtlgInfo(List<ObjectId> parentIds, List<ObjectId> srcDefIds) {
		return sysDefDao.getParent2SelfPrdCtlgInfo(parentIds, srcDefIds);
	}

	@Override
	public List<PmsValueItemVo> queryPmsValueItem() {
		//查询价值交付项
		List<TeSysDef> pmsValueItems = querySysDefs(SysDefConstants.PMS_VALUE_ITEM_DEFTYPE_ID, null, null);
		if (CollectionUtils.isEmpty(pmsValueItems)){
			return null;
		}
		List<ObjectId> pmsValueItemIds = pmsValueItems.stream().map(TeSysDef::getId).collect(Collectors.toList());
		//查询价值交付默认项
		List<TeSysDefCnfg> pmsValueDefaultItems = sysDefCnfgDao.getCnfgsByTypeSrcsFirstSecond(SysDefConstants.PMS_VALUE_ITEM_DEFAULT_PARA, pmsValueItemIds, null);
		//根据价值项划分数据
		Map<ObjectId, TeSysDefCnfg> pmsValueDefaultItemMap = null;
		if (CollectionUtils.isNotEmpty(pmsValueDefaultItems)){
			pmsValueDefaultItemMap = pmsValueDefaultItems.stream().collect(Collectors.toMap(cnfg -> cnfg.getSrcDef().getCid(), Function.identity(), (v1, v2) -> v2));
		}
		//遍历数据
		List<PmsValueItemVo> result = new ArrayList<>();
		for (TeSysDef sysDef : pmsValueItems){
			PmsValueItemVo vo = new PmsValueItemVo();
			vo.setId(sysDef.getId());
			//价值项
			vo.setValueItem(StringUtil.getNotNullStr(sysDef.getDefName()));
			List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
			//子项
			if (CollectionUtils.isNotEmpty(cndtItems)){
				for (TeIdNameCn cn : cndtItems){
					if ("type".equals(cn.getCodeName())){
						vo.setClassify(cn.getName());
					}
					if ("subType".equals(cn.getCodeName())){
						vo.setSubValueItem(cn.getName());
					}
				}
			}
			vo.setJudgeStand(StringUtil.getNotNullStr(sysDef.getDefDesc()));
			//价值交付默认值
			if (MapUtils.isNotEmpty(pmsValueDefaultItemMap) && pmsValueDefaultItemMap.get(sysDef.getId()) != null){
				TeSysDefCnfg teSysDefCnfg = pmsValueDefaultItemMap.get(sysDef.getId());
				vo.setQuantization(teSysDefCnfg.getFirstDef() == null ? null : teSysDefCnfg.getFirstDef().trans2IdName());
				vo.setJudgeStage(teSysDefCnfg.getFirstDef() == null ? null : teSysDefCnfg.getSecondDef().trans2IdName());
				vo.setJudgeRole(teSysDefCnfg.getFirstDef() == null ? null : teSysDefCnfg.getThirdDef().trans2IdName());
			}
			result.add(vo);
		}
		return result;
	}

	@Override
	public Map<ObjectId, String> getCtlg2FullPathMap(List<ObjectId> prdCtlgIdList) {
		Map<ObjectId, String> fullPathMap = new HashMap<>();

		Map<ObjectId, TeSysDef> id2SysDefMap = new HashMap<>();
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_I<>(DFN.common__id, prdCtlgIdList));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common__id);
		fieldNames.add(DFN.sysDef_parent2SelfIds);
		List<TeSysDef> prdCtlgDefs = sysDefDao.findByFieldAndConds(conds, fieldNames);
		Set<ObjectId> parent2SelfIdSet = new HashSet<>();
		for (TeSysDef sysDef : prdCtlgDefs) {
			id2SysDefMap.put(sysDef.getId(), sysDef);
			if (CollectionUtils.isEmpty(sysDef.getParent2SelfIds())) {
				continue;
			}
			parent2SelfIdSet.addAll(sysDef.getParent2SelfIds());
		}

		conds.clear();
		fieldNames.clear();
		conds.add(new DC_I<>(DFN.common__id, new ArrayList<>(parent2SelfIdSet)));
		fieldNames.add(DFN.common__id);
		fieldNames.add(DFN.sysDef_parent2SelfIds);
		fieldNames.add(DFN.sysDef__defName);
		List<TeSysDef> ancestorDefs = sysDefDao.findByFieldAndConds(conds, fieldNames);
		for (TeSysDef ancestorDef : ancestorDefs) {
			id2SysDefMap.put(ancestorDef.getId(), ancestorDef);
		}
		for (ObjectId prdCtlgId : prdCtlgIdList) {
			StringBuilder ctlgPath = new StringBuilder();
			TeSysDef curPrdCtlg = id2SysDefMap.get(prdCtlgId);
			if (curPrdCtlg == null || CollectionUtils.isEmpty(curPrdCtlg.getParent2SelfIds())) {
				continue;
			}
			for (ObjectId curPrdCtlgParent2SelfId : curPrdCtlg.getParent2SelfIds()) {
				TeSysDef parent2SelfCtlgDef = id2SysDefMap.get(curPrdCtlgParent2SelfId);
				ctlgPath.append(parent2SelfCtlgDef.getDefName());
				ctlgPath.append("/");
			}
			fullPathMap.put(prdCtlgId, ctlgPath.substring(0, ctlgPath.length() - 1));
		}
		return fullPathMap;
	}

	@Override
	public Integer queryMaxDefNoByDefTypeAndSrcDef(ObjectId defTypeId, ObjectId srcDefId) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), defTypeId));
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), srcDefId));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysDef__defNo);
		Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysDef__defNo.n());
		List<TeSysDef> defList = sysDefDao.findByFieldAndConds(conds, fieldNames, sort);
		if (CollectionUtils.isEmpty(defList)) {
			return 1;
		}
		Integer currentMaxNo = defList.get(0).getDefNo();
		if (currentMaxNo == null) {
			currentMaxNo = defList.size();
		}
		return  ++currentMaxNo;
	}

	@Override
	public List<TeSysDef> getSysDefByParas(List<ObjectId> paraDefIds) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid,true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId),SysDefConstants.Pfm_Msmt_Sys_Id));
		conds.add(new DC_I<ObjectId>(DFN.sysDef__paras.dot(DFN.sysCustDef_def.dot(DFN.common_cid)),paraDefIds));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DbFieldName.sysDef__defName);
		return sysDefDao.findByFieldAndConds(conds,fieldNames);
	}

	/**
	 * 根据名称和类型获取定义
	 * @param defName 定义名称
	 * @param codeName 定义类型
	 * @return 定义
	 */
	public TeSysDef getSysDefByNameAndType(String defName, SysDefTypeCodeName codeName) {
		TeSysDefType defType = sysDefTypeDao.getSysDefTypeByCodeName(codeName.getValue());
		if (defType == null) {
			throw new BaseException("定义类型不存在，codeName : " + codeName.getValue());
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__isValid, true));
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defType.getCodeName()));
		conds.add(new DC_E(DFN.sysDef__defName, defName));
		List<TeSysDef> sysDefs = sysDefDao.findByConds(conds,null);
		if (CollectionUtils.isEmpty(sysDefs)) {
			return null;
		}
		return sysDefs.get(0);
	}

	public List<SysDefCndtItemTreeVO> convertCndtItemTree(List<TeSysDef> defList, List<String> includeCndtItems) {
		Map<Integer, List<SysDefCndtItemTreeBO>> level2DefMap = new HashMap<>();
		Map<ObjectId, SysDefCndtItemTreeBO> id2TreeBoMap = new HashMap<>();
		
		// 第一步：构建层级映射和ID映射
		for (TeSysDef def : defList) {
			Integer level = getDefLevel(def.getCndtItems(), includeCndtItems);
			SysDefCndtItemTreeBO treeBo = new SysDefCndtItemTreeBO();
			treeBo.setDefNo(def.getDefNo());
			treeBo.setLevel(level);
			treeBo.setParentIds(getTreeParentId(def.getCndtItems(), includeCndtItems));
			treeBo.setId(def.getId());
			treeBo.setName(def.getDefName());
			
			List<SysDefCndtItemTreeBO> levelDefList = level2DefMap.computeIfAbsent(level, v -> new ArrayList<>());
			levelDefList.add(treeBo);
			id2TreeBoMap.put(def.getId(), treeBo);
		}
		
		// 第二步：找到所有根节点（没有父节点的节点）
		List<SysDefCndtItemTreeBO> rootNodes = findRootNodes(level2DefMap, id2TreeBoMap);
		
		// 第三步：为每个根节点构建独立的树
		List<SysDefCndtItemTreeVO> result = new ArrayList<>();
		for (SysDefCndtItemTreeBO rootNode : rootNodes) {
			SysDefCndtItemTreeVO rootTreeVo = buildTreeFromRoot(rootNode, level2DefMap, id2TreeBoMap);
			result.add(rootTreeVo);
		}
		// 按defNo排序
		return result.stream().sorted(Comparator.comparing(SysDefCndtItemTreeVO::getDefNo)).collect(Collectors.toList());
	}
	
	/**
	 * 找到所有根节点（没有父节点的节点）
	 */
	private List<SysDefCndtItemTreeBO> findRootNodes(Map<Integer, List<SysDefCndtItemTreeBO>> level2DefMap, 
													Map<ObjectId, SysDefCndtItemTreeBO> id2TreeBoMap) {
		List<SysDefCndtItemTreeBO> rootNodes = new ArrayList<>();
		Set<ObjectId> allIds = id2TreeBoMap.keySet();
		
		for (SysDefCndtItemTreeBO treeBo : id2TreeBoMap.values()) {
			List<ObjectId> parentIds = treeBo.getParentIds();
			boolean isRoot = true;
			
			// 检查是否有父节点在当前数据集中
			if (parentIds != null) {
				for (ObjectId parentId : parentIds) {
					if (parentId != null && allIds.contains(parentId)) {
						isRoot = false;
						break;
					}
				}
			}
			
			if (isRoot) {
				rootNodes.add(treeBo);
			}
		}
		
		return rootNodes;
	}
	
	/**
	 * 从根节点构建完整的树
	 */
	private SysDefCndtItemTreeVO buildTreeFromRoot(SysDefCndtItemTreeBO rootNode, 
												  Map<Integer, List<SysDefCndtItemTreeBO>> level2DefMap,
												  Map<ObjectId, SysDefCndtItemTreeBO> id2TreeBoMap) {
		SysDefCndtItemTreeVO rootTreeVo = new SysDefCndtItemTreeVO();
		rootTreeVo.setValue(rootNode.getId());
		rootTreeVo.setLabel(rootNode.getName());
		rootTreeVo.setTitle(rootNode.getName());
		rootTreeVo.setDefNo(rootNode.getDefNo());
		
		// 递归构建子节点
		List<SysDefCndtItemTreeVO> children = buildChildren(rootNode.getId(), level2DefMap, id2TreeBoMap);
		if (!children.isEmpty()) {
			rootTreeVo.setChildren(children);
		}
		
		return rootTreeVo;
	}
	
	/**
	 * 构建指定节点的子节点
	 */
	private List<SysDefCndtItemTreeVO> buildChildren(ObjectId parentId, 
													Map<Integer, List<SysDefCndtItemTreeBO>> level2DefMap,
													Map<ObjectId, SysDefCndtItemTreeBO> id2TreeBoMap) {
		List<SysDefCndtItemTreeVO> children = new ArrayList<>();
		
		// 获取当前父节点的层级
		SysDefCndtItemTreeBO parentNode = id2TreeBoMap.get(parentId);
		if (parentNode == null) {
			return children;
		}
		
		Integer parentLevel = parentNode.getLevel();
		Integer childLevel = parentLevel + 1;
		
		// 只查找下一层级的节点作为直接子节点
		List<SysDefCndtItemTreeBO> childLevelNodes = level2DefMap.get(childLevel);
		if (childLevelNodes != null) {
			for (SysDefCndtItemTreeBO node : childLevelNodes) {
				List<ObjectId> parentIds = node.getParentIds();
				if (parentIds != null && parentIds.contains(parentId)) {
					SysDefCndtItemTreeVO childTreeVo = new SysDefCndtItemTreeVO();
					childTreeVo.setValue(node.getId());
					childTreeVo.setLabel(node.getName());
					childTreeVo.setTitle(node.getName());
					childTreeVo.setDefNo(node.getDefNo());
					
					// 递归构建子节点的子节点
					List<SysDefCndtItemTreeVO> grandChildren = buildChildren(node.getId(), level2DefMap, id2TreeBoMap);
					if (!grandChildren.isEmpty()) {
						childTreeVo.setChildren(grandChildren);
					}
					
					children.add(childTreeVo);
				}
			}
		}
		
		// 按defNo排序
		return children.stream().sorted(Comparator.comparing(SysDefCndtItemTreeVO::getDefNo)).collect(Collectors.toList());
	}

	private List<ObjectId> getTreeParentId(List<TeIdNameCn> cndtItems, List<String> includeCndtItems) {
		if (CollectionUtils.isEmpty(cndtItems)) {
			return Collections.singletonList(null);
		}
		List<ObjectId> filterParentList = new ArrayList<>();
		for (TeIdNameCn cndtItem : cndtItems) {
			if (CollectionUtils.isEmpty(includeCndtItems)) {
				filterParentList.add(cndtItem.getCid());
			} else if (includeCndtItems.contains(cndtItem.getCodeName())) {
				filterParentList.add(cndtItem.getCid());
			}
		}
		if (CollectionUtils.isEmpty(filterParentList)) {
			return Collections.singletonList(null);
		}
		return filterParentList;
	}

	private Integer getDefLevel(List<TeIdNameCn> cndtItems, List<String> includeCndtItems) {
		if (CollectionUtils.isEmpty(cndtItems)) {
			return 0;
		}
		List<ObjectId> filterParentList= new ArrayList<>();
		for (TeIdNameCn cndtItem : cndtItems) {
			if (CollectionUtils.isEmpty(includeCndtItems)) {
				filterParentList.add(cndtItem.getCid());
			} else if (includeCndtItems.contains(cndtItem.getCodeName())) {
				filterParentList.add(cndtItem.getCid());
			}
		}
		return filterParentList.size();
	}

}
